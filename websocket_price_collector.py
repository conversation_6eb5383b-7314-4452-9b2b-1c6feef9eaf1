from concurrent.futures import ThreadPoolExecutor
import copy
import shutil
import sys
import websockets
import asyncio
import json
import logging
import pymysql
from datetime import datetime, timedelta, timezone
from pandas import Timestamp
import time
from config import *
import traceback
from websockets.server import serve
from db_config import DB_CONFIG
from strategy_analyzer import StrategyAnalyzer  # 导入策略分析器

import argparse

from okx_api_handler import okx_api



import os
import requests

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)






class WebSocketPriceCollector:


    def __init__(self, local_port=28765):
        """
        初始化WebSocket价格收集器

        参数:
            symbols (list): 要订阅的交易对列表，例如 ['DOGE-USDT']
            local_port (int): 本地WebSocket服务器端口
        """
        parser = argparse.ArgumentParser(description="")

        parser.add_argument("--currency", type=str, help="交易币种", default="DOGE")
        parser.add_argument("--buy_rate", type=float, help="买入阈值（用于下跌做空或上涨买入）", default=0.002)
        parser.add_argument("--sell_rate", type=float, help="卖出阈值（用于上涨平空或下跌平多）", default=0.002)
        parser.add_argument("--rest", type=int, help="交易间隔休息时间（分钟）", default=0)
        parser.add_argument("--min-trigger-rest", type=int, help="最小止损间隔时间（分钟）", default=0)
        parser.add_argument("--lookback-minutes-buy", type=int, help="买回看分钟数", default=2)
        parser.add_argument("--lookback-minutes-sell", type=int, help="卖回看分钟数", default=2)
        parser.add_argument("--initial-capital", type=int, help="初始资本", default=3)
        parser.add_argument("--is-live-trading", type=int, help="是否为实盘交易", default=1)
        parser.add_argument("--reverse-buy", type=int, help="是否反向买入", default=0)
        parser.add_argument("--new_params", type=int, help="新参数", default=1)
        
        parser.add_argument("--local-port", type=int, help="本地WebSocket服务器端口", default=28765)

        args = parser.parse_args()


        self.business_ws_url = "wss://ws.okx.com:8443/ws/v5/business"
        self.public_ws_url = "wss://ws.okx.com:8443/ws/v5/public"
        self.private_ws_url = "wss://ws.okx.com:8443/ws/v5/private"  # 私有频道
        self.local_port = args.local_port
        self.conn = None
        self.cached_data = {}
        self.local_clients = set()  # 存储本地连接的客户端
        self.initial_capital = args.initial_capital
        self.buy_rate = args.buy_rate
        self.sell_rate = args.sell_rate
        self.lookback_minutes_buy = args.lookback_minutes_buy
        self.lookback_minutes_sell = args.lookback_minutes_sell
        self.rest_minutes = args.rest
        self.min_trigger_rest = args.min_trigger_rest
        self.rest=args.rest
        self.is_live_trading=round(args.is_live_trading)
        self.strategy_report=[]
        self.params={}
        self.trade_count=0 #交易次数
        self.currency=args.currency
        self.order_book={}
        self.account_info={}
        self.active_connections = set()  # 跟踪活跃的WebSocket连接
        self.tradding_retry_count=0
        self.pre_params={}


        # 1. 创建交易执行器
        from trading_executor import TradingExecutor
        self.trading_executor = TradingExecutor(price_collector=self)

        # 2. 创建实盘交易策略分析器
        self.strategy_analyzer = StrategyAnalyzer(
            db_config=DB_CONFIG,
            trading_executor=self.trading_executor,
            price_collector=self
        )

        self.strategy_analyzer.save_row_reson = 1
        self.strategy_analyzer.save_trades = 1


        # 3. 创建模拟交易策略分析器
        self.strategy_analyzer_simulate = StrategyAnalyzer(
            db_config=DB_CONFIG
        )
        self.strategy_analyzer_simulate.save_row_reson = 1
        self.strategy_analyzer_simulate.save_trades = 1

        self.instrument = f"{self.currency}-USDT-SWAP"


        self.params = {}

        if self.is_live_trading==1:

            print_log("正在设置杠杆倍数...")
            leverage_result = okx_api.set_leverage(
                instId=self.instrument,
                lever='1',  # 1倍杠杆
                mgnMode='cross'  # 全仓模式
            )
            print_log(f"设置杠杆倍数结果: {leverage_result}")

            # 获取账户余额
            print_log("正在获取账户余额...")
            balance_result = okx_api.get_balance(ccy='USDT')
            if balance_result.get('code') == '0':
                balance_data = balance_result.get('data', [{}])[0]
                details = balance_data.get('details', [{}])[0]
                available_balance = float(details.get('availBal', 0))
                total_equity = float(details.get('eqUsd', 0))

                print_log(f"账户可用余额: {available_balance} USDT")
                print_log(f"账户总权益: {total_equity} USDT")

                # 更新初始资金和可用资金
                self.initial_capital = total_equity
            else:
                print_log(f"获取账户余额失败: {balance_result}")


        all_params = {
            'currency': self.currency,
            'buy_rate': self.buy_rate,
            'sell_rate': self.sell_rate,
            'rest': self.rest_minutes,
            'min_trigger_rest': self.min_trigger_rest,
            'lookback_minutes_buy': self.lookback_minutes_buy,
            'lookback_minutes_sell': self.lookback_minutes_sell,
            'is_live_trading': self.is_live_trading,
            'reverse_buy': args.reverse_buy
        }

        all_params_simulate = copy.deepcopy(all_params)
        all_params_simulate['is_live_trading'] = 0


        # 将所有参数的键名转换为下划线连接的格式
        formatted_params = '_'.join(f"{key}{value}" for key, value in all_params.items())
        formatted_params_simulate = '_'.join(f"{key}{value}" for key, value in all_params_simulate.items())

        self.params_filename = f'./params_cache_{formatted_params}.json'
        self.params_filename_simulate = f'./params_cache_{formatted_params_simulate}.json'

        print_log(f"参数文件名: {self.params_filename}")

        # 然后尝试读取（如果存在新缓存）
        if os.path.exists(self.params_filename) and args.new_params==0:
            with open(self.params_filename, 'r', encoding='utf-8') as file:
                self.params = json.load(file)


        else:
        # 如果本地缓存不存在，则初始化参数字典
            self.params = {
                'currency': self.currency,
                'position': "无仓",
                'entry_price': 0,
                'position_size': 0,
                'available_capital': float(self.initial_capital),
                'initial_capital': float(self.initial_capital),
                'total_fees': 0,
                'trades': [],
                'last_high': 0,
                'last_low': 9999,
                'total_profit': 0,
                'total_trades': 0,
                'successful_trades': 0,
                'trade_log': [],
                'profit': 0,
                'account_value': self.initial_capital,
                'current_position': 0,
                'last_position_price': 0,
                'currency': 'DOGE',  # 添加currency字段
                'buy_rate': self.buy_rate,
                'sell_rate': self.sell_rate,
                'size': 0,
                'rest_minutes': self.rest_minutes,
                'min_trigger_rest': self.min_trigger_rest,
                'last_close_time': None,
                'last_entry_time': None,
                'account_value_history': [],
                'highest_account_value': self.initial_capital,
                'lowest_account_value': self.initial_capital,
                'lookback_minutes_buy': self.lookback_minutes_buy,  # 添加连续K线判断的回看时间
                'lookback_minutes_sell': self.lookback_minutes_sell,  #
                'strategy_report': self.strategy_report,  # 添加策略报告列表
                'continuous_up_count': 0,  # 添加连续上涨计数器
                'continuous_down_count': 0,  # 添加连续下跌计数器
                'continuous_up_keep_count':0,
                'continuous_down_keep_count':0,
                'max_continuous_keep_count':10,
                'last_candle_high': float(0),  # 添加上一次K线最高价
                'last_candle_low': float(0),   # 添加上一次K线最低价
                'is_live_trading': self.is_live_trading,  # 添加实盘交易标志
                'trade_condition': {  # 添加默认结构
                    'reason': '初始化',
                    'trigger_price': 0
                },
                'order_book': {
                    'timestamp': None,
                    'bids': [],  # 格式: [[price, size], ...]
                    'asks': []   # 格式: [[price, size], ...]
                },
                'live_available_balance': 0.0,  # 可用保证金
                'live_total_equity': 0.0,       # 总资产
                'live_position_value': 0.0,     # 持仓价值
                'live_unrealized_pnl': 0.0,      # 未实现盈亏
                'min_trigger_rate':0.0004,
                'pre_row': None,
                'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),  # 添加开始时间
                'run_type': 'live',
                'reverse_buy': args.reverse_buy,  # 反向买入标志,达到上升条件. 反而做空
                'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'lock_time': time.time()  # 添加lock_time字段
            }



        # 然后尝试读取（如果存在新缓存）
        if os.path.exists(self.params_filename_simulate) and args.new_params==0:
            with open(self.params_filename_simulate, 'r', encoding='utf-8') as file:
                self.params_simulate = json.load(file)

        else:
            self.params_simulate = copy.deepcopy(self.params)
            
        self.params_simulate['run_type'] = 'analyze'
        self.params_simulate['is_live_trading'] = 0



        # 获取当前持仓状态
        print_log("正在获取持仓状态...")
        position_result = okx_api.get_positions(instId=self.instrument)
        if position_result.get('code') == '0':
            position_data = position_result.get('data', [{}])[0]
            # 处理pos为空字符串的情况
            pos_value = position_data.get('pos', 0)
            pos_value = 0 if pos_value == '' else float(pos_value)
            position_size = float(pos_value)*1000


            # 处理avgPx为空字符串的情况
            avg_px = position_data.get('avgPx', 0)
            avg_px = 0 if avg_px == '' else avg_px
            entry_price = float(avg_px)

            upl = position_data.get('upl', 0)
            upl = 0 if upl == '' else upl

            position_size_simulate=0
            if pos_value>0 and entry_price>0:
                position_size_simulate = self.initial_capital/entry_price
            

            

            print_log(f"当前持仓: {position_size}张 @ {entry_price}")

            # 更新持仓信息到params
            self.params.update({
                'position_size': position_size,
                'entry_price': entry_price,
                'position': '多仓' if position_data.get('posSide')=='long' and position_size>0 else '空仓' if position_data.get('posSide')=='short' and position_size>0 else '无仓',
                'live_position_value': abs(position_size * entry_price),
                'live_unrealized_pnl': float(upl),
                'available_capital':self.initial_capital,
                'current_position': -abs(position_size) if position_data.get('posSide')=='short' else abs(position_size),
                'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),  # 添加开始时间
            })

            # 更新持仓信息到params
            self.params_simulate.update({
                'position_size': position_size_simulate,
                'entry_price': entry_price,
                'position':  '多仓' if position_data.get('posSide')=='long' and position_size>0 else '空仓' if position_data.get('posSide')=='short' and position_size>0 else '无仓',
                'live_position_value': abs(position_size * entry_price),
                'live_unrealized_pnl': float(upl),
                'available_capital':self.initial_capital,
                'current_position': -abs(position_size) if position_data.get('posSide')=='short' else abs(position_size),
                'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),  # 添加开始时间
            })


        else:
            print_log(f"获取持仓状态失败: {position_result}")



        print_log("初始化参数:")
        print_log(self.params)

        # 在初始化时添加合约验证(占)
        #self._validate_instrument()

        # 添加连接状态跟踪
        self.connections = {
            'public': None,
            'business': None,
            'private': None
        }

        # 添加订阅状态跟踪
        self.subscription_status = {
            'public': False,
            'business': False,
            'private': False
        }

        # 添加最后接收消息时间跟踪
        self.last_message_time = {
            'public': 0,
            'business': 0,
            'private': 0
        }





    def _validate_instrument(self):
        """增强版合约验证方法"""
        print_log(f"开始验证合约: {self.instrument}")
        result = okx_api.get_instruments(instType="SWAP")
        print_log(f"API返回原始数据: {json.dumps(result, indent=2)}")  # 打印原始响应

        # 检查基础响应结构
        if not isinstance(result, dict) or 'code' not in result:
            raise ValueError(f"无效的API响应格式: {result}")

        # 使用字符串比较状态码
        if result.get('code') != 0:
            error_data = result.get('data', [{}])[0] if result.get('data') else {}
            raise ValueError(
                f"获取合约列表失败: {result.get('msg', '未知错误')}\n"
                f"错误代码: {error_data.get('sCode', '无')}\n"
                f"错误信息: {error_data.get('sMsg', '无')}"
            )

        # 检查数据字段是否存在
        if 'data' not in result or not isinstance(result['data'], list):
            raise ValueError("API响应缺少有效数据字段")

        # 安全提取instrument ID
        valid_instruments = []
        for inst in result['data']:
            if not isinstance(inst, dict):
                continue
            inst_id = inst.get('instrument_id')
            if inst_id and inst.get('state') == 'live':  # 只考虑可用合约
                valid_instruments.append(inst_id)

        # 添加找不到合约时的调试信息
        if self.instrument not in valid_instruments:
            similar = [inst for inst in valid_instruments
                      if inst.startswith(self.currency.upper())]

            # 生成调试信息
            debug_info = {
                "请求合约": self.instrument,
                "可用合约数量": len(valid_instruments),
                "前5个可用合约": valid_instruments[:5],
                "类似合约建议": similar[:3] if similar else "无"
            }

            raise ValueError(
                f"合约 {self.instrument} 验证失败\n"
                f"{json.dumps(debug_info, indent=2, ensure_ascii=False)}"
            )

        print_log(f"合约验证通过: {self.instrument}")

    def get_db_connection(self):
        """获取数据库连接"""
        try:
            if self.conn is None or not self.conn.open:
                self.conn = pymysql.connect(
                    host=MYSQL_HOST,
                    user=MYSQL_USER,
                    password=MYSQL_PASSWORD,
                    database=MYSQL_DATABASE,
                    port=MYSQL_PORT
                )
            return self.conn
        except Exception as e:
            print_log(f"数据库连接失败: {str(e)}")
            raise

    def save_to_database(self, data):
        """保存数据到数据库"""
        try:
            conn = self.get_db_connection()
            with conn.cursor() as cursor:
                # 首先检查是否已存在相同时间戳的数据
                check_sql = """
                    SELECT COUNT(*) FROM crypto_prices
                    WHERE currency = %s AND timestamp = %s
                """
                cursor.execute(check_sql, (data['currency'], data['timestamp'].split('+')[0]))
                count = cursor.fetchone()[0]

                if count > 0:
                    print_log(f"数据已存在，跳过保存: {data['currency']} - {data['timestamp'].split('+')[0]}")
                    return

                sql = """
                    INSERT INTO crypto_prices (
                        currency, timestamp, open_price, high_price,
                        low_price, close_price, volume, volume_currency
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s
                    )
                """
                cursor.execute(sql, (
                    data['currency'],
                    data['timestamp'].split('+')[0],
                    data['open'],
                    data['high'],
                    data['low'],
                    data['close'],
                    data['volume'],
                    data['volume_currency']
                ))
                conn.commit()
                print_log(f"成功保存数据: {data['currency']} - {data['timestamp'].split('+')[0]}")
        except Exception as e:
            print_log(f"保存数据失败: {str(e)}")
            if conn:
                conn.rollback()

    async def handle_local_client(self, websocket):
        """处理本地客户端连接"""
        try:
            self.local_clients.add(websocket)
            print_log(f"新客户端连接，当前连接数: {len(self.local_clients)}")

            # 保持连接直到客户端断开
            async for message in websocket:
                try:
                    # 处理客户端消息（如果需要）
                    pass
                except Exception as e:
                    print_log(f"处理客户端消息时出错: {str(e)}")

        except websockets.exceptions.ConnectionClosed:
            print_log("客户端连接已关闭")
        finally:
            self.local_clients.remove(websocket)
            print_log(f"客户端断开连接，当前连接数: {len(self.local_clients)}")

    async def broadcast_to_local_clients(self, data):
        """向所有本地客户端广播数据"""
        if not self.local_clients:
            return

        message = json.dumps(data)
        disconnected_clients = set()

        for client in self.local_clients:
            try:
                await client.send(message)
            except websockets.exceptions.ConnectionClosed:
                disconnected_clients.add(client)
            except Exception as e:
                print_log(f"发送数据到客户端失败: {str(e)}")
                disconnected_clients.add(client)

        # 移除断开连接的客户端
        self.local_clients -= disconnected_clients

    async def process_candle_data(self, data):
        """处理K线数据"""
        try:
            # 新增订单数据处理
            if data.get('arg', {}).get('channel') == 'orders':
                await self.process_order_data(data)
                return

            if data.get('event') == 'error':
                print_log(f"WebSocket错误: {data}")
                return

            if data.get('event'):
                print_log(f"WebSocket事件: {data}")
                return

            if not data.get('data'):
                return

            for candle in data['data']:
                if len(candle) < 9:
                    print_log(f"无效的K线数据格式: {candle}")
                    continue

                inst_id = data.get('arg', {}).get('instId', '')
                if not inst_id:
                    print_log("未找到交易对信息")
                    continue

                currency = inst_id.split('-')[0]
                # 原始时间戳是毫秒，转换为秒
                timestamp_seconds = float(candle[0])/1000
                # 使用fromtimestamp处理，加上时区
                timestamp = datetime.fromtimestamp(timestamp_seconds, timezone.utc)
                # 加8小时以调整时区
                timestamp = timestamp + timedelta(hours=8)

                timestamp=timestamp.isoformat()

                candle_data = {
                    'currency': currency,
                    'timestamp': timestamp,
                    'open': float(candle[1]),
                    'high': float(candle[2]),
                    'low': float(candle[3]),
                    'close': float(candle[4]),
                    'volume': float(candle[5]),
                    'volume_currency': float(candle[6]),
                    'is_complete': candle[8] == '1'
                }

                # 记录秒级行情数据
                with open(f'log/sec_candle/{os.getpid()}.log', 'a') as f:
                    f.write('|'.join(str(v) for v in candle_data.values()) + '\n')
                

                # 广播给本地客户端
                await self.broadcast_to_local_clients({
                    'type': 'kline',
                    'data': candle_data
                })

                self.params['row_close']=0;

                # 如果是一分钟的结束，保存到数据库
                if candle[8] == '1':

                    print_log(f"保存完整K线数据: {inst_id} - {timestamp}")
                    self.save_to_database(candle_data)
                    # 直接在params中设置lock_time
                    self.params['lock_time'] = time.time()
                    self.params['row_close'] = 1

                    #执行一次模拟策略
                    self.params_simulate =  self.strategy_analyzer_simulate.process_trade_logic(candle_data, self.params_simulate)

                try:
                    self.trade_count = len(self.params['trades']) if 'trades' in self.params else 0

                        # 调用交易逻辑处理方法
                    self.params =  self.strategy_analyzer.process_trade_logic(candle_data, self.params)

                    print_log(f"实盘数据: {candle_data['close']:.5f} {self.params['account_value']:.4f} 升{self.params['continuous_up_count']} 跌{self.params['continuous_down_count']} 理由: {self.params['trade_condition'].get('reason', '无原因')}")
                    await self.broadcast_to_local_clients({
                        'type': 'trade_condition',
                        'data': {
                            'reason': self.params['trade_condition'].get('reason', '无原因'),
                            'trigger_price': self.params['trade_condition'].get('trigger_price', 0)
                        }
                    })

                    await self.broadcast_to_local_clients({
                        'type': 'account_value',
                        'data': {
                            'timestamp': candle_data['timestamp'],  # 使用K线时间戳
                            'account_value': self.params['account_value']
                        }
                    })

                    if candle[8] == '1':
                        # 将日志信息封装成字典
                            row_reson_dict = {
                                "timestamp": str(candle_data['timestamp']),
                                "close": candle_data['close'],
                                "last_high": self.params['last_high'],
                                "last_low": self.params['last_low'],
                                "continuous_up_count": self.params['continuous_up_count'],
                                "continuous_down_count": self.params['continuous_down_count'],
                                "trade_condition": self.params['trade_condition']['reason']
                            }

                            # 将模拟日志信息封装成字典
                            row_reson_dict_simulate = {
                                "timestamp": str(candle_data['timestamp']),
                                "close": candle_data['close'],
                                "last_high": self.params_simulate['last_high'],
                                "last_low": self.params_simulate['last_low'],
                                "continuous_up_count": self.params_simulate['continuous_up_count'],
                                "continuous_down_count": self.params_simulate['continuous_down_count'],
                                "trade_condition": self.params_simulate['trade_condition']['reason']
                            }

                            print_log(f"模拟数据: {candle_data['close']:.5f} {self.params_simulate['account_value']:.4f} 升{self.params_simulate['continuous_up_count']} 跌{self.params_simulate['continuous_down_count']} 理由: {self.params_simulate['trade_condition'].get('reason', '无原因')}")

                            # 将日志字典添加到列表中
                            self.strategy_analyzer.row_reson_list[str(candle_data['timestamp'])] = row_reson_dict
                            self.strategy_analyzer_simulate.row_reson_list[str(candle_data['timestamp'])] = row_reson_dict_simulate

                    with open(self.params_filename, 'w', encoding='utf-8') as file:
                        json.dump(self.params, file, ensure_ascii=False, indent=4)


                    with open(self.params_filename_simulate, 'w', encoding='utf-8') as file:
                        json.dump(self.params, file, ensure_ascii=False, indent=4)

                    #如果有新增交易
                    if len(self.params['trades']) > self.trade_count:



                        current_time = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
                        file_name = f"./log/params_cache_{current_time}.json"
                        with open(file_name, 'w' , encoding='utf-8') as file:
                            json.dump(self.params, file, ensure_ascii=False, indent=4)

                        # 广播交易本地客户端
                        await self.broadcast_to_local_clients({
                            'type': 'trade',
                            'data': self.params['trades'][-1]
                        })

                                # 广播完整仪表盘数据
                    await self.broadcast_to_local_clients({
                        'type': 'dashboard',
                        'data': {
                            'account_value': self.params['account_value'],
                            'position_status': self.params['position'],
                            'trigger_price': self.params.get('min_trigger_price', 0),
                            'total_profit': self.params['total_profit'],
                            'total_fees': self.params['total_fees'],
                            'trade_count': len(self.params['trades']),
                            'initial_capital': self.params['initial_capital'],
                            'timestamp': time.time(),
                            'live_available_balance': self.params['live_available_balance'],
                            'live_total_equity': self.params['live_total_equity'],
                            'live_position_value': self.params['live_position_value'],
                            'live_unrealized_pnl': self.params['live_unrealized_pnl']
                        }
                    })

                except Exception as e:
                    print_log(f"处理交易逻辑时发生错误: {str(e)} \n{traceback.format_exc()}")




        except Exception as e:
            print_log(f"处理数据失败: {str(e)}\n{traceback.format_exc()}")

    async def process_order_data(self, data):

        if self.is_live_trading == 0:
            return

        """改进版订单数据处理"""
        try:
            order_info = data.get('data', [{}])[0]
            # 添加字段验证
            if not order_info.get('ordId'):
                print_log("收到无效订单数据")
                return

            # 打印前检查必要字段
            required_fields = ['ordId', 'instId', 'state']
            if not all(field in order_info for field in required_fields):
                print_log(f"订单数据缺少必要字段: {order_info}")
                return

            order_id = order_info.get('ordId')
            order_state = order_info.get('state')

            # 创建一个新的字典来存储需要的订单信息，避免循环引用
            order_data = order_info

            with open('./order_info.json', 'a') as file:
                file.write(f"{datetime.now().isoformat()}: ")
                json.dump(order_data, file, ensure_ascii=False, indent=4)

            # 打印订单信息
            print_log("\n=== 订单状态更新 ===")
            print_log(f"订单ID: {order_data['ordId']}")
            print_log(f"交易对: {order_data['instId']}")
            print_log(f"订单状态: {order_data['state']}")
            print_log(f"订单类型: {order_data['ordType']}")
            print_log(f"方向: {order_data['side']} {order_data['posSide']}")
            print_log(f"价格: {order_data['px']}")
            print_log(f"数量: {order_data['sz']}")
            print_log(f"成交数量: {order_data['fillSz']}")
            print_log(f"成交均价: {order_data['avgPx']}")
            print_log(f"更新时间: {datetime.fromtimestamp(int(order_data['uTime'] or '0')/1000)}")
            print_log("="*50)

            # 更新交易记录，只保存必要的信息.(不记录订单信息)
            #if len(self.params['trades']) > 0:
            #    self.params['trades'][-1]['live'] = order_data

            # 更新订单状态到交易执行器
            if order_id in self.trading_executor.pending_orders:
                self.trading_executor.pending_orders[order_id]['state'] = order_state

                # 处理已撤销订单
                if order_state == 'canceled':
                    #只撤销订单不再下单
                    self.trading_executor.revert_trade_status(order_id)
                    self.tradding_retry_count+=1
                    print_log(f"订单{order_id}已撤销，重试次数: {self.tradding_retry_count}")
                    # 重置lock_time
                    self.params['lock_time'] = 0

                if order_state == 'filled':
                    await self.trading_executor.handle_filled_order(order_id, order_data)
                    self.tradding_retry_count=0

            # 广播给本地客户端
            await self.broadcast_to_local_clients({
                'type': 'order',
                'data': {
                    'order_id': order_data['ordId'],
                    'status': order_data['state'],
                    'price': order_data['px'],
                    'amount': order_data['sz'],
                    'filled': order_data['fillSz'],
                    'timestamp': order_data['uTime']
                }
            })

        except Exception as e:
            print_log(f"处理订单数据失败: {str(e)} \n{traceback.format_exc()}")

    async def process_order_book(self, data):
        """异步处理订单簿数据（存储完整档位）"""
        try:
            book_data = data.get('data', [{}])[0]

            # 存储原始买卖盘数据
            self.params['order_book'] = {
                'timestamp': datetime.now().isoformat(),
                'bids': [[float(p[0]), float(p[1])] for p in book_data.get('bids', [])],  # [价格, 数量]
                'asks': [[float(p[0]), float(p[1])] for p in book_data.get('asks', [])]   # [价格, 数量]
            }

            return


            # 打印统计信息（避免打印全部数据）
            print_log(f"\n订单簿更新 买盘档位: {len(self.order_book['bids'])} 卖盘档位: {len(self.order_book['asks'])}")
            if len(self.order_book['bids']) > 0:
                print_log(f"买一价: {self.order_book['bids'][0][0]} 数量: {self.order_book['bids'][0][1]}")
            if len(self.order_book['asks']) > 0:
                print_log(f"卖一价: {self.order_book['asks'][0][0]} 数量: {self.order_book['asks'][0][1]}")

            # 广播完整数据
            await self.broadcast_to_local_clients({
                'type': 'order_book',
                'data': self.order_book
            })

        except Exception as e:
            print_log(f"处理订单簿失败: {str(e)}")

    async def process_account_data(self, data):
        """改进版账户数据处理"""
        try:

            if self.account_info == data.get('data', [{}])[0]['details'][0]:
                return

            self.account_info = data.get('data', [{}])[0]['details'][0]

            # 添加字段验证和默认值处理
            def safe_float(value, default=0.0):
                try:
                    return float(value) if value not in ['', None] else default
                except (ValueError, TypeError):
                    return default

            # 更新实时账户信息
            self.params.update({
                'live_available_balance': safe_float(self.account_info.get('availBal')),
                'live_available_capital':safe_float(self.account_info.get('frozenBal'))+safe_float(self.account_info.get('availBal')),
                'live_total_equity': safe_float(self.account_info.get('eqUsd')),
                'live_position_value': safe_float(self.account_info.get('frozenBal')),
                'live_unrealized_pnl': safe_float(self.account_info.get('upl'))
            })
            return

            print_log("\n=== 账户更新 ===")
            print_log(f"可用保证金: {self.params['live_available_balance']:.4f}")
            print_log(f"总资产: {self.params['live_total_equity']:.4f}  ({self.params['account_value']:.4f})")
            print_log(f"持仓价值: {self.params['live_position_value']:.4f}")
            print_log(f"未实现盈亏: {self.params['live_unrealized_pnl']:.4f} ({self.params['total_profit']-self.params['total_fees']:.4f})")



            # 广播给本地客户端
            await self.broadcast_to_local_clients({
                'type': 'account',
                'data': {
                    'available': self.params['live_available_balance'],
                    'equity': self.params['live_total_equity'],
                    'position_value': self.params['live_position_value'],
                    'unrealized_pnl': self.params['live_unrealized_pnl']
                }
            })


        except Exception as e:
            print_log(f"处理账户数据失败: {str(e)}\n原始数据: {data}")

    async def process_position_data(self, data):
        """处理持仓数据更新"""
        try:
            position_info = data.get('data', [{}])[0]

            # 定义safe_float函数处理空字符串和None值
            def safe_float(value, default=0.0):
                try:
                    return float(value) if value not in ['', None] else default
                except (ValueError, TypeError):
                    return default

            pos_value = safe_float(position_info.get('pos', 0))
            entry_price = safe_float(position_info.get('avgPx', 0))

            # 更新持仓信息到params
            self.params.update({
                'position_size': pos_value,
                'entry_price': entry_price,
                'position': '多仓' if pos_value > 0 else '空仓' if pos_value < 0 else '无仓'
            })
            print_log(f"持仓更新: {self.params['position']} {self.params['position_size']}张 @ {self.params['entry_price']}")

            # 广播给本地客户端
            await self.broadcast_to_local_clients({
                'type': 'position',
                'data': {
                    'size': self.params['position_size'],
                    'price': self.params['entry_price'],
                    'side': self.params['position']
                }
            })
        except Exception as e:
            print_log(f"处理持仓数据失败: {str(e)}")

    async def process_data(self, data):
        """通用数据分发方法"""
        channel = data.get('arg', {}).get('channel')

        # 使用字典映射不同频道的处理方法
        handlers = {
            'candle1m': self.process_candle_data,  # 频道名称改为candle
            'books5': self.process_order_book,
            'orders': self.process_order_data,
            'account': self.process_account_data,
            'positions': self.process_position_data
        }

        if handler := handlers.get(channel):
            # 为每个频道创建独立任务
            asyncio.create_task(handler(data))
        else:
            print_log(f"收到未处理频道数据: {channel}")  # 改为debug级别日志

    async def monitor_connection_health(self):
        """监控所有连接的健康状态"""
        while True:
            try:
                current_time = time.time()

                # 检查每个连接的状态
                for conn_type, ws in self.connections.items():
                    if ws is None:
                        # 连接不存在，需要建立连接
                        await self.establish_connection(conn_type)
                        continue

                    # 检查最后消息时间，如果超过30秒没有消息，认为连接可能有问题
                    if current_time - self.last_message_time[conn_type] > 30:
                        print_log(f"{conn_type} 连接可能已断开，尝试重新连接")
                        await self.establish_connection(conn_type)

                await asyncio.sleep(5)  # 每5秒检查一次

            except Exception as e:
                print_log(f"监控连接健康状态时出错: {str(e)}")
                await asyncio.sleep(5)

    async def establish_connection(self, conn_type):
        """建立指定类型的连接"""
        try:
            # 如果已有连接，先关闭
            if self.connections[conn_type]:
                try:
                    await self.connections[conn_type].close()
                except:
                    pass
                self.connections[conn_type] = None
                self.subscription_status[conn_type] = False

            # 检查是否已经在重连过程中
            if hasattr(self, '_reconnecting') and self._reconnecting.get(conn_type, False):
                print_log(f"{conn_type} 正在重连中，跳过重复重连")
                return None

            self._reconnecting = getattr(self, '_reconnecting', {})
            self._reconnecting[conn_type] = True

            try:
                # 根据类型建立新连接
                if conn_type == 'public':
                    ws = await websockets.connect(self.public_ws_url)
                    ws.conn_type = 'public'
                    await self.subscribe_public_channels(ws)

                elif conn_type == 'business':
                    ws = await websockets.connect(self.business_ws_url)
                    ws.conn_type = 'business'
                    await self.subscribe_business_channels(ws)

                elif conn_type == 'private':
                    ws = await websockets.connect(self.private_ws_url)
                    ws.conn_type = 'private'
                    await self.subscribe_private_channels(ws)

                self.connections[conn_type] = ws
                asyncio.create_task(self.handle_ws_messages(ws))

                return ws

            finally:
                self._reconnecting[conn_type] = False

        except Exception as e:
            print_log(f"建立 {conn_type} 连接时出错: {str(e)}")
            self.connections[conn_type] = None
            self.subscription_status[conn_type] = False
            await asyncio.sleep(5)
            return await self.establish_connection(conn_type)

    async def subscribe_business_channels(self, ws):
        """订阅业务频道"""
        try:
            if not self.subscription_status['business']:
                print_log("正在订阅K线数据频道...")
                await ws.send(json.dumps({
                    "op": "subscribe",
                    "args": [{"channel": "candle1m", "instId": self.instrument}]
                }))
                self.subscription_status['business'] = True
                print_log("已订阅K线数据频道")
            else:
                print_log("K线数据频道已订阅，跳过重复订阅")
        except Exception as e:
            print_log(f"订阅K线数据频道失败: {str(e)}")
            self.subscription_status['business'] = False
            raise

    async def subscribe_public_channels(self, ws):
        """订阅公共频道"""
        if not self.subscription_status['public']:
            await ws.send(json.dumps({
                "op": "subscribe",
                "args": [{"channel": "books5", "instId": self.instrument}]
            }))
            self.subscription_status['public'] = True
            print_log("已订阅订单簿频道")

    async def subscribe_private_channels(self, ws):
        """订阅私有频道"""
        if not self.subscription_status['private']:
            # 先进行登录
            timestamp = str(int(time.time()))
            sign = okx_api.generate_signature(timestamp + 'GET' + '/users/self/verify')
            login_payload = {
                "op": "login",
                "args": [{
                    "apiKey": API_KEY,
                    "passphrase": API_PASSPHRASE,
                    "timestamp": timestamp,
                    "sign": sign
                }]
            }
            await ws.send(json.dumps(login_payload))

            # 等待登录响应
            login_response = await ws.recv()
            login_data = json.loads(login_response)

            if login_data.get('event') == 'login' and login_data.get('code') == '0':
                await ws.send(json.dumps({
                    "op": "subscribe",
                    "args": [
                        {"channel": "orders", "instType": "ANY", "instId": self.instrument},
                        {"channel": "account"}
                    ]
                }))
                self.subscription_status['private'] = True
                print_log("已订阅私有频道")
            else:
                print_log("私有频道登录失败")
                raise Exception("私有频道登录失败")

    async def handle_ws_messages(self, ws):
        """处理WebSocket消息"""
        try:
            async for message in ws:
                # 更新最后消息时间
                self.last_message_time[ws.conn_type] = time.time()

                data = json.loads(message)

                # 处理订阅确认消息
                if data.get('event') == 'subscribe':
                    print_log(f"订阅确认: {data}")
                    continue

                # 处理错误消息
                if data.get('event') == 'error':
                    print_log(f"WebSocket错误: {data}")
                    # 触发重连
                    await self.establish_connection(ws.conn_type)
                    return

                # 处理正常消息
                await self.process_data(data)

        except websockets.ConnectionClosed:
            print_log(f"{ws.conn_type} 连接已关闭")
            self.subscription_status[ws.conn_type] = False
            await self.establish_connection(ws.conn_type)
        except Exception as e:
            print_log(f"处理消息时出错: {str(e)}")
            self.subscription_status[ws.conn_type] = False
            await self.establish_connection(ws.conn_type)

    async def send_heartbeat(self):
        for conn_type, ws in self.connections.items():
            if ws is not None:  # 只向有效的连接发送心跳包
                await ws.send(json.dumps({"op": "ping"}))  # 发送心跳包
                await asyncio.sleep(30)  # 每30秒发送一次心跳包

    async def start_server(self):
        """启动WebSocket服务器"""
        try:
            async with serve(self.handle_local_client, "localhost", self.local_port) as server:
                print_log(f"本地WebSocket服务器启动在 ws://localhost:{self.local_port}")

                # 启动连接监控
                monitor_task = asyncio.create_task(self.monitor_connection_health())
                heartbeat_task = asyncio.create_task(self.send_heartbeat())
                # 建立初始连接
                for conn_type in ['public', 'business', 'private']:
                    await self.establish_connection(conn_type)

                await asyncio.Future()  # 保持服务器运行

        except Exception as e:
            print_log(f"服务器错误: {str(e)} \n{traceback.format_exc()}")
            await asyncio.sleep(5)

    def start(self):
        """启动WebSocket订阅和本地服务器"""
        try:
            # 检查数据库连接
            try:
                self.get_db_connection()
            except Exception as e:
                print_log(f"数据库连接失败: {str(e)}\n{traceback.format_exc()}")
                sys.exit()

            # 启动事件循环
            asyncio.run(self.start_server())

        except KeyboardInterrupt:

            print_log("程序被用户中断")
            # 在程序被中断时保存策略结果

            print_log("正在保存策略结果...")
            try:

                print_log("="*80)
                strategy_id = self.strategy_analyzer.save_strategy_results(self.params)
                print_log(f"实盘策略结果已保存，策略ID: {strategy_id}")
                print_log("-"*80)
                strategy_id_simulate = self.strategy_analyzer_simulate.save_strategy_results(self.params_simulate)
                print_log(f"模拟策略结果已保存，策略ID: {strategy_id_simulate}")
                print_log("-"*80)

                # 检查并移动策略日志文件
                try:
                    pid = os.getpid()
                    # 确保文件句柄已关闭，防止"另一个程序正在使用此文件"错误
                    if hasattr(print_log, 'file_handles'):
                        for filename, handle in print_log.file_handles.items():
                            try:
                                handle.close()
                                print_log(f"已关闭文件句柄: {filename}")
                            except Exception as e:
                                print_log(f"关闭文件句柄出错: {str(e)}")
                    
                    # 等待文件释放
                    time.sleep(1)
                    
                    live_log_file = f"params_log_{pid}_live.txt"
                    normal_log_file = f"params_log_{pid}.txt"
                    
                    # 创建目标目录
                    log_dir = os.path.join(".", "log", "strategy")
                    os.makedirs(log_dir, exist_ok=True)
                    
                    # 检查并移动实盘日志文件
                    if os.path.exists(live_log_file):
                        target_path = os.path.join(log_dir, f"strategy_line_{strategy_id}_live_1.log")
                        shutil.move(live_log_file, target_path)
                        print_log(f"已移动实盘策略日志到: {target_path}")
                    
                    # 检查并移动模拟日志文件
                    if os.path.exists(normal_log_file):
                        target_path = os.path.join(log_dir, f"strategy_line_{strategy_id_simulate}_live_0.log")
                        shutil.move(normal_log_file, target_path)
                        print_log(f"已移动模拟策略日志到: {target_path}")

                    # 删除旧文件
                    try:
                        # 删除实盘日志文件
                        if os.path.exists(live_log_file) and os.path.exists(os.path.join(log_dir, f"strategy_line_{strategy_id}_live_1.log")):
                            os.remove(live_log_file)
                            print_log(f"已删除原实盘策略日志文件: {live_log_file}")
                        
                        # 删除模拟日志文件
                        if os.path.exists(normal_log_file) and os.path.exists(os.path.join(log_dir, f"strategy_line_{strategy_id_simulate}_live_0.log")):
                            os.remove(normal_log_file)
                            print_log(f"已删除原模拟策略日志文件: {normal_log_file}")


                    except Exception as delete_error:
                        print_log(f"删除旧日志文件时出错: {str(delete_error)}")


                except Exception as move_error:
                    print_log(f"移动策略日志文件时出错: {str(move_error)}")

            except Exception as save_error:
                print_log(f"保存策略结果时出错: {str(save_error)}\n{traceback.format_exc()}")
                traceback.print_exc()

        except Exception as e:
            print_log(f"程序运行错误: {str(e)}\n{traceback.format_exc()}")
        finally:
            if self.conn:
                self.conn.close()

if __name__ == "__main__":

    # 创建并启动收集器
    collector = WebSocketPriceCollector()
    collector.start()

    # # 查看第一个合约信息
    # response = requests.get("https://www.okx.com/api/v5/public/instruments?instType=SWAP").json()
    # print_log(response['data'][0])  # 查看第一个合约信息

    # 测试实时交易


    #trade_result = collector.strategy_analyzer.execute_trade(price=2, available_amount=2, action='BUY', position_type='long', is_live_trading=True,currency=collector.params.get('currency', 'DOGE'))
    #print_log(f"执行交易结果: {trade_result}")



