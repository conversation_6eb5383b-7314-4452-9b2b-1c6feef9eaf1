#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 price_adjust_rate=0 是否能正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def test_price_adjust_rate_zero():
    """测试 price_adjust_rate=0 的情况"""
    print("🧪 测试 price_adjust_rate=0 的情况")
    print("=" * 50)
    
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 测试参数 - 设置 price_adjust_rate=0
    test_params = {
        'current_position': 0,
        'last_close_time': None,
        'rest_minutes': 0,
        'lookback_minutes_buy': 2,
        'lookback_minutes_sell': 2,
        'buy_rate': 0.2,
        'sell_rate': 0.015,
        'price_adjust_rate': 0,  # 设置为0
        'minute_candles': []
    }
    
    # 模拟K线数据 - 做空场景
    candles_data = [
        # 上涨期
        {'timestamp': '2025-03-03T08:41:00+00:00', 'open': 0.23650, 'close': 0.23680, 'high': 0.23690, 'low': 0.23640, 'is_up': True},
        {'timestamp': '2025-03-03T08:42:00+00:00', 'open': 0.23680, 'close': 0.23710, 'high': 0.23720, 'low': 0.23670, 'is_up': True},
        # 下跌期
        {'timestamp': '2025-03-03T08:43:00+00:00', 'open': 0.23710, 'close': 0.23680, 'high': 0.23720, 'low': 0.23670, 'is_up': False},
        {'timestamp': '2025-03-03T08:44:00+00:00', 'open': 0.23680, 'close': 0.23650, 'high': 0.23690, 'low': 0.23640, 'is_up': False},
        # 当前K线 - 继续下跌，触发价格超出范围，需要保护
        {'timestamp': '2025-03-03T08:45:00+00:00', 'open': 0.23677, 'close': 0.23650, 'high': 0.23700, 'low': 0.23617, 'is_up': False}
    ]
    
    test_params['minute_candles'] = candles_data
    
    # 构造当前K线数据
    current_row = {
        'timestamp': '2025-03-03T08:45:00+00:00',
        'open': 0.23677,
        'close': 0.23650,
        'high': 0.23700,
        'low': 0.23617,
        'is_complete': 1
    }
    
    print(f"当前K线：开盘{current_row['open']:.5f} 最高{current_row['high']:.5f} 最低{current_row['low']:.5f}")
    print(f"price_adjust_rate: {test_params['price_adjust_rate']} (设置为0)")
    print()
    
    # 测试交易条件
    result = analyzer.trade_condition_new(current_row, test_params)
    
    if result and result['action']:
        print(f"✅ 交易信号：{result['action']}")
        print(f"📝 交易原因：{result['reason']}")
        print(f"💰 触发价格：{result['trigger_price']:.5f}")
        
        if 'actual_trigger_price' in result:
            print(f"💰 实际触发价：{result['actual_trigger_price']:.5f}")
            
            # 当 price_adjust_rate=0 时，保护价格应该等于开盘价
            expected_protection_price = current_row['open']  # 开盘价 + 0% = 开盘价
            actual_protection_price = result['actual_trigger_price']
            
            print(f"🔍 保护价格验证 (price_adjust_rate=0)：")
            print(f"   期望保护价：{expected_protection_price:.5f} (开盘价+0%)")
            print(f"   实际保护价：{actual_protection_price:.5f}")
            
            if abs(actual_protection_price - expected_protection_price) < 0.00001:
                print("   ✅ 保护价格正确：price_adjust_rate=0 时使用开盘价")
            else:
                print("   ❌ 保护价格错误")
                
            # 验证价格在K线范围内
            if current_row['low'] <= actual_protection_price <= current_row['high']:
                print("   ✅ 保护价格在K线范围内")
            else:
                print("   ❌ 保护价格超出K线范围")
        
    else:
        print("❌ 未产生交易信号")
        if result:
            print(f"📝 原因：{result['reason']}")
    
    print()
    print("=" * 50)
    
    # 测试平仓场景
    print("📊 测试平仓场景：price_adjust_rate=0")
    
    # 修改为空仓，测试平空
    test_params['current_position'] = -404.72
    
    # 模拟K线数据 - 平空场景：先跌后涨
    candles_data_close = [
        # 下跌期
        {'timestamp': '2025-03-03T07:56:00+00:00', 'open': 0.23950, 'close': 0.23930, 'high': 0.23960, 'low': 0.23920, 'is_up': False},
        {'timestamp': '2025-03-03T07:57:00+00:00', 'open': 0.23930, 'close': 0.23910, 'high': 0.23940, 'low': 0.23900, 'is_up': False},
        # 上涨期
        {'timestamp': '2025-03-03T07:58:00+00:00', 'open': 0.23910, 'close': 0.23930, 'high': 0.23940, 'low': 0.23905, 'is_up': True},
        {'timestamp': '2025-03-03T07:59:00+00:00', 'open': 0.23930, 'close': 0.23950, 'high': 0.23960, 'low': 0.23925, 'is_up': True},
        # 当前K线 - 继续上涨，触发平空
        {'timestamp': '2025-03-03T08:00:00+00:00', 'open': 0.23931, 'close': 0.23950, 'high': 0.24060, 'low': 0.23908, 'is_up': True}
    ]
    
    test_params['minute_candles'] = candles_data_close
    
    # 构造当前K线数据
    current_row_close = {
        'timestamp': '2025-03-03T08:00:00+00:00',
        'open': 0.23931,
        'close': 0.23950,
        'high': 0.24060,
        'low': 0.23908,
        'is_complete': 1
    }
    
    print(f"当前仓位：{test_params['current_position']:.2f} (空仓)")
    print(f"当前K线：开盘{current_row_close['open']:.5f} 最高{current_row_close['high']:.5f} 最低{current_row_close['low']:.5f}")
    print()
    
    # 测试交易条件
    result_close = analyzer.trade_condition_new(current_row_close, test_params)
    
    if result_close and result_close['action']:
        print(f"✅ 交易信号：{result_close['action']}")
        print(f"📝 交易原因：{result_close['reason']}")
        print(f"💰 触发价格：{result_close['trigger_price']:.5f}")
        
        if 'actual_trigger_price' in result_close:
            print(f"💰 实际触发价：{result_close['actual_trigger_price']:.5f}")
            
            # 当 price_adjust_rate=0 时，平空保护价格应该等于开盘价
            expected_protection_price = current_row_close['open']  # 开盘价 + 0% = 开盘价
            actual_protection_price = result_close['actual_trigger_price']
            
            print(f"🔍 平空保护价格验证 (price_adjust_rate=0)：")
            print(f"   期望保护价：{expected_protection_price:.5f} (开盘价+0%)")
            print(f"   实际保护价：{actual_protection_price:.5f}")
            
            if abs(actual_protection_price - expected_protection_price) < 0.00001:
                print("   ✅ 保护价格正确：price_adjust_rate=0 时使用开盘价")
            else:
                print("   ❌ 保护价格错误")
        
    else:
        print("❌ 未产生交易信号")
        if result_close:
            print(f"📝 原因：{result_close['reason']}")
    
    print()
    print("🎯 测试总结：")
    print("- price_adjust_rate=0 时，保护价格应该等于开盘价")
    print("- 不再有额外的价格调整")
    print("✅ price_adjust_rate=0 测试完成")

if __name__ == "__main__":
    test_price_adjust_rate_zero()
