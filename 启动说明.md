# 策略分析应用启动说明

## 功能说明

本应用在启动时会自动检查网络连通性：
1. 首先尝试ping Google来检测网络连通性
2. 如果无法连接到Google，会自动启动qv2ray代理软件
3. 代理软件路径：`C:\Program Files\qv2ray\qv2ray.exe`

## 启动方式

### 方式一：使用启动器（推荐）
双击运行 `start_app.bat` 文件，会显示菜单：
1. 启动应用（默认）
2. 测试网络连通性
3. 退出

该启动器会：
- 自动检查Python环境
- 激活虚拟环境（如果存在）
- 安装必要的依赖包
- 启动Flask应用

### 方式二：快速启动
双击运行 `quick_start.bat` 文件，直接启动应用，跳过菜单选择。

### 方式三：网络测试
双击运行 `test_network.py` 文件，单独测试网络连通性和qv2ray启动功能。

### 方式四：代理检测
双击运行 `proxy_helper.py` 文件，详细检测和测试代理设置：
- 检测系统代理配置
- 测试直连和代理连接
- 扫描常见代理端口
- 提供连接建议

### 方式五：手动启动
1. 打开命令提示符
2. 切换到项目目录
3. 运行以下命令：
```bash
python app.py
```

## 访问地址

应用启动后，可以通过以下地址访问：
- 本地访问：http://localhost:5000
- 网络访问：http://0.0.0.0:5000

## 依赖要求

- Python 3.8+
- 所需包已列在 `requirements.txt` 中
- MySQL数据库（需要配置config.py中的数据库连接信息）

## 代理软件要求

如果需要自动启动代理功能，请确保：
1. qv2ray已安装在默认路径：`C:\Program Files\qv2ray\qv2ray.exe`
2. 如果安装在其他路径，请修改app.py中的qv2ray_path变量

## 停止应用

在命令行窗口中按 `Ctrl+C` 即可停止应用

## 故障排除

### 常见问题

1. **Python未找到**：确保Python已正确安装并添加到PATH环境变量
2. **依赖包安装失败**：手动运行 `pip install -r requirements.txt`
3. **数据库连接失败**：检查config.py中的数据库配置
4. **qv2ray启动失败**：检查qv2ray是否正确安装在指定路径

### 网络连接问题

5. **代理启动后仍无法连接Google**：
   - 运行代理检测工具：`python proxy_helper.py`
   - 检查qv2ray代理配置是否正确
   - 确认系统代理设置已生效
   - 检查防火墙是否阻止连接

6. **Python无法使用系统代理**：
   - 重启应用让Python重新读取代理设置
   - 手动配置代理环境变量：
     ```bash
     set HTTP_PROXY=http://127.0.0.1:端口号
     set HTTPS_PROXY=http://127.0.0.1:端口号
     ```

7. **代理软件冲突**：
   - 确保只运行一个代理软件
   - 检查端口是否被占用
   - 重启代理软件

### 诊断工具

- **网络测试**：`python test_network.py`
- **代理诊断**：`python proxy_helper.py`
- **启动器菜单**：运行 `start_app.bat` 选择相应选项
