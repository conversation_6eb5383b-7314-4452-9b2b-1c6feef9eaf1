#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试精简交易原因格式的脚本
"""

def format_concise_reason(action, trigger_price, rate, consecutive_count, ref_time, ref_candle=None):
    """生成精简的交易原因"""
    # 精简词汇映射
    action_map = {
        '开多': '多开', '开空': '空开', 
        '平多': '多平', '平空': '空平',
        '做多触发': '多开', '做空触发': '空开',
        '平多触发': '多平', '平空触发': '空平'
    }
    
    short_action = action_map.get(action, action)
    time_str = ref_time[-8:] if ref_time else ''  # 只取时分秒
    
    if ref_candle:
        # 包含K线信息的版本 (开-高-低格式)
        return f'{short_action}:连{consecutive_count}次,率{rate},价{trigger_price:.5f},参考{time_str}({ref_candle["open"]:.5f}-{ref_candle["high"]:.5f}-{ref_candle["low"]:.5f})'
    else:
        # 简化版本
        return f'{short_action}:连{consecutive_count}次,率{rate},价{trigger_price:.5f},时{time_str}'

def test_concise_reasons():
    """测试精简原因格式"""
    
    print("🔍 测试精简交易原因格式")
    print("="*60)
    
    # 模拟K线数据
    ref_candle = {
        "timestamp": "2025-07-25 21:48:48",
        "open": 0.14828,
        "high": 0.14865,
        "low": 0.14824,
        "close": 0.14855
    }
    
    # 测试用例
    test_cases = [
        {
            'name': '平多触发',
            'action': '平多',
            'trigger_price': 0.14857,
            'rate': 0.2,
            'consecutive_count': 2,
            'ref_time': '2025-07-25 21:48:48',
            'ref_candle': ref_candle
        },
        {
            'name': '平空触发',
            'action': '平空',
            'trigger_price': 0.14835,
            'rate': 0.2,
            'consecutive_count': 2,
            'ref_time': '2025-07-25 21:47:00',
            'ref_candle': ref_candle
        },
        {
            'name': '做多触发',
            'action': '做多触发',
            'trigger_price': 0.14840,
            'rate': 0.2,
            'consecutive_count': '2跌2涨',
            'ref_time': '2025-07-25 21:46:00',
            'ref_candle': ref_candle
        },
        {
            'name': '做空触发',
            'action': '做空触发',
            'trigger_price': 0.14850,
            'rate': 0.2,
            'consecutive_count': '2涨2跌',
            'ref_time': '2025-07-25 21:45:00',
            'ref_candle': ref_candle
        }
    ]
    
    print("📊 原始格式 vs 精简格式对比:")
    print("-"*60)
    
    for case in test_cases:
        print(f"\n🔸 {case['name']}:")
        
        # 生成精简原因
        concise_reason = format_concise_reason(
            case['action'],
            case['trigger_price'],
            case['rate'],
            case['consecutive_count'],
            case['ref_time'],
            case['ref_candle']
        )
        
        # 模拟原始冗长格式
        if case['action'] == '平多':
            original_reason = f"""平多触发
条件：连续{case['consecutive_count']}次下跌
倍率：sell_rate={case['rate']}
参考K线：{case['ref_time'][-8:]}(开:{case['ref_candle']['open']:.5f},高:{case['ref_candle']['high']:.5f},低:{case['ref_candle']['low']:.5f})
价格范围：最高价-最低价={case['ref_candle']['high']:.5f}-{case['ref_candle']['low']:.5f}={case['ref_candle']['high']-case['ref_candle']['low']:.5f}
计算公式：最高价-价格范围×sell_rate
触发价格：{case['ref_candle']['high']:.5f}-{case['ref_candle']['high']-case['ref_candle']['low']:.5f}×{case['rate']}={case['trigger_price']:.5f}"""
        else:
            original_reason = f"原始{case['action']}的冗长描述..."
        
        print(f"  原始长度: {len(original_reason)} 字符")
        print(f"  精简长度: {len(concise_reason)} 字符")
        print(f"  压缩比例: {len(concise_reason)/len(original_reason)*100:.1f}%")
        print(f"  精简原因: {concise_reason}")
        
        # 检查是否超过数据库限制
        status = "✅ 符合" if len(concise_reason) <= 255 else "❌ 超长"
        print(f"  数据库兼容: {status}")

def test_edge_cases():
    """测试边界情况"""
    
    print("\n🧪 边界情况测试")
    print("="*60)
    
    # 测试不同参数组合
    edge_cases = [
        {
            'name': '无K线数据',
            'action': '多开',
            'trigger_price': 0.14857,
            'rate': 1.0,
            'consecutive_count': 5,
            'ref_time': '2025-07-25 21:48:48',
            'ref_candle': None
        },
        {
            'name': '高精度价格',
            'action': '空平',
            'trigger_price': 0.148576789,
            'rate': 0.025,
            'consecutive_count': 10,
            'ref_time': '2025-07-25 21:48:48',
            'ref_candle': {
                "timestamp": "2025-07-25 21:48:48",
                "open": 0.148281234,
                "high": 0.148657890,
                "low": 0.148241111,
                "close": 0.148551234
            }
        },
        {
            'name': '复杂条件',
            'action': '做多触发',
            'trigger_price': 0.14857,
            'rate': 0.333,
            'consecutive_count': '5跌3涨',
            'ref_time': '2025-07-25 21:48:48',
            'ref_candle': {
                "timestamp": "2025-07-25 21:48:48",
                "open": 0.14828,
                "high": 0.14865,
                "low": 0.14824,
                "close": 0.14855
            }
        }
    ]
    
    for case in edge_cases:
        print(f"\n🔸 {case['name']}:")
        
        concise_reason = format_concise_reason(
            case['action'],
            case['trigger_price'],
            case['rate'],
            case['consecutive_count'],
            case['ref_time'],
            case['ref_candle']
        )
        
        print(f"  结果: {concise_reason}")
        print(f"  长度: {len(concise_reason)} 字符")
        
        status = "✅ 符合" if len(concise_reason) <= 255 else "❌ 超长"
        print(f"  数据库兼容: {status}")

def test_compression_ratio():
    """测试压缩比例"""
    
    print("\n📊 压缩效果统计")
    print("="*60)
    
    # 模拟典型的原始原因
    original_reasons = [
        """平多触发
条件：连续2次下跌
倍率：sell_rate=0.2
参考K线：21:48:48(开:0.14828,高:0.14865,低:0.14824)
价格范围：最高价-最低价=0.14865-0.14824=0.00041
计算公式：最高价-价格范围×sell_rate
计算结果：0.14865-0.00041×0.2=0.14857
价格保护：触发价格0.14857>开盘价0.14828
实际触发：直接以开盘价0.14828触发""",
        
        """做多触发
条件：先跌2次，再涨2次
倍率：buy_rate=0.2
参考K线：21:47:00(开:0.14820,高:0.14860,低:0.14815)
价格范围：最高价-最低价=0.14860-0.14815=0.00045
计算公式：最低价+价格范围×buy_rate
触发价格：0.14815+0.00045×0.2=0.14824""",
        
        """平空触发
条件：连续2次上涨
倍率：sell_rate=0.2
参考K线：21:46:00(开:0.14825,高:0.14870,低:0.14820)
价格范围：最高价-最低价=0.14870-0.14820=0.00050
计算公式：最低价+价格范围×sell_rate
触发价格：0.14820+0.00050×0.2=0.14830"""
    ]
    
    # 对应的精简版本
    concise_reasons = [
        "多平:连2次,率0.2,价0.14857,参考21:48:48(0.14828-0.14865-0.14824)",
        "多开:连2跌2涨次,率0.2,价0.14824,参考21:47:00(0.14820-0.14860-0.14815)",
        "空平:连2次,率0.2,价0.14830,参考21:46:00(0.14825-0.14870-0.14820)"
    ]
    
    total_original = 0
    total_concise = 0
    
    for i, (original, concise) in enumerate(zip(original_reasons, concise_reasons), 1):
        original_len = len(original)
        concise_len = len(concise)
        compression_ratio = concise_len / original_len * 100
        
        total_original += original_len
        total_concise += concise_len
        
        print(f"示例 {i}:")
        print(f"  原始: {original_len} 字符")
        print(f"  精简: {concise_len} 字符")
        print(f"  压缩比: {compression_ratio:.1f}%")
        print(f"  节省: {original_len - concise_len} 字符")
    
    overall_compression = total_concise / total_original * 100
    total_saved = total_original - total_concise
    
    print(f"\n📈 总体统计:")
    print(f"  原始总长度: {total_original} 字符")
    print(f"  精简总长度: {total_concise} 字符")
    print(f"  总体压缩比: {overall_compression:.1f}%")
    print(f"  总共节省: {total_saved} 字符")
    print(f"  平均每条节省: {total_saved/len(original_reasons):.0f} 字符")

if __name__ == "__main__":
    print("🧪 精简交易原因格式测试")
    print("="*70)
    
    # 测试基本功能
    test_concise_reasons()
    
    # 测试边界情况
    test_edge_cases()
    
    # 测试压缩效果
    test_compression_ratio()
    
    print("\n" + "="*70)
    print("✅ 所有测试完成！")
    
    print("\n💡 精简效果:")
    print("1. 平均压缩比约20-30%")
    print("2. 所有原因都在255字符限制内")
    print("3. 保留了关键信息：动作、条件、倍率、价格、参考时间")
    print("4. 使用简化词汇：多开/多平/空开/空平")
    print("5. K线格式：时间(开-高-低)")
    
    print("\n🎯 词汇映射:")
    print("- 平多触发 → 多平")
    print("- 平空触发 → 空平") 
    print("- 做多触发 → 多开")
    print("- 做空触发 → 空开")
    print("- 连续N次下跌 → 连N次")
    print("- sell_rate/buy_rate → 率")
    print("- 触发价格 → 价")
    print("- 参考K线时间 → 参考时分秒")
