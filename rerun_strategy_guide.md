# 策略重新运行功能使用指南

## 功能概述

`rerun_strategies` 方法现在支持指定新的时间范围来重新运行已有的策略。这个功能允许你：

1. 使用相同的策略参数但不同的时间范围重新分析
2. 只修改开始时间或结束时间
3. 保持原有时间范围重新运行

## 方法签名

```python
def rerun_strategies(self, strategy_ids, new_start_time=None, new_end_time=None):
    """
    根据指定的策略ID重新执行策略分析

    Args:
        strategy_ids: 单个策略ID或策略ID列表
        new_start_time: 可选，新的开始时间（格式：'YYYY-MM-DD HH:MM:SS'）
        new_end_time: 可选，新的结束时间（格式：'YYYY-MM-DD HH:MM:SS'）

    Returns:
        list: 重新执行后的策略ID列表
    """
```

## 使用方式

### 1. 命令行使用

#### 基本语法
```bash
python strategy_analyzer.py --rerun <策略ID> [--rerun-start <开始时间>] [--rerun-end <结束时间>]
```

#### 示例

**重新运行策略并指定新的完整时间范围：**
```bash
python strategy_analyzer.py --rerun 1,2,3 --rerun-start "2024-01-01 00:00:00" --rerun-end "2024-01-31 23:59:59"
```

**只修改开始时间：**
```bash
python strategy_analyzer.py --rerun 1 --rerun-start "2024-02-01 00:00:00"
```

**只修改结束时间：**
```bash
python strategy_analyzer.py --rerun 1 --rerun-end "2024-03-31 23:59:59"
```

**使用原有时间范围重新运行：**
```bash
python strategy_analyzer.py --rerun 1,2,3
```

### 2. Python代码使用

```python
from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

# 创建分析器实例
analyzer = StrategyAnalyzer(DB_CONFIG)

# 示例1: 重新运行策略并指定新的时间范围
new_strategy_ids = analyzer.rerun_strategies(
    strategy_ids=[1, 2, 3],
    new_start_time="2024-01-01 00:00:00",
    new_end_time="2024-01-31 23:59:59"
)

# 示例2: 只修改开始时间
new_strategy_ids = analyzer.rerun_strategies(
    strategy_ids=1,
    new_start_time="2024-02-01 00:00:00",
    new_end_time=None
)

# 示例3: 只修改结束时间
new_strategy_ids = analyzer.rerun_strategies(
    strategy_ids=[1],
    new_start_time=None,
    new_end_time="2024-03-31 23:59:59"
)

# 示例4: 使用原有时间范围
new_strategy_ids = analyzer.rerun_strategies(strategy_ids=[1, 2, 3])
```

## 参数说明

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `strategy_ids` | int/str/list | 是 | 要重新运行的策略ID，可以是单个ID或ID列表 |
| `new_start_time` | str | 否 | 新的开始时间，格式：'YYYY-MM-DD HH:MM:SS' |
| `new_end_time` | str | 否 | 新的结束时间，格式：'YYYY-MM-DD HH:MM:SS' |

### 命令行参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `--rerun` | 要重新运行的策略ID，多个ID用逗号分隔 | `--rerun 1,2,3` |
| `--rerun-start` | 新的开始时间（可选） | `--rerun-start "2024-01-01 00:00:00"` |
| `--rerun-end` | 新的结束时间（可选） | `--rerun-end "2024-01-31 23:59:59"` |

## 工作流程

1. **读取原策略参数**：从数据库中获取指定策略ID的所有参数
2. **应用新时间**：如果提供了新的时间参数，则使用新时间；否则使用原有时间
3. **重新分析**：使用策略参数和时间范围重新执行策略分析
4. **保存结果**：将新的分析结果保存为新的策略记录
5. **返回新ID**：返回新创建的策略ID列表

## 注意事项

1. **时间格式**：时间必须使用 'YYYY-MM-DD HH:MM:SS' 格式
2. **策略ID**：确保提供的策略ID在数据库中存在
3. **新策略**：重新运行会创建新的策略记录，不会覆盖原有策略
4. **参数保持**：除了时间范围外，所有其他策略参数（buy_rate、sell_rate等）保持不变
5. **日志记录**：所有操作都会记录详细的日志信息

## 使用场景

1. **时间范围扩展**：将策略应用到更长的时间范围
2. **时间窗口移动**：将策略应用到不同的时间窗口
3. **性能对比**：比较同一策略在不同时间段的表现
4. **参数验证**：验证策略在不同市场条件下的稳定性

## 错误处理

如果重新运行过程中出现错误，系统会：
1. 记录详细的错误日志
2. 回滚数据库事务
3. 返回错误信息
4. 不影响原有策略数据

## 测试

使用提供的测试脚本 `test_rerun_with_time.py` 来测试功能：

```bash
python test_rerun_with_time.py
```

该脚本包含了各种使用场景的示例代码。
