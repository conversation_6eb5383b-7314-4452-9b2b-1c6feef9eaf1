#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
详细的策略测试用例 - 逐分钟分析
按照指定参数模拟运行策略，详细说明每分钟的数据情况
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def analyze_minute_by_minute():
    """逐分钟分析策略执行情况"""
    
    # 测试参数
    params = {
        'start_time': "2025-04-10 00:30:00",
        'end_time': "2025-04-10 00:59:00", 
        'sell_rate': 1,
        'buy_rate': 1,
        'rest_minutes': 0,
        'min_trigger_rest': 30,
        'lookback_minutes_buy': 2,
        'lookback_minutes_sell': 5,
        'currency': 'DOGE'
    }
    
    print("=== 策略参数 ===")
    print(f"时间范围: {params['start_time']} 到 {params['end_time']}")
    print(f"lookback_minutes_buy: {params['lookback_minutes_buy']} (反弹连续次数)")
    print(f"lookback_minutes_sell: {params['lookback_minutes_sell']} (回调连续次数)")
    print(f"做多条件: 先跌{params['lookback_minutes_sell']}次 → 再涨{params['lookback_minutes_buy']}次")
    print(f"做空条件: 先涨{params['lookback_minutes_sell']}次 → 再跌{params['lookback_minutes_buy']}次")
    print(f"buy_rate: {params['buy_rate']} (做多触发倍率)")
    print(f"sell_rate: {params['sell_rate']} (做空触发倍率)")
    print(f"币种: {params['currency']}")
    print()
    
    try:
        # 创建策略分析器
        analyzer = StrategyAnalyzer(DB_CONFIG)
        
        # 获取价格数据
        start_time = datetime.fromisoformat(params['start_time'])
        end_time = datetime.fromisoformat(params['end_time'])
        prices = analyzer.get_price_data(start_time, end_time, params['currency'])
        
        if not prices:
            print("错误: 没有找到价格数据")
            return
        
        print(f"获取到 {len(prices)} 条价格数据")
        print()
        
        # 初始化策略参数
        strategy_params = {
            'current_position': 0,
            'last_close_time': None,
            'rest_minutes': params['rest_minutes'],
            'lookback_minutes_buy': params['lookback_minutes_buy'],
            'lookback_minutes_sell': params['lookback_minutes_sell'],
            'buy_rate': params['buy_rate'],
            'sell_rate': params['sell_rate'],
            'minute_candles': []
        }
        
        print("=== 逐分钟分析 ===")
        print("时间                | 开盘   | 最高   | 最低   | 收盘   | 涨跌 | 历史K线数 | 持仓状态 | 策略判断")
        print("-" * 120)
        
        for i, row in enumerate(prices[:30]):  # 只分析前30分钟
            # 🎯 设置为完整K线以测试双重判断逻辑
            row['is_complete'] = 1

            # 🎯 关键：调用 strategy_analyzer.py 中的 trade_condition_new 方法
            trade_result = analyzer.trade_condition_new(row, strategy_params)

            # 获取当前K线信息
            timestamp = row['timestamp']
            open_price = float(row['open'])
            close_price = float(row['close'])
            high_price = float(row['high'])
            low_price = float(row['low'])

            # 判断涨跌
            if len(strategy_params['minute_candles']) > 0:
                current_candle = strategy_params['minute_candles'][-1]
                is_up_str = "涨" if current_candle['is_up'] else "跌"
            else:
                is_up_str = "涨" if close_price > open_price else "跌"

            # 策略判断结果
            action = trade_result.get('action', None) if trade_result else None
            reason = trade_result.get('reason', '无操作') if trade_result else '无操作'
            trigger_price = trade_result.get('trigger_price', 0) if trade_result else 0

            # 🎯 模拟交易执行：如果37分开空，更新持仓状态
            if timestamp == '2025-04-10T00:37:00' and action == '开空':
                strategy_params['current_position'] = -1  # 设置为空仓
                print(f"  🔥 执行开空操作，持仓状态更新为: {strategy_params['current_position']}")

            # 输出当前分钟信息
            position_str = f"持仓:{strategy_params['current_position']}" if strategy_params['current_position'] != 0 else "无仓"
            print(f"{timestamp} | {open_price:6.4f} | {high_price:6.4f} | {low_price:6.4f} | {close_price:6.4f} | {is_up_str:2s} | {len(strategy_params['minute_candles']):8d} | {position_str} | {action or '无'}: {reason}")

            # 如果有交易信号，显示触发价格
            if action and action != '无':
                print(f"  🎯 触发价格: {trigger_price:.4f}")

            # 详细分析前10分钟的情况
            if i < 10:
                print(f"  详细分析:")
                analyze_detailed_condition_from_strategy(analyzer, strategy_params, row, params)
                print()
        
        print()
        print("=== 分析总结 ===")
        print(f"总共分析了 {min(60, len(prices))} 分钟的数据")
        print(f"策略需要至少 {params['lookback_minutes_buy'] + params['lookback_minutes_sell']} 分钟历史数据才能开始判断")
        
    except Exception as e:
        print(f"分析过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

def analyze_detailed_condition_from_strategy(analyzer, strategy_params, current_row, params):
    """通过调用策略分析器的方法来详细分析当前分钟的策略条件"""

    candles = strategy_params['minute_candles']
    lookback_buy = params['lookback_minutes_buy']
    lookback_sell = params['lookback_minutes_sell']
    buy_rate = params['buy_rate']
    current_high = float(current_row['high'])
    current_low = float(current_row['low'])

    print(f"    当前持仓: {strategy_params['current_position']}")
    print(f"    历史K线数: {len(candles)}")
    print(f"    策略要求: 做多(先跌{lookback_sell}次→再涨{lookback_buy}次), 做空(先涨{lookback_sell}次→再跌{lookback_buy}次)")

    # 🎯 直接调用策略分析器的方法来测试做多条件
    print(f"    === 做多条件测试 ===")
    try:
        long_result = analyzer._check_long_condition(
            strategy_params, lookback_buy, lookback_sell, buy_rate, current_high
        )
        if long_result:
            print(f"    ✅ 做多条件满足: {long_result['reason']}")
            print(f"    触发价格: {long_result['trigger_price']:.4f}")
        else:
            print(f"    ❌ 做多条件不满足")

            # 详细分析为什么不满足
            if len(candles) < lookback_buy:
                print(f"      原因: 历史数据不足，需要至少{lookback_buy}条上涨数据")
            elif len(candles) >= lookback_buy:
                recent_up = candles[-lookback_buy:]
                all_up = all(candle['is_up'] for candle in recent_up)
                print(f"      最近{lookback_buy}分钟: {[('涨' if c['is_up'] else '跌') for c in recent_up]}")
                print(f"      连续上涨: {'是' if all_up else '否'}")

                if all_up and len(candles) >= lookback_buy + lookback_sell:
                    start_idx = len(candles) - lookback_buy
                    down_period = candles[start_idx - lookback_sell:start_idx]
                    all_down = all(not candle['is_up'] for candle in down_period)
                    print(f"      之前{lookback_sell}分钟: {[('涨' if c['is_up'] else '跌') for c in down_period]}")
                    print(f"      连续下跌: {'是' if all_down else '否'}")

                    if all_down:
                        lowest_candle = min(down_period, key=lambda x: x['low'])
                        trigger_price = lowest_candle['high'] if buy_rate == 1.0 else lowest_candle['low'] + (lowest_candle['high'] - lowest_candle['low']) * buy_rate
                        print(f"      触发价格: {trigger_price:.4f}, 当前最高: {current_high:.4f}")
                        print(f"      价格达标: {'是' if current_high > trigger_price else '否'}")
    except Exception as e:
        print(f"    ❌ 做多条件测试出错: {str(e)}")

    # 🎯 直接调用策略分析器的方法来测试做空条件
    print(f"    === 做空条件测试 ===")
    try:
        short_result = analyzer._check_short_condition(
            strategy_params, lookback_buy, lookback_sell, buy_rate, current_low
        )
        if short_result:
            print(f"    ✅ 做空条件满足: {short_result['reason']}")
            print(f"    触发价格: {short_result['trigger_price']:.4f}")
        else:
            print(f"    ❌ 做空条件不满足")

            # 详细分析为什么不满足
            if len(candles) < lookback_buy:
                print(f"      原因: 历史数据不足，需要至少{lookback_buy}条下跌数据")
            elif len(candles) >= lookback_buy:
                recent_down = candles[-lookback_buy:]
                all_down = all(not candle['is_up'] for candle in recent_down)
                print(f"      最近{lookback_buy}分钟: {[('涨' if c['is_up'] else '跌') for c in recent_down]}")
                print(f"      连续下跌: {'是' if all_down else '否'}")

                if all_down and len(candles) >= lookback_buy + lookback_sell:
                    start_idx = len(candles) - lookback_buy
                    up_period = candles[start_idx - lookback_sell:start_idx]
                    all_up = all(candle['is_up'] for candle in up_period)
                    print(f"      之前{lookback_sell}分钟: {[('涨' if c['is_up'] else '跌') for c in up_period]}")
                    print(f"      连续上涨: {'是' if all_up else '否'}")

                    if all_up:
                        highest_candle = max(up_period, key=lambda x: x['high'])
                        trigger_price = highest_candle['low'] if buy_rate == 1.0 else highest_candle['high'] - (highest_candle['high'] - highest_candle['low']) * buy_rate
                        print(f"      触发价格: {trigger_price:.4f}, 当前最低: {current_low:.4f}")
                        print(f"      价格达标: {'是' if current_low < trigger_price else '否'}")
    except Exception as e:
        print(f"    ❌ 做空条件测试出错: {str(e)}")

if __name__ == "__main__":
    analyze_minute_by_minute()
