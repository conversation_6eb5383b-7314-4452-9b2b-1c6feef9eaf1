# 分析交易数据
trades = [
    # 第一笔交易：做空开仓
    {
        'time': '2025-03-01 00:45:00',
        'open': 0.19899, 'close': 0.19852, 'high': 0.19925, 'low': 0.19851,
        'trigger_price': 0.19974, 'actual_trigger_price': 0.19925, 'protection_price': 0.19899,
        'position': -500.40553
    },
    # 第二笔交易：平空
    {
        'time': '2025-03-01 01:11:00', 
        'open': 0.19901, 'close': 0.19930, 'high': 0.19944, 'low': 0.19885,
        'trigger_price': 0.19821, 'actual_trigger_price': 0.19885, 'protection_price': 0.19901,
        'position': 0
    },
    # 第三笔交易：做多开仓
    {
        'time': '2025-03-01 01:47:00',
        'open': 0.19823, 'close': 0.19848, 'high': 0.19851, 'low': 0.19798,
        'trigger_price': 0.19806, 'actual_trigger_price': 0.19843, 'protection_price': 0.19823,
        'position': 508.00101
    }
]

print('=== 交易价格分析 ===')
print()

for i, trade in enumerate(trades, 1):
    print(f'第{i}笔交易 - {trade["time"]}')
    print(f'K线范围: [{trade["low"]:.5f}, {trade["high"]:.5f}]')
    print(f'开盘价: {trade["open"]:.5f}')
    print(f'收盘价: {trade["close"]:.5f}')
    print(f'触发价: {trade["trigger_price"]:.5f}')
    print(f'实际触发价: {trade["actual_trigger_price"]:.5f}')
    print(f'保护价: {trade["protection_price"]:.5f}')
    print(f'仓位: {trade["position"]:.5f}')
    print()
    
    # 检查价格是否在K线范围内
    low, high = trade['low'], trade['high']
    trigger_in_range = low <= trade['trigger_price'] <= high
    actual_in_range = low <= trade['actual_trigger_price'] <= high
    protection_in_range = low <= trade['protection_price'] <= high
    
    print('价格检查:')
    status1 = '✅' if trigger_in_range else '❌'
    status2 = '✅' if actual_in_range else '❌'
    status3 = '✅' if protection_in_range else '❌'
    
    print(f'  触发价在K线范围内: {trigger_in_range} {status1}')
    print(f'  实际触发价在K线范围内: {actual_in_range} {status2}')
    print(f'  保护价在K线范围内: {protection_in_range} {status3}')
    print()
    
    if not actual_in_range:
        print('🚨 发现问题：实际触发价脱离K线范围！')
        print(f'   实际触发价 {trade["actual_trigger_price"]:.5f} 不在 [{low:.5f}, {high:.5f}] 范围内')
    
    if not protection_in_range:
        print('🚨 发现问题：保护价脱离K线范围！')
        print(f'   保护价 {trade["protection_price"]:.5f} 不在 [{low:.5f}, {high:.5f}] 范围内')
    
    print('-' * 60)
