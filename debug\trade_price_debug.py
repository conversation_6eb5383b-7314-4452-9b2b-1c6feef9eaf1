#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易价格超出K线范围调试工具
针对特定时间点进行详细分析
"""

import json
import pandas as pd
from datetime import datetime, timedelta

def debug_trade_price_issue():
    """调试交易价格超出K线范围的问题"""
    
    # 问题时间点
    target_time = "2025-03-01 02:19:00"
    
    print(f"🔍 调试交易价格问题 - 时间点: {target_time}")
    print("=" * 60)
    
    # 从复制的数据中提取信息
    problem_data = {
        "时间": "2025-03-01 02:19:00",
        "K线数据": {
            "开盘": 0.19844,
            "收盘": 0.19777,
            "最高": 0.19846,
            "最低": 0.19760
        },
        "交易数据": {
            "时间": "2025-03-01 02:19:00",
            "类型": "close_long",
            "价格": 0.19996,  # 这个价格超出了K线范围！
            "数量": 504.64,
            "手续费": -0.05045
        },
        "策略数据": {
            "触发价": 0.19996,
            "实际触发价": 0.19824,
            "保护价": 0.19844,
            "参考K线": "02:15:00(0.19984-0.20005-0.19960)"
        }
    }
    
    # 分析问题
    print("📊 K线数据分析:")
    kline = problem_data["K线数据"]
    print(f"   开盘价: {kline['开盘']}")
    print(f"   收盘价: {kline['收盘']}")
    print(f"   最高价: {kline['最高']}")
    print(f"   最低价: {kline['最低']}")
    print(f"   价格范围: [{kline['最低']}, {kline['最高']}]")
    
    print("\n💰 交易数据分析:")
    trade = problem_data["交易数据"]
    print(f"   交易价格: {trade['价格']}")
    print(f"   交易类型: {trade['类型']}")
    
    # 检查价格是否超出范围
    trade_price = trade['价格']
    kline_high = kline['最高']
    kline_low = kline['最低']
    
    print(f"\n🚨 价格范围检查:")
    print(f"   交易价格: {trade_price}")
    print(f"   K线范围: [{kline_low}, {kline_high}]")
    
    if trade_price > kline_high:
        exceed_high = trade_price - kline_high
        exceed_pct = (exceed_high / kline_high) * 100
        print(f"   ❌ 交易价格超出最高价 {exceed_high:.5f} ({exceed_pct:.3f}%)")
    elif trade_price < kline_low:
        exceed_low = kline_low - trade_price
        exceed_pct = (exceed_low / kline_low) * 100
        print(f"   ❌ 交易价格低于最低价 {exceed_low:.5f} ({exceed_pct:.3f}%)")
    else:
        print(f"   ✅ 交易价格在K线范围内")
    
    # 分析策略数据
    print(f"\n🎯 策略数据分析:")
    strategy = problem_data["策略数据"]
    print(f"   触发价: {strategy['触发价']}")
    print(f"   实际触发价: {strategy['实际触发价']}")
    print(f"   保护价: {strategy['保护价']}")
    print(f"   参考K线: {strategy['参考K线']}")
    
    # 解析参考K线数据
    ref_kline_str = strategy['参考K线']
    if "(" in ref_kline_str and ")" in ref_kline_str:
        ref_time = ref_kline_str.split("(")[0]
        ref_prices = ref_kline_str.split("(")[1].split(")")[0].split("-")
        if len(ref_prices) == 3:
            ref_low, ref_high, ref_close = [float(p) for p in ref_prices]
            print(f"\n📈 参考K线分析 ({ref_time}):")
            print(f"   参考最低价: {ref_low}")
            print(f"   参考最高价: {ref_high}")
            print(f"   参考收盘价: {ref_close}")
            print(f"   参考范围: [{ref_low}, {ref_high}]")
            
            # 检查触发价是否在参考K线范围内
            trigger_price = strategy['触发价']
            if ref_low <= trigger_price <= ref_high:
                print(f"   ✅ 触发价 {trigger_price} 在参考K线范围内")
            else:
                print(f"   ⚠️ 触发价 {trigger_price} 超出参考K线范围")
    
    # 可能的原因分析
    print(f"\n🔍 可能原因分析:")
    print("1. 交易价格可能来自上一个K线的数据")
    print("2. 策略计算时使用了错误的K线数据")
    print("3. 交易执行时间与K线时间不匹配")
    print("4. 数据同步问题导致价格错位")
    
    # 建议的调试步骤
    print(f"\n🛠️ 建议调试步骤:")
    print("1. 检查交易执行的具体时间戳")
    print("2. 验证策略计算时使用的K线数据")
    print("3. 检查数据获取和处理的时序")
    print("4. 确认交易价格的来源和计算逻辑")
    
    return problem_data

def create_debug_query():
    """生成用于调试的数据库查询"""
    
    target_time = "2025-03-01 02:19:00"
    
    # 查询周围时间的K线数据
    print(f"\n📝 调试查询语句:")
    print("=" * 40)
    
    # K线数据查询
    print("-- 查询问题时间点周围的K线数据")
    print(f"""
SELECT 
    timestamp,
    open_price,
    close_price,
    high_price,
    low_price,
    volume
FROM kline_data 
WHERE timestamp BETWEEN '{target_time}' - INTERVAL 10 MINUTE 
  AND '{target_time}' + INTERVAL 10 MINUTE
ORDER BY timestamp;
""")
    
    # 交易数据查询
    print("-- 查询问题时间点的交易数据")
    print(f"""
SELECT 
    timestamp,
    position_type,
    price,
    amount,
    fee,
    trigger_reason
FROM trades 
WHERE timestamp BETWEEN '{target_time}' - INTERVAL 5 MINUTE 
  AND '{target_time}' + INTERVAL 5 MINUTE
ORDER BY timestamp;
""")
    
    # 策略日志查询
    print("-- 查询问题时间点的策略日志")
    print(f"""
SELECT 
    timestamp,
    action,
    trigger_price,
    actual_trigger_price,
    protection_price,
    reason
FROM strategy_logs 
WHERE timestamp BETWEEN '{target_time}' - INTERVAL 5 MINUTE 
  AND '{target_time}' + INTERVAL 5 MINUTE
ORDER BY timestamp;
""")

def analyze_time_sequence():
    """分析时间序列问题"""
    
    print(f"\n⏰ 时间序列分析:")
    print("=" * 40)
    
    # 问题时间点
    problem_time = datetime.strptime("2025-03-01 02:19:00", "%Y-%m-%d %H:%M:%S")
    ref_time = datetime.strptime("2025-03-01 02:15:00", "%Y-%m-%d %H:%M:%S")
    
    time_diff = problem_time - ref_time
    
    print(f"问题时间: {problem_time}")
    print(f"参考时间: {ref_time}")
    print(f"时间差: {time_diff.total_seconds()} 秒")
    
    # 分析可能的时间窗口问题
    print(f"\n🕐 可能的时间窗口问题:")
    print("1. 策略计算时使用了前一个K线的价格")
    print("2. 交易执行延迟导致价格不匹配")
    print("3. 数据更新时序问题")
    
    # 建议检查的时间点
    check_times = []
    for i in range(-5, 6):
        check_time = problem_time + timedelta(minutes=i)
        check_times.append(check_time.strftime("%Y-%m-%d %H:%M:%S"))
    
    print(f"\n📅 建议检查的时间点:")
    for i, time_str in enumerate(check_times):
        if i == 5:  # 中间的时间点（问题时间）
            print(f"   {time_str} ← 问题时间")
        else:
            print(f"   {time_str}")

if __name__ == "__main__":
    print("🐛 交易价格超出K线范围调试工具")
    print("=" * 60)
    
    # 执行调试分析
    problem_data = debug_trade_price_issue()
    
    # 生成调试查询
    create_debug_query()
    
    # 分析时间序列
    analyze_time_sequence()
    
    print(f"\n✅ 调试分析完成")
    print("请根据上述分析结果检查代码逻辑和数据一致性")
