import pymysql
from config import *

def execute_sql_file():
    # 建立数据库连接
    conn = pymysql.connect(
        host=MYSQL_HOST,
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        database=MYSQL_DATABASE,
        port=MYSQL_PORT
    )
    
    try:
        with conn.cursor() as cursor:
            # 读取SQL文件
            with open('sql/create_indexes.sql', 'r', encoding='utf-8') as file:
                sql_commands = file.read().split(';')
                
                # 执行每个SQL命令
                for command in sql_commands:
                    command = command.strip()
                    if command:  # 跳过空命令
                        try:
                            print(f"执行SQL: {command}")
                            cursor.execute(command)
                            print("执行成功")
                        except Exception as e:
                            print(f"执行出错: {str(e)}")
                
        # 提交更改
        conn.commit()
        print("所有索引创建完成")
        
    except Exception as e:
        print(f"发生错误: {str(e)}")
        conn.rollback()
    
    finally:
        conn.close()

if __name__ == "__main__":
    execute_sql_file()
