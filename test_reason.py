#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 测试详细原因描述的脚本

def test_buy_rate_reason():
    """测试buy_rate的详细原因描述"""
    
    # 模拟做多触发
    buy_rate = 0.4
    lookback_minutes_sell = 5
    lookback_minutes_buy = 2
    
    # 模拟下跌期最后K线数据
    last_down_candle = {
        'timestamp': '2025-04-10 01:11:00',
        'open': 0.14828,
        'high': 0.14865,
        'low': 0.14824,
        'close': 0.14855
    }
    
    price_range = last_down_candle['high'] - last_down_candle['low']
    trigger_price = last_down_candle['low'] + (price_range * buy_rate)
    
    if buy_rate == 1.0:
        reason = f'做多触发\n条件：先跌{lookback_minutes_sell}次，再涨{lookback_minutes_buy}次\n倍率：buy_rate=1.0\n参考K线：{last_down_candle["timestamp"][-8:]}(开:{last_down_candle["open"]:.5f},高:{last_down_candle["high"]:.5f},低:{last_down_candle["low"]:.5f})\n计算公式：buy_rate=1.0时直接使用最高价\n触发价格：{trigger_price:.5f}'
    else:
        reason = f'做多触发\n条件：先跌{lookback_minutes_sell}次，再涨{lookback_minutes_buy}次\n倍率：buy_rate={buy_rate}\n参考K线：{last_down_candle["timestamp"][-8:]}(开:{last_down_candle["open"]:.5f},高:{last_down_candle["high"]:.5f},低:{last_down_candle["low"]:.5f})\n价格范围：最高价-最低价={last_down_candle["high"]:.5f}-{last_down_candle["low"]:.5f}={price_range:.5f}\n计算公式：最低价+价格范围×buy_rate\n触发价格：{last_down_candle["low"]:.5f}+{price_range:.5f}×{buy_rate}={trigger_price:.5f}'
    
    print("做多触发原因:")
    print(reason)
    print()

def test_sell_rate_reason():
    """测试sell_rate的详细原因描述"""

    # 模拟平多触发
    sell_rate = 0.6
    consecutive_down_count = 2

    # 模拟最后上涨K线数据（修复后的参考K线）
    last_up_candle = {
        'timestamp': '2025-04-10 01:16:00',
        'open': 0.14874,
        'high': 0.14895,
        'low': 0.14874,
        'close': 0.14895
    }

    price_range = last_up_candle['high'] - last_up_candle['low']
    trigger_price = last_up_candle['high'] - (price_range * sell_rate)

    if sell_rate == 1.0:
        reason = f'平多触发\n条件：连续{consecutive_down_count}次下跌\n倍率：sell_rate=1.0\n参考K线：{last_up_candle["timestamp"][-8:]}(开:{last_up_candle["open"]:.5f},高:{last_up_candle["high"]:.5f},低:{last_up_candle["low"]:.5f})\n计算公式：sell_rate=1.0时直接使用最低价\n触发价格：{trigger_price:.5f}'
    else:
        reason = f'平多触发\n条件：连续{consecutive_down_count}次下跌\n倍率：sell_rate={sell_rate}\n参考K线：{last_up_candle["timestamp"][-8:]}(开:{last_up_candle["open"]:.5f},高:{last_up_candle["high"]:.5f},低:{last_up_candle["low"]:.5f})\n价格范围：最高价-最低价={last_up_candle["high"]:.5f}-{last_up_candle["low"]:.5f}={price_range:.5f}\n计算公式：最高价-价格范围×sell_rate\n触发价格：{last_up_candle["high"]:.5f}-{price_range:.5f}×{sell_rate}={trigger_price:.5f}'

    print("平多触发原因:")
    print(reason)
    print()

def test_short_reason():
    """测试做空的详细原因描述"""
    
    # 模拟做空触发
    buy_rate = 0.3
    lookback_minutes_sell = 5
    lookback_minutes_buy = 2
    
    # 模拟上涨期最高K线数据
    highest_candle = {
        'timestamp': '2025-04-10 05:02:00',
        'open': 0.16338,
        'high': 0.16390,
        'low': 0.16337,
        'close': 0.16340
    }
    
    price_range = highest_candle['high'] - highest_candle['low']
    trigger_price = highest_candle['high'] - (price_range * buy_rate)
    
    if buy_rate == 1.0:
        reason = f'做空触发\n条件：先涨{lookback_minutes_sell}次，再跌{lookback_minutes_buy}次\n倍率：buy_rate=1.0\n参考K线：{highest_candle["timestamp"][-8:]}(开:{highest_candle["open"]:.5f},高:{highest_candle["high"]:.5f},低:{highest_candle["low"]:.5f})\n触发价格：直接跌破最低价{trigger_price:.5f}'
    else:
        reason = f'做空触发\n条件：先涨{lookback_minutes_sell}次，再跌{lookback_minutes_buy}次\n倍率：buy_rate={buy_rate}\n参考K线：{highest_candle["timestamp"][-8:]}(开:{highest_candle["open"]:.5f},高:{highest_candle["high"]:.5f},低:{highest_candle["low"]:.5f})\n价格范围：{price_range:.5f}\n触发价格：{highest_candle["high"]:.5f}-{price_range:.5f}×{buy_rate}={trigger_price:.5f}'
    
    print("做空触发原因:")
    print(reason)
    print()

def test_close_short_reason():
    """测试平空的详细原因描述"""

    # 模拟平空触发
    sell_rate = 0.7
    consecutive_up_count = 2

    # 模拟最后下跌K线数据（修复后的参考K线）
    last_down_candle = {
        'timestamp': '2025-04-10 05:06:00',
        'open': 0.16280,
        'high': 0.16285,
        'low': 0.16260,
        'close': 0.16265
    }

    price_range = last_down_candle['high'] - last_down_candle['low']
    trigger_price = last_down_candle['low'] + (price_range * sell_rate)

    if sell_rate == 1.0:
        reason = f'平空触发\n条件：连续{consecutive_up_count}次上涨\n倍率：sell_rate=1.0\n参考K线：{last_down_candle["timestamp"][-8:]}(开:{last_down_candle["open"]:.5f},高:{last_down_candle["high"]:.5f},低:{last_down_candle["low"]:.5f})\n计算公式：sell_rate=1.0时直接使用最高价\n触发价格：{trigger_price:.5f}'
    else:
        reason = f'平空触发\n条件：连续{consecutive_up_count}次上涨\n倍率：sell_rate={sell_rate}\n参考K线：{last_down_candle["timestamp"][-8:]}(开:{last_down_candle["open"]:.5f},高:{last_down_candle["high"]:.5f},低:{last_down_candle["low"]:.5f})\n价格范围：最高价-最低价={last_down_candle["high"]:.5f}-{last_down_candle["low"]:.5f}={price_range:.5f}\n计算公式：最低价+价格范围×sell_rate\n触发价格：{last_down_candle["low"]:.5f}+{price_range:.5f}×{sell_rate}={trigger_price:.5f}'

    print("平空触发原因:")
    print(reason)
    print()

if __name__ == "__main__":
    print("=== 详细原因描述测试 ===")
    print()
    
    test_buy_rate_reason()
    test_sell_rate_reason()
    test_short_reason()
    test_close_short_reason()
    
    print("=== 测试完成 ===")
