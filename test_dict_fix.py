#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试字典修复的脚本
验证 row_reson_list 字典操作
"""

def test_dict_operations():
    """测试字典操作修复"""
    
    # 模拟 self.row_reson_list 初始化
    row_reson_list = {}
    
    # 模拟交易数据
    trade = {
        'timestamp': '2025-07-25 19:14:50',
        'action': '平多',
        'reason': """平多触发
条件：连续2次下跌
倍率：sell_rate=0.2
参考K线：01:11:00(开:0.14828,高:0.14865,低:0.14824)
价格范围：最高价-最低价=0.14865-0.14824=0.00041
计算公式：最高价-价格范围×sell_rate
计算结果：0.14865-0.00041×0.2=0.14857
价格保护：触发价格0.14857>开盘价0.14828
实际触发：直接以开盘价0.14828触发
详细分析：当前K线开盘价为0.14828，最高价为0.14865，最低价为0.14824
价格范围计算：0.14865 - 0.14824 = 0.00041
触发价格计算：0.14865 - 0.00041 × 0.2 = 0.14857
由于触发价格0.14857大于开盘价0.14828，超出了K线范围
因此采用价格保护机制，直接以开盘价0.14828作为触发价格""",
        'price': 0.14828
    }
    
    print("🔍 测试字典操作修复")
    print("="*50)
    
    # 模拟修复后的逻辑
    original_reason = trade['reason']
    reason = original_reason
    
    print(f"原始原因长度: {len(original_reason)} 字符")
    
    # 如果原因太长，截断并保存完整版本
    if len(original_reason) > 250:  # 留5个字符的缓冲
        reason = original_reason[:247] + '...'
        
        # 保存完整的原因到row_reason_list中（使用字典格式）
        if row_reson_list is not None:
            # 创建唯一的键，避免重复
            key = f"{trade['timestamp']}_{trade['action']}_truncated"
            row_reson_list[key] = {
                'timestamp': trade['timestamp'],
                'action': trade['action'],
                'full_reason': original_reason,
                'truncated_reason': reason,
                'original_length': len(original_reason),
                'truncated_length': len(reason)
            }
            
        print(f"⚠️ 交易原因过长已截断: {trade['timestamp']} {trade['action']} (原长度: {len(original_reason)})")
        print(f"截断后长度: {len(reason)} 字符")
        print(f"截断后原因: {reason[:100]}...")
    else:
        print("✅ 原因长度正常，无需截断")
    
    print("\n" + "="*50)
    print("📊 row_reson_list 内容:")
    
    if row_reson_list:
        for key, value in row_reson_list.items():
            print(f"\n键: {key}")
            print(f"时间戳: {value['timestamp']}")
            print(f"动作: {value['action']}")
            print(f"原始长度: {value['original_length']}")
            print(f"截断长度: {value['truncated_length']}")
            print(f"完整原因: {value['full_reason'][:100]}...")
            print(f"截断原因: {value['truncated_reason'][:100]}...")
    else:
        print("字典为空")
    
    return row_reson_list

def test_multiple_trades():
    """测试多个交易的情况"""
    
    print("\n🔄 测试多个交易")
    print("="*50)
    
    row_reson_list = {}
    
    # 模拟多个交易
    trades = [
        {
            'timestamp': '2025-07-25 19:14:50',
            'action': '开多',
            'reason': '做多触发：价格突破0.14857'
        },
        {
            'timestamp': '2025-07-25 19:15:00',
            'action': '平多',
            'reason': """平多触发
条件：连续2次下跌
倍率：sell_rate=0.2
参考K线：01:11:00(开:0.14828,高:0.14865,低:0.14824)
价格范围：最高价-最低价=0.14865-0.14824=0.00041
计算公式：最高价-价格范围×sell_rate
计算结果：0.14865-0.00041×0.2=0.14857
价格保护：触发价格0.14857>开盘价0.14828
实际触发：直接以开盘价0.14828触发"""
        },
        {
            'timestamp': '2025-07-25 19:16:00',
            'action': '开空',
            'reason': '做空触发：价格跌破0.14820'
        }
    ]
    
    for i, trade in enumerate(trades, 1):
        print(f"\n处理交易 {i}: {trade['action']}")
        
        original_reason = trade['reason']
        reason = original_reason
        
        if len(original_reason) > 250:
            reason = original_reason[:247] + '...'
            
            # 创建唯一的键
            key = f"{trade['timestamp']}_{trade['action']}_truncated"
            row_reson_list[key] = {
                'timestamp': trade['timestamp'],
                'action': trade['action'],
                'full_reason': original_reason,
                'truncated_reason': reason,
                'original_length': len(original_reason),
                'truncated_length': len(reason)
            }
            
            print(f"  ⚠️ 截断: {len(original_reason)} → {len(reason)} 字符")
        else:
            print(f"  ✅ 正常: {len(original_reason)} 字符")
    
    print(f"\n📊 总共处理 {len(trades)} 个交易")
    print(f"📝 截断记录数: {len(row_reson_list)}")
    
    return row_reson_list

def test_json_export():
    """测试JSON导出"""
    
    print("\n💾 测试JSON导出")
    print("="*50)
    
    # 获取测试数据
    row_reson_list = test_multiple_trades()
    
    if row_reson_list:
        import json
        
        # 模拟保存到文件
        json_content = json.dumps(row_reson_list, ensure_ascii=False, indent=4)
        
        print("JSON内容预览:")
        print(json_content[:500] + "..." if len(json_content) > 500 else json_content)
        
        print(f"\nJSON长度: {len(json_content)} 字符")
        print("✅ JSON导出测试成功")
    else:
        print("❌ 没有数据需要导出")

if __name__ == "__main__":
    print("🧪 字典操作修复测试")
    print("="*60)
    
    # 测试基本字典操作
    result1 = test_dict_operations()
    
    # 测试多个交易
    result2 = test_multiple_trades()
    
    # 测试JSON导出
    test_json_export()
    
    print("\n✅ 所有测试完成！")
    print("\n💡 修复要点:")
    print("1. row_reson_list 是字典，不是列表")
    print("2. 使用 dict[key] = value 而不是 dict.append()")
    print("3. 创建唯一键避免重复")
    print("4. 保存完整的截断信息")
