#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试交易原因长度的脚本
用于验证reason字段截断功能
"""

def test_reason_length():
    """测试不同长度的交易原因"""
    
    # 模拟一个很长的交易原因
    long_reason = """平多触发
条件：连续2次下跌
倍率：sell_rate=0.2
参考K线：01:11:00(开:0.14828,高:0.14865,低:0.14824)
价格范围：最高价-最低价=0.14865-0.14824=0.00041
计算公式：最高价-价格范围×sell_rate
计算结果：0.14865-0.00041×0.2=0.14857
价格保护：触发价格0.14857>开盘价0.14828
实际触发：直接以开盘价0.14828触发
详细分析：当前K线开盘价为0.14828，最高价为0.14865，最低价为0.14824
价格范围计算：0.14865 - 0.14824 = 0.00041
触发价格计算：0.14865 - 0.00041 × 0.2 = 0.14857
由于触发价格0.14857大于开盘价0.14828，超出了K线范围
因此采用价格保护机制，直接以开盘价0.14828作为触发价格"""
    
    print(f"原始原因长度: {len(long_reason)} 字符")
    print(f"原始原因:\n{long_reason}")
    print("\n" + "="*50 + "\n")
    
    # 模拟截断逻辑
    if len(long_reason) > 250:
        truncated_reason = long_reason[:247] + '...'
        print(f"截断后长度: {len(truncated_reason)} 字符")
        print(f"截断后原因:\n{truncated_reason}")
    else:
        print("原因长度在限制范围内，无需截断")
    
    print("\n" + "="*50 + "\n")
    
    # 测试短原因
    short_reason = "做多触发：价格突破0.14857"
    print(f"短原因长度: {len(short_reason)} 字符")
    print(f"短原因: {short_reason}")
    
    if len(short_reason) > 250:
        truncated_short = short_reason[:247] + '...'
        print(f"截断后: {truncated_short}")
    else:
        print("短原因无需截断")

def test_database_field_limits():
    """测试数据库字段限制"""
    
    print("数据库字段限制测试:")
    print("reason字段: VARCHAR(255) - 最大255字符")
    print("trigger_reason字段: VARCHAR(255) - 最大255字符")
    
    # 测试边界情况
    test_cases = [
        ("正常长度", "做多触发：价格突破"),
        ("接近限制", "a" * 250),
        ("刚好限制", "a" * 255),
        ("超过限制", "a" * 300),
        ("大幅超过", "a" * 500)
    ]
    
    for name, test_reason in test_cases:
        length = len(test_reason)
        if length > 250:
            truncated = test_reason[:247] + '...'
            print(f"{name}: {length}字符 -> 截断为{len(truncated)}字符")
        else:
            print(f"{name}: {length}字符 -> 无需截断")

def simulate_strategy_analyzer_fix():
    """模拟策略分析器的修复逻辑"""
    
    # 模拟交易数据
    trades = [
        {
            'timestamp': '2025-07-25 18:40:30',
            'action': '开多',
            'reason': '做多触发：价格突破0.14857',
            'price': 0.14857
        },
        {
            'timestamp': '2025-07-25 18:41:00',
            'action': '平多',
            'reason': """平多触发
条件：连续2次下跌
倍率：sell_rate=0.2
参考K线：01:11:00(开:0.14828,高:0.14865,低:0.14824)
价格范围：最高价-最低价=0.14865-0.14824=0.00041
计算公式：最高价-价格范围×sell_rate
计算结果：0.14865-0.00041×0.2=0.14857
价格保护：触发价格0.14857>开盘价0.14828
实际触发：直接以开盘价0.14828触发
详细分析：当前K线开盘价为0.14828，最高价为0.14865，最低价为0.14824
价格范围计算：0.14865 - 0.14824 = 0.00041
触发价格计算：0.14865 - 0.00041 × 0.2 = 0.14857
由于触发价格0.14857大于开盘价0.14828，超出了K线范围
因此采用价格保护机制，直接以开盘价0.14828作为触发价格""",
            'price': 0.14828
        }
    ]
    
    print("模拟策略分析器修复逻辑:")
    print("="*60)
    
    truncated_reasons = []
    
    for i, trade in enumerate(trades, 1):
        print(f"\n交易 {i}:")
        print(f"时间: {trade['timestamp']}")
        print(f"动作: {trade['action']}")
        print(f"价格: {trade['price']}")
        
        original_reason = trade['reason']
        print(f"原始原因长度: {len(original_reason)} 字符")
        
        # 应用修复逻辑
        if len(original_reason) > 250:
            reason = original_reason[:247] + '...'
            print(f"⚠️ 原因过长，已截断为 {len(reason)} 字符")
            print(f"截断后原因: {reason}")
            
            # 保存完整原因
            truncated_reasons.append({
                'timestamp': trade['timestamp'],
                'action': trade['action'],
                'full_reason': original_reason,
                'truncated_reason': reason
            })
        else:
            reason = original_reason
            print(f"✅ 原因长度正常，无需截断")
            print(f"原因: {reason}")
    
    if truncated_reasons:
        print(f"\n📝 共有 {len(truncated_reasons)} 个交易原因被截断")
        print("完整原因已保存到 row_reason_list 中")

if __name__ == "__main__":
    print("🔍 交易原因长度测试")
    print("="*60)
    
    test_reason_length()
    print("\n" + "="*60 + "\n")
    
    test_database_field_limits()
    print("\n" + "="*60 + "\n")
    
    simulate_strategy_analyzer_fix()
    
    print("\n✅ 测试完成！")
    print("\n💡 修复方案:")
    print("1. 检查reason字段长度")
    print("2. 如果超过250字符，截断为247字符并添加'...'")
    print("3. 将完整原因保存到JSON文件中")
    print("4. 在日志中记录截断操作")
