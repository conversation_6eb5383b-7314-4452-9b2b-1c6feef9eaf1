#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试触发保护逻辑修正
验证做多和做空的保护机制是否正确体现趋势方向
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG
from datetime import datetime

def test_trigger_protection():
    """测试触发保护逻辑"""
    print("🧪 测试触发保护逻辑修正")
    print("=" * 50)
    
    # 创建策略分析器
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 测试参数
    test_params = {
        'current_position': 0,
        'last_close_time': None,
        'rest_minutes': 0,
        'lookback_minutes_buy': 2,
        'lookback_minutes_sell': 2,
        'buy_rate': 0.2,
        'sell_rate': 0.015,
        'price_adjust_rate': 0.001,  # 0.1%
        'minute_candles': []
    }
    
    # 模拟K线数据 - 先跌后涨的做多场景
    candles_data = [
        # 下跌期
        {'timestamp': '2025-03-03T08:41:00+00:00', 'open': 0.23750, 'close': 0.23720, 'high': 0.23760, 'low': 0.23710, 'is_up': False},
        {'timestamp': '2025-03-03T08:42:00+00:00', 'open': 0.23720, 'close': 0.23690, 'high': 0.23730, 'low': 0.23680, 'is_up': False},
        # 上涨期
        {'timestamp': '2025-03-03T08:43:00+00:00', 'open': 0.23690, 'close': 0.23720, 'high': 0.23730, 'low': 0.23680, 'is_up': True},
        {'timestamp': '2025-03-03T08:44:00+00:00', 'open': 0.23720, 'close': 0.23750, 'high': 0.23760, 'low': 0.23710, 'is_up': True},
        # 当前K线 - 继续上涨但触发价格超出范围，需要保护
        {'timestamp': '2025-03-03T08:45:00+00:00', 'open': 0.23677, 'close': 0.23720, 'high': 0.23750, 'low': 0.23617, 'is_up': True}
    ]
    
    # 添加K线数据到参数
    test_params['minute_candles'] = candles_data
    
    # 构造当前K线数据
    current_row = {
        'timestamp': '2025-03-03T08:45:00+00:00',
        'open': 0.23677,
        'close': 0.23720,
        'high': 0.23750,
        'low': 0.23617,
        'is_complete': 1
    }
    
    print("📊 测试场景：做多保护机制")
    print(f"当前K线：开盘{current_row['open']:.5f} 最高{current_row['high']:.5f} 最低{current_row['low']:.5f}")
    print(f"价格调整比例：{test_params['price_adjust_rate']*100:.1f}%")
    print()
    
    # 测试交易条件
    result = analyzer.trade_condition_new(current_row, test_params)
    
    if result and result['action']:
        print(f"✅ 交易信号：{result['action']}")
        print(f"📝 交易原因：{result['reason']}")
        print(f"💰 触发价格：{result['trigger_price']:.5f}")
        
        if 'actual_trigger_price' in result:
            print(f"💰 实际触发价：{result['actual_trigger_price']:.5f}")
            
            # 验证保护逻辑
            expected_protection_price = current_row['open'] * (1 + test_params['price_adjust_rate'])
            actual_protection_price = result['actual_trigger_price']
            
            print(f"🔍 保护价格验证：")
            print(f"   期望保护价：{expected_protection_price:.5f} (开盘价+{test_params['price_adjust_rate']*100:.1f}%)")
            print(f"   实际保护价：{actual_protection_price:.5f}")
            
            if abs(actual_protection_price - expected_protection_price) < 0.00001:
                print("   ✅ 保护价格正确：体现上涨趋势")
            else:
                print("   ❌ 保护价格错误")
                
            # 验证价格在K线范围内
            if current_row['low'] <= actual_protection_price <= current_row['high']:
                print("   ✅ 保护价格在K线范围内")
            else:
                print("   ❌ 保护价格超出K线范围")
        
    else:
        print("❌ 未产生交易信号")
        if result:
            print(f"📝 原因：{result['reason']}")
    
    print()
    print("=" * 50)
    
    # 测试做空场景
    print("📊 测试场景：做空保护机制")
    
    # 模拟K线数据 - 先涨后跌的做空场景
    candles_data_short = [
        # 上涨期
        {'timestamp': '2025-03-03T08:41:00+00:00', 'open': 0.23650, 'close': 0.23680, 'high': 0.23690, 'low': 0.23640, 'is_up': True},
        {'timestamp': '2025-03-03T08:42:00+00:00', 'open': 0.23680, 'close': 0.23710, 'high': 0.23720, 'low': 0.23670, 'is_up': True},
        # 下跌期
        {'timestamp': '2025-03-03T08:43:00+00:00', 'open': 0.23710, 'close': 0.23680, 'high': 0.23720, 'low': 0.23670, 'is_up': False},
        {'timestamp': '2025-03-03T08:44:00+00:00', 'open': 0.23680, 'close': 0.23650, 'high': 0.23690, 'low': 0.23640, 'is_up': False},
        # 当前K线 - 继续下跌但触发价格超出范围，需要保护
        {'timestamp': '2025-03-03T08:45:00+00:00', 'open': 0.23677, 'close': 0.23650, 'high': 0.23700, 'low': 0.23617, 'is_up': False}
    ]
    
    # 重置参数
    test_params['minute_candles'] = candles_data_short
    
    # 构造当前K线数据（做空场景）
    current_row_short = {
        'timestamp': '2025-03-03T08:45:00+00:00',
        'open': 0.23677,
        'close': 0.23650,
        'high': 0.23700,
        'low': 0.23617,
        'is_complete': 1
    }
    
    print(f"当前K线：开盘{current_row_short['open']:.5f} 最高{current_row_short['high']:.5f} 最低{current_row_short['low']:.5f}")
    print()
    
    # 测试交易条件
    result_short = analyzer.trade_condition_new(current_row_short, test_params)
    
    if result_short and result_short['action']:
        print(f"✅ 交易信号：{result_short['action']}")
        print(f"📝 交易原因：{result_short['reason']}")
        print(f"💰 触发价格：{result_short['trigger_price']:.5f}")
        
        if 'actual_trigger_price' in result_short:
            print(f"💰 实际触发价：{result_short['actual_trigger_price']:.5f}")
            
            # 验证保护逻辑
            expected_protection_price = current_row_short['open'] * (1 - test_params['price_adjust_rate'])
            actual_protection_price = result_short['actual_trigger_price']
            
            print(f"🔍 保护价格验证：")
            print(f"   期望保护价：{expected_protection_price:.5f} (开盘价-{test_params['price_adjust_rate']*100:.1f}%)")
            print(f"   实际保护价：{actual_protection_price:.5f}")
            
            if abs(actual_protection_price - expected_protection_price) < 0.00001:
                print("   ✅ 保护价格正确：体现下跌趋势")
            else:
                print("   ❌ 保护价格错误")
                
            # 验证价格在K线范围内
            if current_row_short['low'] <= actual_protection_price <= current_row_short['high']:
                print("   ✅ 保护价格在K线范围内")
            else:
                print("   ❌ 保护价格超出K线范围")
        
    else:
        print("❌ 未产生交易信号")
        if result_short:
            print(f"📝 原因：{result_short['reason']}")
    
    print()
    print("🎯 测试总结：")
    print("- 做多保护：开盘价 + 0.1% (体现上涨趋势)")
    print("- 做空保护：开盘价 - 0.1% (体现下跌趋势)")
    print("- 确保保护价格在当前K线范围内")
    print("✅ 触发保护逻辑修正完成")

if __name__ == "__main__":
    test_trigger_protection()
