# 策略重新运行参数传递示例

## ✅ **修改完成**

我已经修改了 `rerun_strategies` 方法，现在可以传递所有策略参数了！

### **新增的参数支持**:
- `--currency` - 货币对
- `--buy-rate` - 买入阈值
- `--sell-rate` - 卖出阈值
- `--rest` - 休息时间（分钟）
- `--min-trigger-rest` - 最小触发休息时间（分钟）
- `--lookback-minutes-buy` - 买入回看时间（分钟）
- `--lookback-minutes-sell` - 卖出回看时间（分钟）

## 🎯 **使用示例**

### **1. 基本重新运行（只改时间）**
```bash
python strategy_analyzer.py --rerun 886151 --rerun-start "2025-07-10 00:00:00" --rerun-end "2025-07-12 23:59:59"
```

### **2. 重新运行并修改货币对**
```bash
python strategy_analyzer.py --rerun 886151 --rerun-start "2025-07-10 00:00:00" --rerun-end "2025-07-12 23:59:59" --currency BTC
```

### **3. 重新运行并修改策略参数**
```bash
python strategy_analyzer.py --rerun 886151 --rerun-start "2025-07-10 00:00:00" --rerun-end "2025-07-12 23:59:59" --currency DOGE --buy-rate 0.025 --sell-rate 0.02
```

### **4. 完整参数示例**
```bash
python strategy_analyzer.py \
  --rerun 886151 \
  --rerun-start "2025-07-10 00:00:00" \
  --rerun-end "2025-07-12 23:59:59" \
  --currency DOGE \
  --buy-rate 0.025 \
  --sell-rate 0.02 \
  --rest 30 \
  --min-trigger-rest 15 \
  --lookback-minutes-buy 60 \
  --lookback-minutes-sell 45 \
  --debug
```

### **5. 测试不同货币对**
```bash
# 测试 BTC
python strategy_analyzer.py --rerun 886151 --currency BTC --rerun-start "2025-07-10 00:00:00" --rerun-end "2025-07-12 23:59:59"

# 测试 ETH
python strategy_analyzer.py --rerun 886151 --currency ETH --rerun-start "2025-07-10 00:00:00" --rerun-end "2025-07-12 23:59:59"

# 测试 DOGE
python strategy_analyzer.py --rerun 886151 --currency DOGE --rerun-start "2025-07-10 00:00:00" --rerun-end "2025-07-12 23:59:59"
```

### **6. 参数优化测试**
```bash
# 测试更激进的参数
python strategy_analyzer.py --rerun 886151 --buy-rate 0.03 --sell-rate 0.025 --rest 20 --rerun-start "2025-07-10 00:00:00" --rerun-end "2025-07-12 23:59:59"

# 测试更保守的参数
python strategy_analyzer.py --rerun 886151 --buy-rate 0.015 --sell-rate 0.01 --rest 60 --rerun-start "2025-07-10 00:00:00" --rerun-end "2025-07-12 23:59:59"
```

### **7. 多个策略ID**
```bash
python strategy_analyzer.py --rerun 886151,886152,886153 --currency DOGE --buy-rate 0.025 --rerun-start "2025-07-10 00:00:00" --rerun-end "2025-07-12 23:59:59"
```

## 📊 **日志输出示例**

运行时会看到详细的参数变化日志：

```
开始重新运行策略: 886151
开始重新执行策略 ID: 886151
策略参数:
  时间范围: 2025-07-10 00:00:00 到 2025-07-12 23:59:59
  货币对: DOGE
  买入阈值: 0.025
  卖出阈值: 0.02
  休息时间: 30分钟
  最小触发休息: 15分钟
  买入回看时间: 60分钟
  卖出回看时间: 45分钟
  ✓ 开始时间已更新: 2025-03-01 00:00:00 -> 2025-07-10 00:00:00
  ✓ 结束时间已更新: 2025-05-31 23:59:59 -> 2025-07-12 23:59:59
  ✓ 买入阈值已更新: 0.02 -> 0.025
  ✓ 卖出阈值已更新: 0.015 -> 0.02
  ✓ 休息时间已更新: 45 -> 30
```

## 🔧 **参数说明**

### **时间参数**
- `--rerun-start` - 新的开始时间
- `--rerun-end` - 新的结束时间

### **交易参数**
- `--currency` - 交易币种 (DOGE, BTC, ETH等)
- `--buy-rate` - 买入阈值，如 0.025 表示 2.5%
- `--sell-rate` - 卖出阈值，如 0.02 表示 2%

### **时间控制参数**
- `--rest` - 交易间隔休息时间（分钟）
- `--min-trigger-rest` - 最小止损间隔时间（分钟）
- `--lookback-minutes-buy` - 买入回看时间（分钟）
- `--lookback-minutes-sell` - 卖出回看时间（分钟）

### **调试参数**
- `--debug` - 启用调试模式，输出详细日志
- `--no-save` - 不保存到数据库（仅测试）

## 💡 **最佳实践**

### **1. 参数测试流程**
```bash
# 1. 先用 --no-save 测试参数
python strategy_analyzer.py --rerun 886151 --currency DOGE --buy-rate 0.025 --no-save --debug

# 2. 确认参数正确后正式运行
python strategy_analyzer.py --rerun 886151 --currency DOGE --buy-rate 0.025 --debug
```

### **2. 批量测试不同参数**
```bash
# 创建批处理脚本测试多组参数
for rate in 0.02 0.025 0.03; do
    python strategy_analyzer.py --rerun 886151 --buy-rate $rate --sell-rate $(echo "$rate * 0.8" | bc) --debug
done
```

### **3. 结果对比**
运行后会生成新的策略ID，可以在Web界面中对比不同参数的效果。

## 🎯 **你的命令现在可以这样用**

```bash
# 你原来的命令
python strategy_analyzer.py --rerun 886151 --rerun-start "2025-07-10 00:00:00" --rerun-end "2025-07-12 23:59:59"

# 现在可以添加更多参数
python strategy_analyzer.py --rerun 886151 --rerun-start "2025-07-10 00:00:00" --rerun-end "2025-07-12 23:59:59" --currency DOGE --buy-rate 0.025 --sell-rate 0.02 --debug
```

现在所有参数都可以正确传递和使用了！🎉
