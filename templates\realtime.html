<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RealtimeChart</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        #chart {
            width: 100%;
            height: 80vh;
        }
        
        #toastContainer {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .toast-message {
            padding: 10px 20px;
            margin-bottom: 10px;
            border-radius: 4px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .toast-message.show {
            opacity: 1;
        }
        
        .toast-message.success { background-color: #d4edda; color: #155724; }
        .toast-message.warning { background-color: #fff3cd; color: #856404; }
        .toast-message.error { background-color: #f8d7da; color: #721c24; }
        .toast-message.info { background-color: #d1ecf1; color: #0c5460; }

        .dashboard-container {
            display: grid;
            grid-template-columns: repeat(6, minmax(108px, 0.6fr)) minmax(200px, 1fr);
            overflow-x: auto;
            scrollbar-width: thin;
            padding-bottom: 8px;
            gap: 15px;
            padding: 15px;
            background: #f8f9fa;
            margin: 15px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            white-space: nowrap;
        }

        .dashboard-card {
            background: white;
            padding: 12px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            transition: transform 0.2s;
            border-left: 4px solid;
            min-width: 108px;
            min-height: auto;
            height: auto;
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
        }

        .dashboard-card .icon {
            font-size: 20px;
            margin-right: 10px;
        }

        .dashboard-card .content {
            flex: 1;
        }

        .dashboard-card .label {
            color: #666;
            font-size: 12px;
            margin-bottom: 5px;
        }

        .dashboard-card .value {
            font-size: 16px;
            font-weight: 600;
        }

        /* 各卡片颜色 */
        .account-value { border-color: #1890ff; }
        .profit { border-color: #52c41a; }
        .trades { border-color: #fa8c16; }
        .income { border-color: #f5222d; }
        .fee { border-color: #722ed1; }
        .reason { border-color: #13c2c2; }

        @keyframes highlight {
            0% { background: rgba(255,215,0,0.3); }
            100% { background: transparent; }
        }

        .dashboard-card.reason .value {
            font-size: 12px;
            line-height: 1.2;
        }

        .dashboard-container::-webkit-scrollbar {
            height: 6px;
            background: #f1f1f1;
        }

        .dashboard-container::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div id="dashboard" class="dashboard-container">
        <div class="dashboard-card account-value">
            <div class="icon">💰</div>
            <div class="content">
                <div class="label">账户价值</div>
                <div class="value" id="accountValue">--</div>
            </div>
        </div>
        
        <div class="dashboard-card profit">
            <div class="icon">📈</div>
            <div class="content">
                <div class="label">收益百分比</div>
                <div class="value" id="profitPercentage">--</div>
            </div>
        </div>

        <div class="dashboard-card trades">
            <div class="icon">🔄</div>
            <div class="content">
                <div class="label">交易次数</div>
                <div class="value" id="tradeCount">0</div>
            </div>
        </div>

        <div class="dashboard-card income">
            <div class="icon">💹</div>
            <div class="content">
                <div class="label">总收入</div>
                <div class="value" id="totalProfit">--</div>
            </div>
        </div>

        <div class="dashboard-card fee">
            <div class="icon">📃</div>
            <div class="content">
                <div class="label">总手续费</div>
                <div class="value" id="totalFee">--</div>
            </div>
        </div>

        <div class="dashboard-card position">
            <div class="icon">📊</div>
            <div class="content">
                <div class="label">仓位状态</div>
                <div class="value" id="positionStatus">--</div>
            </div>
        </div>

        <div class="dashboard-card reason">
            <div class="icon">📌</div>
            <div class="content">
                <div class="label">最新交易原因</div>
                <div class="value" id="lastReason">--</div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row mt-3">
            <div class="col-12">
                <h2 class="text-center">实时K线图</h2>
                <div id="chart"></div>
            </div>
        </div>
    </div>
    
    <div id="toastContainer"></div>

    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="{{ url_for('static', filename='js/realtime_chart.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            RealtimeChart.init('chart');
        });
    </script>
</body>
</html> 