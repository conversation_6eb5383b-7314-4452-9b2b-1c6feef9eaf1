-- 创建存储过程，用于计算最佳策略参数及其在后续时间段的表现
DROP PROCEDURE IF EXISTS analyze_strategy_performance;
DELIMITER //
CREATE PROCEDURE analyze_strategy_performance(
    IN p_date DATE,
    IN p_start_hour INT,
    IN p_duration_hours INT
)
procedure_label: BEGIN
    DECLARE v_start_time DATETIME;
    DECLARE v_end_time DATETIME;
    DECLARE v_next_start_time DATETIME;
    DECLARE v_next_end_time DATETIME;

    -- 设置初始时间段
    SET v_start_time = CONCAT(p_date, ' ', LPAD(p_start_hour, 2, '0'), ':00:00');
    SET v_end_time = DATE_ADD(v_start_time, INTERVAL p_duration_hours HOUR);
    SET v_end_time = DATE_SUB(v_end_time, INTERVAL 1 SECOND);

    -- 设置后续时间段
    SET v_next_start_time = DATE_ADD(v_start_time, INTERVAL p_duration_hours HOUR);
    SET v_next_end_time = DATE_ADD(v_next_start_time, INTERVAL p_duration_hours HOUR);
    SET v_next_end_time = DATE_SUB(v_next_end_time, INTERVAL 1 SECOND);

    -- 首先找出初始时间段收益最好的策略参数
    DROP TABLE IF EXISTS best_strategy;
    CREATE TABLE best_strategy AS
    SELECT
        sell_rate,
        buy_rate,
        min_trigger_rest,
        rest_minutes,
        lookback_minutes_buy,
        lookback_minutes_sell,
        currency,
        final_capital AS initial_capital
    FROM `okx`.`strategy_results`
    WHERE `start_time` = v_start_time AND `end_time` = v_end_time
    ORDER BY `final_capital` DESC
    LIMIT 1;

    -- 检查是否找到了最佳策略
    IF (SELECT COUNT(*) FROM best_strategy) = 0 THEN
        SELECT CONCAT('错误: 在时间段 ', v_start_time, ' 到 ', v_end_time, ' 没有找到策略结果') AS error_message;
        LEAVE procedure_label;
    END IF;

    -- 找出后续时间段最好的策略
    DROP TABLE IF EXISTS best_in_next_period;
    CREATE TABLE best_in_next_period AS
    SELECT
        final_capital AS best_next_capital
    FROM `okx`.`strategy_results`
    WHERE `start_time` = v_next_start_time AND `end_time` = v_next_end_time
    ORDER BY `final_capital` DESC
    LIMIT 1;

    -- 检查是否找到了后续时段的最佳策略
    IF (SELECT COUNT(*) FROM best_in_next_period) = 0 THEN
        SELECT CONCAT('错误: 在后续时间段 ', v_next_start_time, ' 到 ', v_next_end_time, ' 没有找到策略结果') AS error_message;
        LEAVE procedure_label;
    END IF;

    -- 用初始时间段最佳参数在后续时间段的表现
    DROP TABLE IF EXISTS result_next_period;
    CREATE TABLE result_next_period AS
    SELECT
        final_capital AS next_period_capital
    FROM `okx`.`strategy_results`
    WHERE `start_time` = v_next_start_time AND `end_time` = v_next_end_time
    AND `sell_rate` = (SELECT sell_rate FROM best_strategy)
    AND `buy_rate` = (SELECT buy_rate FROM best_strategy)
    AND `min_trigger_rest` = (SELECT min_trigger_rest FROM best_strategy)
    AND `rest_minutes` = (SELECT rest_minutes FROM best_strategy)
    AND `lookback_minutes_buy` = (SELECT lookback_minutes_buy FROM best_strategy)
    AND `lookback_minutes_sell` = (SELECT lookback_minutes_sell FROM best_strategy)
    AND `currency` = (SELECT currency FROM best_strategy)
    LIMIT 1;

    -- 检查是否找到了初始策略在后续时段的结果
    IF (SELECT COUNT(*) FROM result_next_period) = 0 THEN
        SELECT CONCAT('错误: 在后续时间段 ', v_next_start_time, ' 到 ', v_next_end_time, ' 没有找到初始策略的结果') AS error_message;
        LEAVE procedure_label;
    END IF;

    -- 将分析结果插入到结果表中
    INSERT INTO `okx`.`strategy_performance_analysis` (
        analysis_date,
        initial_period_start,
        initial_period_end,
        next_period_start,
        next_period_end,
        sell_rate,
        buy_rate,
        min_trigger_rest,
        rest_minutes,
        lookback_minutes_buy,
        lookback_minutes_sell,
        currency,
        initial_period_best_capital,
        next_period_best_capital,
        initial_strategy_in_next_period,
        performance_ratio_percent,
        created_at
    )
    SELECT
        p_date AS analysis_date,
        v_start_time AS initial_period_start,
        v_end_time AS initial_period_end,
        v_next_start_time AS next_period_start,
        v_next_end_time AS next_period_end,
        bs.sell_rate,
        bs.buy_rate,
        bs.min_trigger_rest,
        bs.rest_minutes,
        bs.lookback_minutes_buy,
        bs.lookback_minutes_sell,
        bs.currency,
        -- 最佳策略在初始时段的表现（初始时段最佳收益）
        bs.initial_capital AS initial_period_best_capital,
        -- 后续时段最佳策略收益（后续时段理论最佳收益）
        (SELECT best_next_capital FROM best_in_next_period) AS next_period_best_capital,
        -- 初始时段最佳策略在后续时段的表现（实际获得收益）
        (SELECT next_period_capital FROM result_next_period) AS initial_strategy_in_next_period,
        -- 计算性能比率（实际收益/理论最佳收益的百分比）
        ((SELECT next_period_capital FROM result_next_period) /
         (SELECT best_next_capital FROM best_in_next_period)) * 100 AS performance_ratio_percent,
        NOW() AS created_at
    FROM best_strategy bs;

    -- 返回分析结果以便查看
    SELECT
        p_date AS '分析日期',
        CONCAT(DATE_FORMAT(v_start_time, '%H:%i'), '-', DATE_FORMAT(v_end_time, '%H:%i')) AS '初始时段',
        CONCAT(DATE_FORMAT(v_next_start_time, '%H:%i'), '-', DATE_FORMAT(v_next_end_time, '%H:%i')) AS '后续时段',
        bs.sell_rate AS '卖出阈值',
        bs.buy_rate AS '买入阈值',
        bs.min_trigger_rest AS '最小触发休息',
        bs.rest_minutes AS '休息分钟数',
        bs.lookback_minutes_buy AS '买入回看分钟',
        bs.lookback_minutes_sell AS '卖出回看分钟',
        bs.currency AS '货币',
        bs.initial_capital AS '初始时段最佳收益',
        (SELECT best_next_capital FROM best_in_next_period) AS '后续时段理论最佳收益',
        (SELECT next_period_capital FROM result_next_period) AS '后续时段实际收益',
        ((SELECT next_period_capital FROM result_next_period) /
         (SELECT best_next_capital FROM best_in_next_period)) * 100 AS '性能比率(%)'
    FROM best_strategy bs;

    -- 清理临时表
    DROP TABLE IF EXISTS best_strategy;
    DROP TABLE IF EXISTS best_in_next_period;
    DROP TABLE IF EXISTS result_next_period;
END //
DELIMITER ;

-- 创建存储结果的表
CREATE TABLE IF NOT EXISTS `okx`.`strategy_performance_analysis` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `analysis_date` DATE COMMENT '分析日期',
    `initial_period_start` DATETIME COMMENT '初始时段开始时间',
    `initial_period_end` DATETIME COMMENT '初始时段结束时间',
    `next_period_start` DATETIME COMMENT '后续时段开始时间',
    `next_period_end` DATETIME COMMENT '后续时段结束时间',
    `sell_rate` DECIMAL(10,6) COMMENT '卖出阈值',
    `buy_rate` DECIMAL(10,6) COMMENT '买入阈值',
    `min_trigger_rest` INT COMMENT '最小触发休息',
    `rest_minutes` INT COMMENT '休息分钟数',
    `lookback_minutes_buy` INT COMMENT '买入回看分钟',
    `lookback_minutes_sell` INT COMMENT '卖出回看分钟',
    `currency` VARCHAR(10) COMMENT '货币',
    `initial_period_best_capital` DECIMAL(15,6) COMMENT '初始时段最佳收益',
    `next_period_best_capital` DECIMAL(15,6) COMMENT '后续时段理论最佳收益',
    `initial_strategy_in_next_period` DECIMAL(15,6) COMMENT '后续时段实际收益',
    `performance_ratio_percent` DECIMAL(10,2) COMMENT '性能比率(%)',
    `created_at` DATETIME COMMENT '创建时间',
    INDEX `idx_analysis_date` (`analysis_date`),
    INDEX `idx_periods` (`initial_period_start`, `next_period_end`)
) COMMENT='策略性能分析结果表';

-- 创建统计全天收益的视图
DROP VIEW IF EXISTS `okx`.`daily_strategy_performance`;
CREATE VIEW `okx`.`daily_strategy_performance` AS
SELECT
    analysis_date AS '日期',
    SUM(initial_strategy_in_next_period - 100) AS '全天累计收益',
    AVG(performance_ratio_percent) AS '平均性能比率(%)',
    COUNT(*) AS '时段数量'
FROM `okx`.`strategy_performance_analysis`
GROUP BY analysis_date
ORDER BY analysis_date DESC;

-- 调用示例：
-- CALL analyze_strategy_performance('2025-03-31', 0, 2);  -- 分析2025-03-31 00:00-01:59的最佳策略在02:00-03:59的表现
-- CALL analyze_strategy_performance('2025-03-31', 2, 2);  -- 分析2025-03-31 02:00-03:59的最佳策略在04:00-05:59的表现
-- CALL analyze_strategy_performance('2025-03-31', 4, 2);  -- 分析2025-03-31 04:00-05:59的最佳策略在06:00-07:59的表现
-- CALL analyze_strategy_performance('2025-03-31', 6, 2);  -- 分析2025-03-31 06:00-07:59的最佳策略在08:00-09:59的表现
-- CALL analyze_strategy_performance('2025-03-31', 8, 2);  -- 分析2025-03-31 08:00-09:59的最佳策略在10:00-11:59的表现
-- CALL analyze_strategy_performance('2025-03-31', 10, 2); -- 分析2025-03-31 10:00-11:59的最佳策略在12:00-13:59的表现
-- CALL analyze_strategy_performance('2025-03-31', 12, 2); -- 分析2025-03-31 12:00-13:59的最佳策略在14:00-15:59的表现
-- CALL analyze_strategy_performance('2025-03-31', 14, 2); -- 分析2025-03-31 14:00-15:59的最佳策略在16:00-17:59的表现
-- CALL analyze_strategy_performance('2025-03-31', 16, 2); -- 分析2025-03-31 16:00-17:59的最佳策略在18:00-19:59的表现
-- CALL analyze_strategy_performance('2025-03-31', 18, 2); -- 分析2025-03-31 18:00-19:59的最佳策略在20:00-21:59的表现
-- CALL analyze_strategy_performance('2025-03-31', 20, 2); -- 分析2025-03-31 20:00-21:59的最佳策略在22:00-23:59的表现

-- 分析一整天的所有时段（每2小时一个时段）
DROP PROCEDURE IF EXISTS analyze_full_day_performance;
DELIMITER //
CREATE PROCEDURE analyze_full_day_performance(IN p_date DATE)
BEGIN
    DECLARE v_hour INT DEFAULT 0;
    DECLARE v_error_count INT DEFAULT 0;

    -- 清除当天的旧数据
    DELETE FROM `okx`.`strategy_performance_analysis` WHERE analysis_date = p_date;

    -- 先分析前一天晚上22:00-24:00的最佳策略，用于预测当天凌晨0:00-2:00的收益
    BEGIN
        DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
        BEGIN
            SET v_error_count = v_error_count + 1;
            SELECT CONCAT('错误: 处理前一天晚上22:00-24:00时段时发生错误') AS error_message;
        END;

        CALL analyze_strategy_performance(DATE_SUB(p_date, INTERVAL 1 DAY), 22, 2);
    END;

    -- 循环分析当天的各个时段
    WHILE v_hour < 22 DO
        BEGIN
            DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
            BEGIN
                SET v_error_count = v_error_count + 1;
                SELECT CONCAT('错误: 处理时段 ', v_hour, ':00 到 ', v_hour+2, ':00 时发生错误') AS error_message;
            END;

            CALL analyze_strategy_performance(p_date, v_hour, 2);
        END;

        SET v_hour = v_hour + 2;
    END WHILE;


    -- 显示当天的汇总结果
    SELECT * FROM `okx`.`daily_strategy_performance` WHERE 日期 = p_date;
END //
DELIMITER ;

-- 调用示例：
CALL analyze_full_day_performance('2025-04-10'); -- 分析2025-03-31整天的策略表现