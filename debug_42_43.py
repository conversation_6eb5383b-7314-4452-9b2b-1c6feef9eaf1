#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
专门调试00:42:00和00:43:00的交易逻辑问题
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def debug_42_43():
    """专门调试00:42:00和00:43:00的交易逻辑问题"""
    
    print("=== 调试00:42:00和00:43:00的交易逻辑问题 ===")
    print()
    
    # 创建策略分析器实例
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 获取00:35:00到00:45:00的数据
    try:
        with analyzer.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT timestamp, open_price as open, close_price as close, 
                       high_price as high, low_price as low
                FROM crypto_prices 
                WHERE currency = 'DOGE' 
                AND timestamp >= '2025-06-06 00:35:00' 
                AND timestamp <= '2025-06-06 00:45:00'
                ORDER BY timestamp
            """)
            results = cursor.fetchall()
            
            if not results:
                print("没有找到数据")
                return
                
            print(f"找到 {len(results)} 条数据")
            print()
            
            # 模拟策略分析器中的涨跌判断逻辑
            minute_candles = []
            
            for i, row in enumerate(results):
                print(f"=== {row['timestamp']} ===")
                
                # 计算涨跌
                open_price = float(row['open'])
                close_price = float(row['close'])
                high_price = float(row['high'])
                low_price = float(row['low'])
                
                # 策略分析器中的涨跌判断逻辑
                if len(minute_candles) > 0:
                    prev_candle = minute_candles[-1]
                    # 上涨：当前最高值和最低值都比上一分钟高
                    if high_price > prev_candle['high'] and low_price > prev_candle['low']:
                        is_up_strategy = True
                    # 下跌：当前最高值和最低值都比上一分钟低
                    elif high_price < prev_candle['high'] and low_price < prev_candle['low']:
                        is_up_strategy = False
                    else:
                        # 横盘或混合情况，暂时按收盘价判断
                        is_up_strategy = close_price > open_price
                else:
                    # 第一条数据，按收盘价判断
                    is_up_strategy = close_price > open_price
                
                print(f"开盘: {open_price:.6f}, 收盘: {close_price:.6f}, 最高: {high_price:.6f}, 最低: {low_price:.6f}")
                print(f"涨跌判断: {'涨' if is_up_strategy else '跌'}")
                
                if len(minute_candles) > 0:
                    prev_candle = minute_candles[-1]
                    print(f"上一分钟: 最高{prev_candle['high']:.6f}, 最低{prev_candle['low']:.6f}")
                    print(f"比较结果: 最高值{'高于' if high_price > prev_candle['high'] else '低于'}上一分钟, 最低值{'高于' if low_price > prev_candle['low'] else '低于'}上一分钟")
                
                # 添加到历史数据
                candle_data = {
                    'timestamp': row['timestamp'],
                    'open': open_price,
                    'close': close_price,
                    'high': high_price,
                    'low': low_price,
                    'is_up': is_up_strategy
                }
                minute_candles.append(candle_data)
                print()
            
            # 分析00:42:00的开仓条件
            print("\n" + "="*60)
            print("分析00:42:00的开仓条件")
            print("="*60)
            
            # 找到00:42:00的索引
            target_index_42 = -1
            for i, candle in enumerate(minute_candles):
                if '00:42:00' in str(candle['timestamp']):
                    target_index_42 = i
                    break
            
            if target_index_42 >= 0:
                print(f"找到00:42:00，索引: {target_index_42}")
                
                # 模拟开仓逻辑：先跌5分钟再涨2分钟
                lookback_buy = 5
                lookback_sell = 2
                total_needed = lookback_buy + lookback_sell - 1  # 6分钟
                
                if target_index_42 >= total_needed:
                    # 获取历史数据（不包含当前K线，模拟策略分析器的逻辑）
                    historical_candles = minute_candles[target_index_42-total_needed:target_index_42]
                    current_candle = minute_candles[target_index_42]
                    
                    # 创建完整序列（历史数据 + 当前K线）
                    all_candles = historical_candles + [current_candle]
                    
                    print(f"\n完整序列（{len(all_candles)}条）:")
                    for j, candle in enumerate(all_candles):
                        trend = "涨" if candle['is_up'] else "跌"
                        print(f"  {j+1}. {candle['timestamp']} - {trend}")
                    
                    # 检查做多条件：先跌5分钟，再涨2分钟
                    down_period = all_candles[:lookback_buy]  # 前5分钟
                    up_period = all_candles[lookback_buy:]    # 后2分钟（包含当前）
                    
                    print(f"\n前5分钟（下跌期）:")
                    for j, candle in enumerate(down_period):
                        trend = "涨" if candle['is_up'] else "跌"
                        print(f"  {j+1}. {candle['timestamp']} - {trend}")
                    
                    print(f"\n后2分钟（上涨期）:")
                    for j, candle in enumerate(up_period):
                        trend = "涨" if candle['is_up'] else "跌"
                        print(f"  {j+1}. {candle['timestamp']} - {trend}")
                    
                    # 检查前期是否连续下跌
                    all_down_in_down_period = all(not candle['is_up'] for candle in down_period)
                    # 检查后期是否连续上涨
                    all_up_in_up_period = all(candle['is_up'] for candle in up_period)
                    
                    print(f"\n前5分钟连续下跌: {all_down_in_down_period}")
                    print(f"后2分钟连续上涨: {all_up_in_up_period}")
                    print(f"满足做多条件: {all_down_in_down_period and all_up_in_up_period}")
                    
                    # 如果满足开多条件，记录后2分钟的涨跌情况
                    if all_down_in_down_period and all_up_in_up_period:
                        print(f"\n✅ 00:42:00 满足开多条件")
                        print(f"后2分钟的涨跌情况:")
                        for j, candle in enumerate(up_period):
                            trend = "涨" if candle['is_up'] else "跌"
                            print(f"  {candle['timestamp']} - {trend}")
                    else:
                        print(f"\n❌ 00:42:00 不满足开多条件")
                else:
                    print(f"历史数据不足，需要{total_needed}条，实际只有{target_index_42}条")
            
            # 分析00:43:00的平仓条件
            print("\n" + "="*60)
            print("分析00:43:00的平仓条件")
            print("="*60)
            
            # 找到00:43:00的索引
            target_index_43 = -1
            for i, candle in enumerate(minute_candles):
                if '00:43:00' in str(candle['timestamp']):
                    target_index_43 = i
                    break
            
            if target_index_43 >= 0:
                print(f"找到00:43:00，索引: {target_index_43}")
                
                # 模拟平仓逻辑：连续2分钟下跌（含当前）
                lookback_sell = 2
                
                if target_index_43 >= lookback_sell - 1:
                    # 获取历史数据（不包含当前K线）+ 当前K线
                    historical_candles = minute_candles[target_index_43-(lookback_sell-1):target_index_43]
                    current_candle = minute_candles[target_index_43]
                    
                    # 创建完整序列（历史数据 + 当前K线）
                    recent_candles = historical_candles + [current_candle]
                    
                    print(f"\n最近2分钟数据:")
                    for j, candle in enumerate(recent_candles):
                        trend = "涨" if candle['is_up'] else "跌"
                        print(f"  {j+1}. {candle['timestamp']} - {trend}")
                    
                    # 检查是否连续下跌
                    all_down = all(not candle['is_up'] for candle in recent_candles)
                    
                    print(f"\n连续2分钟下跌: {all_down}")
                    print(f"满足平多条件: {all_down}")
                    
                    if all_down:
                        print(f"\n✅ 00:43:00 满足平多条件")
                    else:
                        print(f"\n❌ 00:43:00 不满足平多条件")
                        
                        # 分析为什么不满足
                        print(f"详细分析:")
                        for j, candle in enumerate(recent_candles):
                            trend = "涨" if candle['is_up'] else "跌"
                            status = "✅" if not candle['is_up'] else "❌"
                            print(f"  {status} {candle['timestamp']} - {trend}")
                else:
                    print(f"历史数据不足，需要{lookback_sell-1}条历史数据，实际只有{target_index_43}条")
                    
    except Exception as e:
        print(f"调试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_42_43()
