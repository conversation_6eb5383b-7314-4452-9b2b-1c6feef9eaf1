<!DOCTYPE html>
<html>
<head>
    <title>图表控制测试</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
</head>
<body>
    <h2>图表控制功能测试</h2>
    
    <div style="margin: 10px 0;">
        <button onclick="zoomIn()">🔍+ 放大</button>
        <button onclick="zoomOut()">🔍- 缩小</button>
        <button onclick="panLeft()">⬅️ 左移</button>
        <button onclick="panRight()">➡️ 右移</button>
        <button onclick="resetZoom()">🏠 重置</button>
    </div>
    
    <div style="margin: 10px 0;">
        <button onclick="showTimeRange('15m')">15M</button>
        <button onclick="showTimeRange('30m')">30M</button>
        <button onclick="showTimeRange('1h')">1H</button>
        <button onclick="showTimeRange('4h')">4H</button>
        <button onclick="showTimeRange('all')">全部</button>
    </div>
    
    <div id="chart" style="width: 100%; height: 400px;"></div>
    
    <div id="info" style="margin: 10px 0; font-family: monospace; font-size: 12px;"></div>

    <script>
        // 创建测试数据
        const timestamps = [];
        const data = [];
        const startTime = new Date('2025-01-01 00:00:00');
        
        for (let i = 0; i < 1440; i++) { // 24小时，每分钟一个数据点
            const time = new Date(startTime.getTime() + i * 60000);
            const timeStr = time.toISOString().slice(0, 19).replace('T', ' ');
            timestamps.push(timeStr);
            
            const base = 100 + Math.sin(i / 60) * 10; // 基础价格波动
            const noise = (Math.random() - 0.5) * 2; // 随机噪声
            const price = base + noise;
            
            data.push([
                price - 0.5,  // open
                price + 0.5,  // close  
                price - 1,    // low
                price + 1     // high
            ]);
        }

        // 初始化图表
        const chart = echarts.init(document.getElementById('chart'));
        
        const option = {
            xAxis: {
                type: 'category',
                data: timestamps,
                scale: true
            },
            yAxis: {
                scale: true
            },
            dataZoom: [{
                type: 'inside',
                start: 0,
                end: 10,  // 初始显示10%的数据
                minSpan: 0,  // 允许无限放大
                maxSpan: 100
            }, {
                type: 'slider',
                start: 0,
                end: 10,
                minSpan: 0,  // 允许无限放大
                maxSpan: 100
            }],
            series: [{
                type: 'candlestick',
                data: data
            }]
        };
        
        chart.setOption(option);
        
        // 更新信息显示
        function updateInfo() {
            const option = chart.getOption();
            const dataZoom = option.dataZoom[0];
            const range = dataZoom.end - dataZoom.start;
            const center = (dataZoom.start + dataZoom.end) / 2;
            
            document.getElementById('info').innerHTML = `
                当前范围: ${dataZoom.start.toFixed(3)}% - ${dataZoom.end.toFixed(3)}%<br>
                范围大小: ${range.toFixed(3)}%<br>
                中心点: ${center.toFixed(3)}%<br>
                数据点数: ${timestamps.length}<br>
                显示数据点: ${Math.round(timestamps.length * range / 100)}
            `;
        }
        
        // 监听dataZoom事件
        chart.on('dataZoom', updateInfo);
        updateInfo();

        // 控制函数（与主页面相同的逻辑）
        function zoomIn() {
            const option = chart.getOption();
            const dataZoom = option.dataZoom[0];
            const currentRange = dataZoom.end - dataZoom.start;
            const center = (dataZoom.start + dataZoom.end) / 2;
            const zoomFactor = 0.7;
            const newRange = currentRange * zoomFactor;
            const newStart = Math.max(0, center - newRange / 2);
            const newEnd = Math.min(100, center + newRange / 2);
            
            chart.dispatchAction({
                type: 'dataZoom',
                start: newStart,
                end: newEnd
            });
        }
        
        function zoomOut() {
            const option = chart.getOption();
            const dataZoom = option.dataZoom[0];
            const currentRange = dataZoom.end - dataZoom.start;
            const center = (dataZoom.start + dataZoom.end) / 2;
            const zoomFactor = 1.5;
            const newRange = Math.min(100, currentRange * zoomFactor);
            let newStart = center - newRange / 2;
            let newEnd = center + newRange / 2;
            
            if (newStart < 0) {
                newStart = 0;
                newEnd = Math.min(100, newRange);
            }
            if (newEnd > 100) {
                newEnd = 100;
                newStart = Math.max(0, 100 - newRange);
            }
            
            chart.dispatchAction({
                type: 'dataZoom',
                start: newStart,
                end: newEnd
            });
        }
        
        function panLeft() {
            const option = chart.getOption();
            const dataZoom = option.dataZoom[0];
            const range = dataZoom.end - dataZoom.start;
            const step = range;
            const newStart = Math.max(0, dataZoom.start - step);
            const newEnd = newStart + range;
            
            chart.dispatchAction({
                type: 'dataZoom',
                start: newStart,
                end: newEnd
            });
        }
        
        function panRight() {
            const option = chart.getOption();
            const dataZoom = option.dataZoom[0];
            const range = dataZoom.end - dataZoom.start;
            const step = range;
            const newEnd = Math.min(100, dataZoom.end + step);
            const newStart = Math.max(0, newEnd - range);
            
            chart.dispatchAction({
                type: 'dataZoom',
                start: newStart,
                end: newEnd
            });
        }
        
        function resetZoom() {
            chart.dispatchAction({
                type: 'dataZoom',
                start: 0,
                end: 10
            });
        }
        
        function showTimeRange(range) {
            const option = chart.getOption();
            const dataZoom = option.dataZoom[0];
            const currentCenter = (dataZoom.start + dataZoom.end) / 2;
            
            let targetRange = 0;
            switch(range) {
                case '15m': targetRange = 15/1440 * 100; break;  // 15分钟/24小时
                case '30m': targetRange = 30/1440 * 100; break;  // 30分钟/24小时
                case '1h': targetRange = 60/1440 * 100; break;   // 1小时/24小时
                case '4h': targetRange = 240/1440 * 100; break;  // 4小时/24小时
                case 'all': targetRange = 100; break;
            }
            
            let start, end;
            if (range === 'all') {
                start = 0;
                end = 100;
            } else {
                start = Math.max(0, currentCenter - targetRange / 2);
                end = Math.min(100, currentCenter + targetRange / 2);
                
                if (end > 100) {
                    end = 100;
                    start = Math.max(0, end - targetRange);
                }
                if (start < 0) {
                    start = 0;
                    end = Math.min(100, start + targetRange);
                }
            }
            
            chart.dispatchAction({
                type: 'dataZoom',
                start: start,
                end: end
            });
        }
    </script>
</body>
</html>
