#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
边界检查测试脚本
验证所有交易价格都在K线范围内
"""

import pandas as pd
import sys
import os

def test_boundary_check():
    """测试边界检查功能"""
    
    print("=== 边界检查测试 ===")
    print()
    
    # 读取最新的策略日志文件
    log_files = []
    log_dir = "log/strategy"
    
    if os.path.exists(log_dir):
        for file in os.listdir(log_dir):
            if file.startswith("strategy_line_") and file.endswith(".log"):
                log_files.append(os.path.join(log_dir, file))
    
    if not log_files:
        print("❌ 没有找到策略日志文件")
        return False
    
    # 使用最新的日志文件
    latest_log = max(log_files, key=os.path.getmtime)
    print(f"📁 分析文件: {latest_log}")
    print()
    
    try:
        # 读取日志文件
        df = pd.read_csv(latest_log, sep='\t', encoding='utf-8', low_memory=False)

        # 检查列名
        print(f"📋 文件列名: {list(df.columns)}")
        print()

        # 检查是否有实际触发价列
        if '实际触发价' not in df.columns:
            print("⚠️  文件中没有实际触发价列，可能是旧格式的日志文件")
            return True

        # 过滤有交易的记录（实际触发价 > 0）
        trades = df[df['实际触发价'] > 0].copy()
        
        if len(trades) == 0:
            print("⚠️  没有找到交易记录")
            return True
        
        print(f"📊 找到 {len(trades)} 笔交易记录")
        print()
        
        # 检查每笔交易
        all_valid = True
        
        for idx, trade in trades.iterrows():
            timestamp = trade['时间']
            open_price = trade['开盘价']
            close_price = trade['收盘价']
            high_price = trade['最高价']
            low_price = trade['最低价']
            trigger_price = trade['触发价']
            actual_trigger_price = trade['实际触发价']
            protection_price = trade['保护价']
            position = trade['当前仓位']
            
            print(f"🔍 交易 {idx+1}: {timestamp}")
            print(f"   K线范围: [{low_price:.5f}, {high_price:.5f}]")
            print(f"   开盘价: {open_price:.5f}")
            print(f"   收盘价: {close_price:.5f}")
            print(f"   触发价: {trigger_price:.5f}")
            print(f"   实际触发价: {actual_trigger_price:.5f}")
            print(f"   保护价: {protection_price:.5f}")
            print(f"   仓位: {position:.2f}")
            
            # 检查实际触发价是否在K线范围内
            actual_in_range = low_price <= actual_trigger_price <= high_price
            protection_in_range = low_price <= protection_price <= high_price
            trigger_in_range = low_price <= trigger_price <= high_price
            
            print(f"   边界检查:")
            print(f"     实际触发价在范围内: {actual_in_range} {'✅' if actual_in_range else '❌'}")
            print(f"     保护价在范围内: {protection_in_range} {'✅' if protection_in_range else '❌'}")
            print(f"     触发价在范围内: {trigger_in_range} {'✅' if trigger_in_range else '⚠️ (历史价格)'}")
            
            if not actual_in_range:
                print(f"   🚨 错误: 实际触发价 {actual_trigger_price:.5f} 超出K线范围 [{low_price:.5f}, {high_price:.5f}]")
                all_valid = False
            
            if not protection_in_range:
                print(f"   🚨 错误: 保护价 {protection_price:.5f} 超出K线范围 [{low_price:.5f}, {high_price:.5f}]")
                all_valid = False
            
            print()
        
        # 总结
        if all_valid:
            print("🎉 所有交易价格都在K线范围内！边界检查正常工作。")
            return True
        else:
            print("❌ 发现价格超出K线范围的问题！")
            return False
            
    except Exception as e:
        print(f"❌ 读取日志文件时出错: {e}")
        return False

def run_test_with_strategy():
    """运行策略并测试边界检查"""
    
    print("=== 运行策略测试 ===")
    print()
    
    # 运行策略分析
    import subprocess
    
    cmd = [
        "python", "strategy_analyzer.py",
        "--start", "2025-04-10 02:18:00",
        "--end", "2025-04-10 02:32:00", 
        "--sell-rate", "1",
        "--buy-rate", "1",
        "--rest", "0",
        "--min-trigger-rest", "0",
        "--lookback-minutes-buy", "2",
        "--lookback-minutes-sell", "5",
        "--price-adjust-rate", "0.001"
    ]
    
    print("🚀 运行策略分析...")
    result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
    
    if result.returncode != 0:
        print(f"❌ 策略分析失败: {result.stderr}")
        return False
    
    print("✅ 策略分析完成")
    print()
    
    # 测试边界检查
    return test_boundary_check()

if __name__ == "__main__":
    success = run_test_with_strategy()
    sys.exit(0 if success else 1)
