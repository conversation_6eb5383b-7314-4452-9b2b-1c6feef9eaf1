from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor

from datetime import datetime
import sys
import traceback
from dotenv import load_dotenv
import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header


# 加载环境变量
load_dotenv()

# OKX API配置
API_CONFIG = {
    'api_key': os.getenv('OKX_API_KEY'),
    'secret_key': os.getenv('OKX_SECRET_KEY'),
    'passphrase': os.getenv('OKX_PASSPHRASE'),
    'is_simulated': os.getenv('OKX_SIMULATED', '1') == '1'  # 默认使用模拟盘
}

# MySQL配置
MYSQL_HOST = os.getenv('MYSQL_HOST', 'localhost')
MYSQL_USER = os.getenv('MYSQL_USER', 'root')
MYSQL_PASSWORD = os.getenv('MYSQL_PASSWORD', '')
MYSQL_DATABASE = os.getenv('MYSQL_DATABASE', 'crypto_data')
MYSQL_PORT = int(os.getenv('MYSQL_PORT', 3306))

# 统一API配置（删除旧配置）
API_KEY = API_CONFIG['api_key']
API_SECRET = API_CONFIG['secret_key']  # 原环境变量名与代码不匹配
API_PASSPHRASE = API_CONFIG['passphrase']


# 邮件配置
EMAIL_CONFIG = {
    'smtp_server': os.getenv('SMTP_SERVER', 'smtp.qq.com'),
    'smtp_port': int(os.getenv('SMTP_PORT', 587)),
    'sender_email': os.getenv('SENDER_EMAIL'),
    'sender_password': os.getenv('SENDER_PASSWORD'),
    'default_to_email': os.getenv('DEFAULT_TO_EMAIL')
}



def print_log(message):
    """打印日志"""
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
    if not hasattr(print_log, 'first_call_time'):
        print_log.first_call_time = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")

    # 获取当前运行的脚本文件名（不带后缀）
    current_script = os.path.basename(os.path.splitext(sys.argv[0])[0])
    log_dir = os.path.join(os.path.dirname(__file__), './log')
    os.makedirs(log_dir, exist_ok=True)
    with open(os.path.join(log_dir, f'{current_script}_{print_log.first_call_time}-{os.getpid()}.log'), 'a', encoding='utf-8') as log_file:
        print(f"{current_time} - {message}", file=log_file)
    print(f"{current_time} - {message}")



def send_email(subject, content, to_email=None):
    """
    发送邮件通知
    :param subject: 邮件主题
    :param content: 邮件内容
    :param to_email: 收件人邮箱，如果为None则使用配置文件中的默认邮箱
    """
    try:
        # 从配置文件获取邮件设置
        smtp_server = EMAIL_CONFIG.get('smtp_server', 'smtp.qq.com')
        smtp_port = EMAIL_CONFIG.get('smtp_port', 587)
        sender_email = EMAIL_CONFIG.get('sender_email')
        sender_password = EMAIL_CONFIG.get('sender_password')
        default_to_email = EMAIL_CONFIG.get('default_to_email')
        
        if not sender_email or not sender_password:
            print_log("邮件配置不完整，无法发送邮件")
            return
            
        # 设置收件人
        to_email = to_email or default_to_email
        if not to_email:
            print_log("未指定收件人邮箱，无法发送邮件")
            return
            
        # 创建邮件对象
        msg = MIMEMultipart()
        msg['From'] = sender_email
        msg['To'] = to_email
        msg['Subject'] = Header(str(subject), 'utf-8')
        
        # 添加邮件正文
        msg.attach(MIMEText(str(content), 'plain', 'utf-8'))
        
        # 直接发送邮件
        try:
            with smtplib.SMTP(smtp_server, smtp_port, timeout=10) as server:
                server.login(sender_email, sender_password)
                server.send_message(msg)
                print_log(f"邮件发送成功: {subject}")
        except TimeoutError:
            print_log("邮件发送超时")
        except Exception as e:
            error_msg = traceback.format_exc()
            print_log(f"发送邮件时出错: [{str(e)}]\n错误详情:\n{error_msg}")
        
    except Exception as e:
        error_msg = traceback.format_exc()
        print_log(f"发送邮件时出错: [{str(e)}]\n错误详情:\n{error_msg}")