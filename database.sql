/*
 Navicat Premium Data Transfer

 Source Server         : wsl
 Source Server Type    : MySQL
 Source Server Version : 80035
 Source Host           : ************:3306
 Source Schema         : okx

 Target Server Type    : MySQL
 Target Server Version : 80035
 File Encoding         : 65001

 Date: 22/01/2025 18:25:58
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for crypto_prices
-- ----------------------------
DROP TABLE IF EXISTS `crypto_prices`;
CREATE TABLE `crypto_prices`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `currency` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `timestamp` datetime NULL DEFAULT NULL,
  `open_price` decimal(20, 8) NULL DEFAULT NULL,
  `high_price` decimal(20, 8) NULL DEFAULT NULL,
  `low_price` decimal(20, 8) NULL DEFAULT NULL,
  `close_price` decimal(20, 8) NULL DEFAULT NULL,
  `volume` decimal(20, 8) NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `volume_currency` decimal(20, 8) NOT NULL DEFAULT 0.00000000,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_currency_timestamp`(`currency` ASC, `timestamp` ASC) USING BTREE,
  INDEX `idx_crypto_prices_currency_timestamp`(`currency` ASC, `timestamp` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1572345 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for strategy_results
-- ----------------------------
DROP TABLE IF EXISTS `strategy_results`;
CREATE TABLE `strategy_results`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `strategy_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '策略名称',
  `strategy_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '策略类型',
  `start_time` datetime NULL DEFAULT NULL COMMENT '分析开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '分析结束时间',
  `currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '币种',
  `long_rebound_rate` decimal(10, 4) NULL DEFAULT NULL COMMENT '做多反弹买入阈值',
  `long_pullback_rate` decimal(10, 4) NULL DEFAULT NULL COMMENT '做多回落卖出阈值',
  `initial_capital` decimal(20, 8) NULL DEFAULT NULL COMMENT '初始资金',
  `final_capital` decimal(20, 8) NULL DEFAULT NULL COMMENT '最终资金',
  `total_trades` int NULL DEFAULT NULL COMMENT '总交易次数',
  `successful_trades` int NULL DEFAULT NULL COMMENT '盈利交易次数',
  `total_profit` decimal(20, 8) NULL DEFAULT NULL COMMENT '总盈亏金额',
  `total_fees` decimal(20, 8) NULL DEFAULT NULL COMMENT '总手续费',
  `roi` decimal(10, 4) NULL DEFAULT NULL COMMENT '投资回报率（ROI）',
  `trade_log` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '交易日志',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '结果记录时间',
  `rest_minutes` int NULL DEFAULT 0,
  `highest_account_value` decimal(20, 8) NULL DEFAULT NULL COMMENT '最高账户价值',
  `lowest_account_value` decimal(20, 8) NULL DEFAULT NULL COMMENT '最低账户价值',
  `account_value_history` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '账户价值历史记录(JSON)',
  `min_trigger_rest` int NULL DEFAULT 0 COMMENT '最低触发止损时间',
  `lookback_minutes_buy` int NULL DEFAULT 4,
  `lookback_minutes_sell` int NULL DEFAULT 4,
  `short_rebound_rate` decimal(10, 4) NULL DEFAULT NULL COMMENT '做空反弹卖出阈值',
  `short_pullback_rate` decimal(10, 4) NULL DEFAULT NULL COMMENT '做空回落买入阈值',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_strategy_results_final_capital`(`final_capital` DESC) USING BTREE,
  INDEX `idx_strategy_results_combined`(`start_time` ASC, `end_time` ASC, `long_rebound_rate` ASC, `long_pullback_rate` ASC, `rest_minutes` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3413 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '策略分析结果表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for strategy_trades
-- ----------------------------
DROP TABLE IF EXISTS `strategy_trades`;
CREATE TABLE `strategy_trades`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `strategy_id` int NOT NULL COMMENT '策略ID',
  `timestamp` datetime NOT NULL COMMENT '交易时间',
  `action` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '交易动作',
  `position_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '持仓类型(long/short)',
  `price` decimal(20, 8) NOT NULL COMMENT '交易价格',
  `amount` decimal(20, 8) NOT NULL COMMENT '交易数量',
  `entry_price` decimal(20, 8) NULL DEFAULT NULL COMMENT '入场价格',
  `position_size` decimal(20, 8) NULL DEFAULT NULL COMMENT '持仓量',
  `capital_before` decimal(20, 8) NULL DEFAULT NULL COMMENT '交易前资金',
  `capital_after` decimal(20, 8) NULL DEFAULT NULL COMMENT '交易后资金',
  `fee` decimal(20, 8) NOT NULL COMMENT '交易手续费',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易原因',
  `profit_amount` decimal(20, 8) NULL DEFAULT NULL COMMENT '交易盈亏金额',
  `profit_percentage` decimal(10, 4) NULL DEFAULT NULL COMMENT '交易盈亏百分比',
  `trigger_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '触发原因',
  `market_price` decimal(20, 8) NULL DEFAULT NULL COMMENT '市场价格',
  `account_value` decimal(20, 8) NULL DEFAULT NULL COMMENT '账户价值',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `rest_minutes` int NULL DEFAULT 0,
  `currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'DOGE' COMMENT '币种',
  `last_high` decimal(20, 8) NULL DEFAULT NULL COMMENT '最高价格',
  `last_low` decimal(20, 8) NULL DEFAULT NULL COMMENT '最低价格',
  `min_trigger_price` decimal(20, 8) NOT NULL COMMENT '最低卖出触发价格',
  `min_trigger_rest` int NULL DEFAULT 0 COMMENT '最低触发止损时间',
  `lookback_minutes` int NULL DEFAULT 4,
  `long_rebound_rate` decimal(10, 4) NULL DEFAULT NULL COMMENT '做多反弹买入阈值',
  `long_pullback_rate` decimal(10, 4) NULL DEFAULT NULL COMMENT '做多回落卖出阈值',
  `short_rebound_rate` decimal(10, 4) NULL DEFAULT NULL COMMENT '做空反弹卖出阈值',
  `short_pullback_rate` decimal(10, 4) NULL DEFAULT NULL COMMENT '做空回落买入阈值',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `strategy_id`(`strategy_id` ASC) USING BTREE COMMENT '策略ID索引',
  INDEX `timestamp`(`timestamp` ASC) USING BTREE COMMENT '时间戳索引',
  INDEX `idx_strategy_trades_timestamp`(`timestamp` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3394165 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '策略交易记录表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
