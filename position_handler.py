import logging
from datetime import datetime, timedelta
from okx_api_handler import okx_api

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_current_position(inst_id="DOGE-USDT-SWAP", currency="DOGE"):
    """
    从OKX API获取当前持仓信息

    Args:
        inst_id: 产品ID，如 "DOGE-USDT-SWAP"
        currency: 币种，如 "DOGE"

    Returns:
        dict: 持仓信息
    """
    try:
        # 获取持仓信息
        position_result = okx_api.get_positions(instId=inst_id)

        # 获取当前市场价格
        ticker_result = okx_api.get_ticker(instId=inst_id)

        if position_result.get('code') != '0' or ticker_result.get('code') != '0':
            logger.error(f"获取持仓或价格信息失败: {position_result.get('msg')} / {ticker_result.get('msg')}")
            return None

        # 提取持仓数据
        position_data = position_result.get('data', [{}])[0]
        ticker_data = ticker_result.get('data', [{}])[0]

        # 提取市场价格
        market_price = float(ticker_data.get('last', '0'))

        # 确定持仓类型
        position_size = float(position_data.get('pos', '0'))
        if position_size > 0:
            position_type = 'long'
        elif position_size < 0:
            position_type = 'short'
            position_size = abs(position_size)  # 转为正数存储
        else:
            position_type = 'none'

        # 准备持仓记录
        position_record = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'currency': currency,
            'position_type': position_type,
            'position_size': position_size,
            'entry_price': float(position_data.get('avgPx', '0')),
            'market_price': market_price,
            'unrealized_pnl': float(position_data.get('upl', '0')),
            'leverage': float(position_data.get('lever', '1')),
            'margin_mode': position_data.get('mgnMode', 'cross'),
            'liquidation_price': float(position_data.get('liqPx', '0')),
            'status': 'active'
        }

        logger.info(f"获取到当前持仓: {position_record}")
        return position_record

    except Exception as e:
        logger.error(f"获取持仓数据时出错: {str(e)}")
        return None

def get_position_history(start_time, end_time, currency="DOGE", inst_id="DOGE-USDT-SWAP"):
    """
    获取指定时间范围内的持仓历史数据

    由于OKX API不提供历史持仓数据的直接查询，我们使用历史订单来推断持仓状态

    Args:
        start_time: 开始时间，格式为 'YYYY-MM-DD HH:MM:SS'
        end_time: 结束时间，格式为 'YYYY-MM-DD HH:MM:SS'
        currency: 币种，如 "DOGE"
        inst_id: 产品ID，如 "DOGE-USDT-SWAP"

    Returns:
        dict: 以分钟为键，包含该分钟内所有持仓记录的字典
    """
    try:
        # 获取历史订单
        start_timestamp = int(datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S').timestamp() * 1000)
        end_timestamp = int(datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S').timestamp() * 1000)

        # 获取历史订单
        orders_result = okx_api.get_history_orders(
            instId=inst_id,
            begin=str(start_timestamp),
            end=str(end_timestamp),
            instType="SWAP"
        )

        if orders_result.get('code') != '0':
            logger.error(f"获取历史订单失败: {orders_result.get('msg')}")
            return {}

        orders = orders_result.get('data', [])
        logger.info(f"获取到 {len(orders)} 条历史订单记录")

        # 根据订单推断持仓状态
        position_data = {}
        current_position = {
            'position_type': 'none',
            'position_size': 0,
            'entry_price': 0,
            'market_price': 0,
            'unrealized_pnl': 0,
            'leverage': 1,
            'margin_mode': 'cross',
            'liquidation_price': 0,
            'status': 'active'
        }

        # 获取当前持仓作为最新状态
        current = get_current_position(inst_id, currency)
        if current:
            current_position = {
                'position_type': current['position_type'],
                'position_size': current['position_size'],
                'entry_price': current['entry_price'],
                'market_price': current['market_price'],
                'unrealized_pnl': current['unrealized_pnl'],
                'leverage': current['leverage'],
                'margin_mode': current['margin_mode'],
                'liquidation_price': current['liquidation_price'],
                'status': current['status']
            }

        # 按时间排序订单（从新到旧）
        orders.sort(key=lambda x: int(x.get('cTime', 0)), reverse=True)

        # 从当前时间开始，每分钟记录一次持仓状态
        current_time = datetime.now()
        end_dt = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
        if end_dt > current_time:
            end_dt = current_time

        start_dt = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')

        # 从结束时间向前推，每分钟记录一次持仓状态
        minute = end_dt
        while minute >= start_dt:
            minute_str = minute.strftime('%Y-%m-%d %H:%M:00')
            minute_ts = int(minute.timestamp() * 1000)

            # 找到该分钟之前的最后一个订单
            position_changed = False
            for order in orders:
                order_time = int(order.get('cTime', 0))
                if order_time <= minute_ts:
                    # 更新持仓状态
                    side = order.get('side', '')
                    pos_side = order.get('posSide', '')
                    size = float(order.get('sz', 0))
                    price = float(order.get('avgPx', 0))

                    if side == 'buy' and pos_side == 'long':
                        # 开多仓
                        current_position['position_type'] = 'long'
                        current_position['position_size'] = size
                        current_position['entry_price'] = price
                        position_changed = True
                    elif side == 'sell' and pos_side == 'long':
                        # 平多仓
                        current_position['position_type'] = 'none'
                        current_position['position_size'] = 0
                        current_position['entry_price'] = 0
                        position_changed = True
                    elif side == 'sell' and pos_side == 'short':
                        # 开空仓
                        current_position['position_type'] = 'short'
                        current_position['position_size'] = size
                        current_position['entry_price'] = price
                        position_changed = True
                    elif side == 'buy' and pos_side == 'short':
                        # 平空仓
                        current_position['position_type'] = 'none'
                        current_position['position_size'] = 0
                        current_position['entry_price'] = 0
                        position_changed = True

                    if position_changed:
                        break

            # 记录该分钟的持仓状态
            if minute_str not in position_data:
                position_data[minute_str] = []

            position_data[minute_str].append({
                'timestamp': minute_str,
                'currency': currency,
                'position_type': current_position['position_type'],
                'position_size': current_position['position_size'],
                'entry_price': current_position['entry_price'],
                'market_price': current_position['market_price'],
                'unrealized_pnl': current_position['unrealized_pnl'],
                'leverage': current_position['leverage'],
                'margin_mode': current_position['margin_mode'],
                'liquidation_price': current_position['liquidation_price'],
                'status': current_position['status']
            })

            # 向前推一分钟
            minute -= timedelta(minutes=1)

        return position_data

    except Exception as e:
        logger.error(f"获取持仓历史数据时出错: {str(e)}")
        return {}

# 添加一个获取实时持仓的函数，直接返回当前持仓状态
def get_realtime_position(inst_id="DOGE-USDT-SWAP", currency="DOGE"):
    """
    获取实时持仓数据，格式化为前端需要的格式

    Args:
        inst_id: 产品ID，如 "DOGE-USDT-SWAP"
        currency: 币种，如 "DOGE"

    Returns:
        dict: 持仓数据，格式为 {timestamp: [position_data]}
    """
    try:
        position = get_current_position(inst_id, currency)
        if not position:
            return {}

        # 格式化为前端需要的格式
        minute_ts = position['timestamp'][:17] + '00'
        return {
            minute_ts: [position]
        }
    except Exception as e:
        logger.error(f"获取实时持仓数据时出错: {str(e)}")
        return {}

if __name__ == "__main__":
    # 测试获取当前持仓
    position = get_current_position()
    print(f"当前持仓: {position}")

    # 测试获取历史持仓
    history = get_position_history(
        start_time='2023-01-01 00:00:00',
        end_time='2023-12-31 23:59:59'
    )
    print(f"历史持仓数量: {len(history)}")
