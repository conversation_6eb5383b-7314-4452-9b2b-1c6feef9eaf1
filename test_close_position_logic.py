#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试平多平空逻辑的价格调整功能
"""

def test_close_position_price_adjustment():
    """测试平仓价格调整逻辑"""
    
    print("=== 测试平多价格调整逻辑 ===")
    
    # 测试场景1：平多 - 现价超过触发价很多
    trigger_price = 100.0
    current_low = 99.0  # 跌破触发价1%
    
    exceed_ratio = (trigger_price - current_low) / trigger_price
    print(f"触发价: {trigger_price}")
    print(f"当前最低价: {current_low}")
    print(f"超过比例: {exceed_ratio*100:.2f}%")
    
    if exceed_ratio > 0.001:  # 超过0.1%
        actual_trigger_price = current_low * (1 - 0.001)  # 再下跌0.1%
        print(f"调整后成交价: {actual_trigger_price:.5f} (再下跌0.1%)")
    else:
        actual_trigger_price = trigger_price
        print(f"成交价: {actual_trigger_price:.5f} (使用原触发价)")
    
    print()
    
    # 测试场景2：平多 - 现价刚好达到触发价
    trigger_price = 100.0
    current_low = 99.95  # 刚好跌破触发价0.05%
    
    exceed_ratio = (trigger_price - current_low) / trigger_price
    print(f"触发价: {trigger_price}")
    print(f"当前最低价: {current_low}")
    print(f"超过比例: {exceed_ratio*100:.2f}%")
    
    if exceed_ratio > 0.001:  # 超过0.1%
        actual_trigger_price = current_low * (1 - 0.001)  # 再下跌0.1%
        print(f"调整后成交价: {actual_trigger_price:.5f} (再下跌0.1%)")
    else:
        actual_trigger_price = trigger_price
        print(f"成交价: {actual_trigger_price:.5f} (使用原触发价)")
    
    print("\n=== 测试平空价格调整逻辑 ===")
    
    # 测试场景3：平空 - 现价超过触发价很多
    trigger_price = 100.0
    current_high = 101.0  # 突破触发价1%
    
    exceed_ratio = (current_high - trigger_price) / trigger_price
    print(f"触发价: {trigger_price}")
    print(f"当前最高价: {current_high}")
    print(f"超过比例: {exceed_ratio*100:.2f}%")
    
    if exceed_ratio > 0.001:  # 超过0.1%
        actual_trigger_price = current_high * (1 + 0.001)  # 再上涨0.1%
        print(f"调整后成交价: {actual_trigger_price:.5f} (再上涨0.1%)")
    else:
        actual_trigger_price = trigger_price
        print(f"成交价: {actual_trigger_price:.5f} (使用原触发价)")
    
    print()
    
    # 测试场景4：平空 - 现价刚好达到触发价
    trigger_price = 100.0
    current_high = 100.05  # 刚好突破触发价0.05%
    
    exceed_ratio = (current_high - trigger_price) / trigger_price
    print(f"触发价: {trigger_price}")
    print(f"当前最高价: {current_high}")
    print(f"超过比例: {exceed_ratio*100:.2f}%")
    
    if exceed_ratio > 0.001:  # 超过0.1%
        actual_trigger_price = current_high * (1 + 0.001)  # 再上涨0.1%
        print(f"调整后成交价: {actual_trigger_price:.5f} (再上涨0.1%)")
    else:
        actual_trigger_price = trigger_price
        print(f"成交价: {actual_trigger_price:.5f} (使用原触发价)")

if __name__ == "__main__":
    test_close_position_price_adjustment()
