# 加密货币交易策略分析系统

## 系统概述

这是一个专门用于分析加密货币（目前专注于DOGE）交易策略的自动化系统。系统采用基于价格回弹的交易策略，通过参数优化寻找最佳交易时机。

### 核心特性

- 自动化数据收集和分析
- 可配置的交易策略参数
- 详细的交易记录和性能分析
- 多参数组合测试
- 精确的财务计算（使用Decimal）
- 完整的交易日志记录

## 系统架构

### 文件结构

- `strategy_analyzer.py`: 核心策略分析器
  - 实现交易策略逻辑
  - 参数优化和回测
  - 交易记录和性能统计
  - 支持多线程并行分析

- `doge_price_fetcher.py`: 价格数据获取器
  - 从OKX交易所获取DOGE价格数据
  - 处理和规范化价格数据
  - 数据存储到MySQL数据库

- `app.py`: Web应用服务器
  - 提供Web界面展示分析结果
  - 处理用户交互和参数配置
  - 实时展示策略分析进度

- `database.sql`: 数据库结构
  - 价格数据表结构
  - 交易记录表结构
  - 策略结果表结构

- `templates/index.html`: Web界面模板
  - 策略参数配置界面
  - 分析结果展示
  - 交易记录可视化

### 数据库结构

系统使用MySQL数据库存储数据，主要包含以下表：

1. `crypto_prices`: 通用加密货币价格表
   - 支持多币种价格数据存储
   - 包含开高低收、成交量等信息
   - 使用币种和时间戳联合唯一索引

2. `doge_prices`: DOGE专用价格表
   - 存储DOGE-USDT的分钟级价格数据
   - 包含完整的K线信息
   - 用于策略回测和分析

3. `strategy_results`: 策略分析结果表
   - 记录每次策略运行的参数配置
   - 存储策略执行的性能指标
   - 包含ROI、交易次数、成功率等数据

4. `strategy_trades`: 交易记录表
   - 记录每笔具体的交易信息
   - 存储交易价格、数量、手续费等
   - 包含交易原因和触发条件

### 交易策略逻辑

1. 买入条件：
   - 跟踪局部最低价
   - 当价格从局部最低点反弹超过设定阈值时买入（可配置1%-10%）
   - 使用全仓位交易策略

2. 卖出条件：
   - 跟踪买入后的最高价
   - 当价格从最高点回落超过设定阈值时卖出（可配置1%-10%）
   - 不设止损，纯粹基于回落幅度执行

3. 交易执行：
   - 考虑交易手续费（0.1%）
   - 使用Decimal保证计算精度
   - 详细记录每笔交易原因

### 性能指标

系统记录和分析以下指标：
- 总交易次数
- 盈利交易次数
- 总收益率
- 单笔最大盈利/亏损
- 总手续费支出
- 持仓时间统计

## 运行配置

### 环境要求

```
Python 3.11+
MySQL 8.0+
依赖包：见requirements.txt
```

### 数据库配置

```sql
数据库名：trading_bot
主要表：
- price_data: 存储价格数据
- strategy_results: 存储策略分析结果
- trades: 存储交易记录
```

### 运行参数

```bash
python strategy_analyzer.py [参数]
--days: 分析的天数
--start-date: 开始日期（YYYY-MM-DD）
--end-date: 结束日期（YYYY-MM-DD）
--threads: 并行线程数（默认1）
```

## 未来规划

1. 策略增强：
   - 添加更多技术指标
   - 支持多币种分析
   - 实现实时交易执行

2. 系统优化：
   - 改进数据库查询性能
   - 添加更多可视化图表
   - 实现策略回测报告导出

3. 风险管理：
   - 添加资金管理模块
   - 实现风险评估指标
   - 优化持仓时间管理

## 维护说明

- 定期更新价格数据
- 监控数据库性能
- 备份策略分析结果
- 记录系统改进建议
