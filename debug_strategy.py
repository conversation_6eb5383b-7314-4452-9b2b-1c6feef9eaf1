#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试策略分析器 - 专门调试00:42:00的开仓逻辑
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def debug_specific_time():
    """调试特定时间点的策略逻辑"""
    
    print("=== 调试00:42:00的开仓逻辑 ===")
    print()
    
    # 创建策略分析器实例
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 获取00:35:00到00:45:00的数据
    try:
        with analyzer.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT timestamp, open_price as open, close_price as close, 
                       high_price as high, low_price as low
                FROM crypto_prices 
                WHERE currency = 'DOGE' 
                AND timestamp >= '2025-06-06 00:35:00' 
                AND timestamp <= '2025-06-06 00:45:00'
                ORDER BY timestamp
            """)
            results = cursor.fetchall()
            
            if not results:
                print("没有找到数据")
                return
                
            print(f"找到 {len(results)} 条数据")
            print()
            
            # 模拟策略分析器的完整逻辑
            minute_candles = []
            
            # 策略参数
            lookback_minutes_buy = 5
            lookback_minutes_sell = 2
            buy_rate = 1.0
            
            for i, row in enumerate(results):
                timestamp = row['timestamp']
                open_price = float(row['open'])
                close_price = float(row['close'])
                high_price = float(row['high'])
                low_price = float(row['low'])
                
                print(f"=== {timestamp} ===")
                print(f"开盘: {open_price:.6f}, 收盘: {close_price:.6f}")
                print(f"最高: {high_price:.6f}, 最低: {low_price:.6f}")
                
                # 计算涨跌（模拟策略分析器的逻辑）
                if len(minute_candles) > 0:
                    prev_candle = minute_candles[-1]
                    print(f"上一分钟: 最高{prev_candle['high']:.6f}, 最低{prev_candle['low']:.6f}")
                    
                    # 上涨：当前最高值和最低值都比上一分钟高
                    if high_price > prev_candle['high'] and low_price > prev_candle['low']:
                        is_up = True
                        reason = "最高值和最低值都比上一分钟高"
                    # 下跌：当前最高值和最低值都比上一分钟低
                    elif high_price < prev_candle['high'] and low_price < prev_candle['low']:
                        is_up = False
                        reason = "最高值和最低值都比上一分钟低"
                    else:
                        # 横盘或混合情况，按收盘价判断
                        is_up = close_price > open_price
                        reason = f"横盘或混合情况，按收盘价判断: {close_price:.6f} {'>' if is_up else '<='} {open_price:.6f}"
                    
                    print(f"涨跌判断: {'涨' if is_up else '跌'} ({reason})")
                else:
                    # 第一条数据，按收盘价判断
                    is_up = close_price > open_price
                    print(f"涨跌判断: {'涨' if is_up else '跌'} (第一条数据，按收盘价判断)")
                
                # 添加到历史数据（模拟策略分析器的逻辑：先添加再判断）
                candle_data = {
                    'timestamp': timestamp,
                    'open': open_price,
                    'close': close_price,
                    'high': high_price,
                    'low': low_price,
                    'is_up': is_up
                }
                minute_candles.append(candle_data)
                
                # 保持最近20条数据（模拟策略分析器的逻辑）
                if len(minute_candles) > 20:
                    minute_candles = minute_candles[-20:]
                
                print(f"历史K线数量: {len(minute_candles)}")
                
                # 如果是00:42:00，详细调试analyze_correct_consecutive_moves函数
                print(f"检查时间戳: '{timestamp}' == '2025-06-06 00:42:00': {timestamp == '2025-06-06 00:42:00'}")
                if timestamp == '2025-06-06 00:42:00' or '00:42:00' in str(timestamp):
                    print(f"\n🔍 详细调试00:42:00的analyze_correct_consecutive_moves函数")
                    
                    # 模拟analyze_correct_consecutive_moves函数的逻辑
                    candles = minute_candles[:-1]  # 不包含当前K线的历史数据
                    current_row = {
                        'timestamp': timestamp,
                        'open': open_price,
                        'close': close_price,
                        'high': high_price,
                        'low': low_price
                    }
                    
                    print(f"传入analyze_correct_consecutive_moves的参数:")
                    print(f"  candles数量: {len(candles)}")
                    print(f"  lookback_sell: {lookback_minutes_sell}")
                    print(f"  lookback_buy: {lookback_minutes_buy}")
                    print(f"  buy_rate: {buy_rate}")
                    
                    # 🔧 最终修正：需要足够的历史数据：lookback_buy + lookback_sell - 1
                    total_needed = lookback_minutes_buy + lookback_minutes_sell - 1
                    print(f"  total_needed: {total_needed}")
                    print(f"  len(candles): {len(candles)}")
                    
                    if len(candles) < total_needed:
                        print(f"❌ 历史数据不足，返回None")
                        print("-" * 80)
                        print()
                        continue
                    
                    # 获取当前K线数据
                    current_open = float(current_row['open'])
                    current_close = float(current_row['close'])
                    current_high = float(current_row['high'])
                    current_low = float(current_row['low'])
                    
                    # 使用新的涨跌判断标准：基于最高值和最低值的比较
                    if len(candles) > 0:
                        prev_candle = candles[-1]
                        # 上涨：当前最高值和最低值都比上一分钟高
                        if current_high > prev_candle['high'] and current_low > prev_candle['low']:
                            current_is_up = True
                        # 下跌：当前最高值和最低值都比上一分钟低
                        elif current_high < prev_candle['high'] and current_low < prev_candle['low']:
                            current_is_up = False
                        else:
                            # 横盘或混合情况，暂时按收盘价判断
                            current_is_up = current_close > current_open
                    else:
                        # 第一条数据，按收盘价判断
                        current_is_up = current_close > current_open
                    
                    print(f"  当前K线涨跌: {'涨' if current_is_up else '跌'}")
                    
                    # 创建包含当前K线的完整序列
                    current_candle = {
                        'timestamp': current_row['timestamp'],
                        'open': current_open,
                        'close': current_close,
                        'high': current_high,
                        'low': current_low,
                        'is_up': current_is_up
                    }
                    
                    # 🔧 最终修正：使用历史数据 + 当前K线，但确保有足够的历史数据
                    all_candles = candles[-total_needed:] + [current_candle]
                    print(f"  all_candles数量: {len(all_candles)}")
                    
                    # 检查做多条件：先跌lookback_buy分钟，再涨lookback_sell分钟
                    down_period = all_candles[:lookback_minutes_buy]  # 前lookback_buy分钟
                    up_period = all_candles[lookback_minutes_buy:]    # 后lookback_sell分钟（包含当前）
                    
                    print(f"  down_period数量: {len(down_period)}")
                    print(f"  up_period数量: {len(up_period)}")
                    
                    # 🔧 最终修正：确保后期有足够的数据
                    if len(up_period) < lookback_minutes_sell:
                        print(f"❌ 后期数据不足: 需要{lookback_minutes_sell}分钟，实际只有{len(up_period)}分钟")
                        continue
                    
                    print(f"\n前{lookback_minutes_buy}分钟（下跌期）:")
                    for j, candle in enumerate(down_period):
                        trend = "涨" if candle['is_up'] else "跌"
                        status = "✅" if not candle['is_up'] else "❌"
                        print(f"  {status} {j+1}. {candle['timestamp']} - {trend}")
                    
                    print(f"\n后{lookback_minutes_sell}分钟（上涨期）:")
                    for j, candle in enumerate(up_period):
                        trend = "涨" if candle['is_up'] else "跌"
                        status = "✅" if candle['is_up'] else "❌"
                        print(f"  {status} {j+1}. {candle['timestamp']} - {trend}")
                    
                    # 检查前期是否连续下跌
                    all_down_in_down_period = all(not candle['is_up'] for candle in down_period)
                    # 检查后期是否连续上涨
                    all_up_in_up_period = all(candle['is_up'] for candle in up_period)
                    
                    print(f"\n前{lookback_minutes_buy}分钟连续下跌: {all_down_in_down_period}")
                    print(f"后{lookback_minutes_sell}分钟连续上涨: {all_up_in_up_period}")
                    
                    if all_down_in_down_period and all_up_in_up_period:
                        # 找到下跌期间的最低点
                        lowest_candle = min(down_period, key=lambda x: x['low'])
                        
                        # 计算做多触发价格
                        if buy_rate == 1:
                            long_trigger_price = lowest_candle['high']
                        else:
                            price_range = lowest_candle['high'] - lowest_candle['low']
                            long_trigger_price = lowest_candle['low'] + (price_range * buy_rate)
                        
                        print(f"最低点: {lowest_candle['timestamp']} - 最低价: {lowest_candle['low']:.6f}, 最高价: {lowest_candle['high']:.6f}")
                        print(f"触发价格: {long_trigger_price:.6f}")
                        print(f"当前最高价: {current_high:.6f}")
                        
                        # 检查当前价格是否达到触发条件
                        if current_high >= long_trigger_price:
                            print(f"✅ 满足做多条件，开仓！")
                        else:
                            print(f"❌ 价格未达到触发条件")
                    else:
                        print(f"❌ 不满足做多条件")
                
                print("-" * 80)
                print()
                    
    except Exception as e:
        print(f"分析过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_specific_time()
