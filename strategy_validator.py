import argparse
import pymysql
import pandas as pd
import traceback
from datetime import datetime
from db_config import DB_CONFIG

def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(
        host=DB_CONFIG['host'],
        user=DB_CONFIG['user'],
        password=DB_CONFIG['password'],
        database=DB_CONFIG['database'],
        port=DB_CONFIG['port'],
        cursorclass=pymysql.cursors.DictCursor
    )

def get_strategy_data(strategy_id):
    """根据策略ID获取策略的K线数据和交易信息"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            # 获取策略的交易记录
            trade_sql = """
            SELECT *
            FROM strategy_trades
            WHERE strategy_id = %s
            ORDER BY timestamp ASC
            """
            cursor.execute(trade_sql, (strategy_id,))
            trades = cursor.fetchall()
            
            # 检查是否有交易记录
            if not trades:
                print(f"错误: 未找到策略ID {strategy_id} 的交易记录")
                return None, None
                
            # 获取币种信息
            currency = trades[0].get('currency', 'DOGE')  # 默认使用DOGE
            
            # 获取策略的K线数据
            kline_sql = """
            SELECT *
            FROM crypto_prices
            WHERE currency = %s
            AND timestamp >= (SELECT MIN(timestamp) FROM strategy_trades WHERE strategy_id = %s)
            AND timestamp <= (SELECT MAX(timestamp) FROM strategy_trades WHERE strategy_id = %s)
            ORDER BY timestamp ASC
            """
            cursor.execute(kline_sql, (currency, strategy_id, strategy_id))
            kline_data = cursor.fetchall()
            
            if not kline_data:
                print(f"错误: 未找到币种 {currency} 在策略ID {strategy_id} 的时间范围内的K线数据")
            
            return trades, kline_data
    except Exception as e:
        print(f"获取数据时发生错误: {str(e)}")
        traceback.print_exc()
        return None, None

def validate_trades(trades, kline_data):
    """验证交易的准确性"""
    # 检查交易记录和K线数据是否存在
    if not trades or not kline_data:
        print("错误: 没有交易记录或K线数据可供验证")
        return
        
    kline_df = pd.DataFrame(kline_data)
    kline_df['timestamp'] = pd.to_datetime(kline_df['timestamp'])
    
    previous_buy_price = None
    total_profit = 0
    total_fees = 0
    account_value_before = 0
    account_value_after = trades[0]['capital_before']

    print(f"开始验证 {len(trades)} 笔交易记录...")
    
    for trade in trades:
        timestamp = pd.to_datetime(trade['timestamp'])
        action = trade['action']
        price = trade['price']
        amount = trade['amount']
        fee = trade['fee']
        capital_before = trade['capital_before']
        capital_after = trade['capital_after']
        profit = trade['profit_amount']
        position_size = trade['position_size']
        position_type = trade.get('position_type', '')
        
        # 检查买入卖出价格是否符合K线数据
        kline_row = kline_df[(kline_df['timestamp'] == timestamp)]
        if not kline_row.empty:
            kline_open = kline_row['open_price'].values[0]
            kline_close = kline_row['close_price'].values[0]
            kline_high = kline_row['high_price'].values[0]
            kline_low = kline_row['low_price'].values[0]

            if action == 'BUY':
                if price < kline_low or price > kline_high:
                    print(f"错误: 买入价格 {price} 不在K线范围内 {kline_low} - {kline_high} 时间: {timestamp}")
                previous_buy_price = price

                total_fees += -abs(fee)
                


            elif action == 'SELL':
                if price < kline_low or price > kline_high:
                    print(f"错误: 卖出价格 {price} 不在K线范围内 {kline_low} - {kline_high} 时间: {timestamp}")
                if previous_buy_price is None:
                    print(f"错误: 卖出订单没有对应的买入订单 时间: {timestamp}")
                else:
                    calculated_profit = 0
                    if position_type == 'close_long':
                        calculated_profit = (price - previous_buy_price) * position_size
                    elif position_type == 'close_short':
                        calculated_profit = (previous_buy_price - price) * position_size
                    else:
                        print(f"警告: 未知的仓位类型 {position_type}, 无法计算理论利润")
                        calculated_profit = profit  # 使用记录的利润作为理论值

                    if abs(round(calculated_profit, 3) - round(profit, 3)) > 0.001:
                        print(f"错误: 计算的利润 {calculated_profit:.3f} 与记录的利润 {profit:.3f} 不匹配，数量: {position_size:.3f} 时间: {timestamp}")

                    total_profit += profit
                    total_fees += -abs(fee)
        else:
            print(f"错误: 找不到对应的K线记录 时间: {timestamp}")

        if trade['capital_before'] != account_value_after and abs(trade['capital_before'] - account_value_after) > 0.001:
            print(f"错误: 账户价值前不等于上一笔交易后的价值: 预期 {account_value_after:.3f} 实际 {trade['capital_before']:.3f} 时间: {timestamp}")
        
        # 账户价值对比
        account_value_before = capital_before
        account_value_after = capital_after
        expected_account_value_after = account_value_before + profit + (-abs(fee))
        
        if abs(round(account_value_after, 3) - round(expected_account_value_after, 3)) > 0.001:
            print(f"错误: 账户价值不匹配: 预期 {expected_account_value_after:.3f} 实际 {account_value_after:.3f} 时间: {timestamp}")

    print(f"验证完成! 总利润: {total_profit:.2f} 总手续费: {total_fees:.2f}")

def main(strategy_id):
    """主函数，验证指定策略ID的交易"""
    print(f"开始验证策略ID {strategy_id} 的交易数据...")
    
    # 获取策略数据
    print(f"正在获取策略 {strategy_id} 的交易记录和K线数据...")
    trades, kline_data = get_strategy_data(strategy_id)
    
    if trades is None or kline_data is None:
        print(f"错误: 未能获取策略 {strategy_id} 的完整数据，验证终止")
        return
    
    if not trades:
        print(f"错误: 策略 {strategy_id} 没有任何交易记录")
        return
        
    if not kline_data:
        print(f"错误: 策略 {strategy_id} 的交易时间范围内没有K线数据")
        return
    
    print(f"成功获取 {len(trades)} 笔交易记录和 {len(kline_data)} 条K线数据")
    
    # 验证交易
    print(f"开始验证交易准确性...")
    validate_trades(trades, kline_data)
    
    print(f"策略 {strategy_id} 验证完成!")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="策略验证器")
    parser.add_argument("--strategy-id", type=int, required=False, help="策略ID")
    try:
        args = parser.parse_args()
        if args.strategy_id is None:
            # 自动获取最新一条策略的ID
            def get_latest_strategy_id():
                """获取最新的策略ID"""
                try:
                    with get_db_connection() as conn:
                        with conn.cursor() as cursor:
                            cursor.execute("SELECT MAX(id) FROM strategy_results")
                            result = cursor.fetchone()
                            return result['MAX(id)'] if result and result['MAX(id)'] else None
                except Exception as e:
                    print(f"获取最新策略ID时发生错误: {str(e)}")
                    return None
                    
            latest_strategy_id = get_latest_strategy_id()
            print(f"自动获取最新策略ID: {latest_strategy_id}")
            main(latest_strategy_id)
        else:
            print(f"准备验证策略ID: {args.strategy_id}")
            main(args.strategy_id)
    except Exception as e:
        print(f"程序执行过程中发生错误: {str(e)}")
        traceback.print_exc()
