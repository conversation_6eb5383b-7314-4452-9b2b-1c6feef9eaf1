import pandas as pd
import os

# 检查最新的策略日志文件
log_file = 'log/strategy/strategy_line_892810_live_0.log'

print(f'检查文件: {log_file}')

try:
    # 读取文件
    df = pd.read_csv(log_file, sep='\t', encoding='utf-8', low_memory=False)
    print(f'总行数: {len(df)}')
    
    # 转换数据类型
    df['实际触发价'] = pd.to_numeric(df['实际触发价'], errors='coerce')
    df['最高价'] = pd.to_numeric(df['最高价'], errors='coerce')
    df['最低价'] = pd.to_numeric(df['最低价'], errors='coerce')
    df['保护价'] = pd.to_numeric(df['保护价'], errors='coerce')
    
    # 过滤有交易的记录
    trades = df[df['实际触发价'] > 0].copy()
    print(f'总交易记录: {len(trades)}')
    
    if len(trades) == 0:
        print('没有交易记录')
        exit()
    
    # 检查边界违规
    violations = []
    for idx, trade in trades.iterrows():
        low_price = trade['最低价']
        high_price = trade['最高价']
        actual_trigger_price = trade['实际触发价']
        protection_price = trade['保护价']
        
        # 检查实际触发价是否在范围内
        actual_in_range = low_price <= actual_trigger_price <= high_price
        protection_in_range = low_price <= protection_price <= high_price
        
        if not actual_in_range:
            violations.append({
                'timestamp': trade['时间'],
                'type': '实际触发价',
                'price': actual_trigger_price,
                'low': low_price,
                'high': high_price
            })
        
        if not protection_in_range:
            violations.append({
                'timestamp': trade['时间'],
                'type': '保护价', 
                'price': protection_price,
                'low': low_price,
                'high': high_price
            })
    
    print(f'边界违规数量: {len(violations)}')
    
    if len(violations) == 0:
        print('🎉 所有交易价格都在K线范围内！边界检查修复成功！')
        
        # 显示一些示例
        print()
        print('示例交易记录（最后5笔）:')
        for idx, trade in trades.tail(5).iterrows():
            timestamp = trade['时间']
            low_price = trade['最低价']
            high_price = trade['最高价']
            actual_trigger_price = trade['实际触发价']
            protection_price = trade['保护价']
            
            print(f'  {timestamp}')
            print(f'    K线范围: [{low_price:.5f}, {high_price:.5f}]')
            print(f'    实际触发价: {actual_trigger_price:.5f} ✅')
            print(f'    保护价: {protection_price:.5f} ✅')
            print()
    else:
        print(f'❌ 发现 {len(violations)} 个边界违规问题:')
        for violation in violations[:10]:  # 只显示前10个
            print(f'  🚨 {violation["timestamp"]}')
            print(f'     {violation["type"]}: {violation["price"]:.5f}')
            print(f'     K线范围: [{violation["low"]:.5f}, {violation["high"]:.5f}]')
            print()
        
        if len(violations) > 10:
            print(f'  ... 还有 {len(violations) - 10} 个违规')

except Exception as e:
    print(f'错误: {e}')
    import traceback
    traceback.print_exc()
