@echo off
echo 自定义策略分析工具
echo ==================

set /p START_DATE="输入开始日期 (格式: YYYY-MM-DD, 默认: 2025-04-22): "
if "%START_DATE%"=="" set START_DATE=2025-04-22

set /p END_DATE="输入结束日期 (格式: YYYY-MM-DD, 默认: 2025-04-22): "
if "%END_DATE%"=="" set END_DATE=2025-04-22

set /p START_TIME="输入开始时间 (格式: HH:MM:SS, 默认: 00:00:00): "
if "%START_TIME%"=="" set START_TIME=00:00:00

set /p END_TIME="输入结束时间 (格式: HH:MM:SS, 默认: 23:59:59): "
if "%END_TIME%"=="" set END_TIME=23:59:59

set /p BUY_RATE="输入买入阈值 (默认: 0.006): "
if "%BUY_RATE%"=="" set BUY_RATE=0.006

set /p SELL_RATE="输入卖出阈值 (默认: 0.004): "
if "%SELL_RATE%"=="" set SELL_RATE=0.004

set /p CURRENCY="输入币种 (默认: DOGE): "
if "%CURRENCY%"=="" set CURRENCY=DOGE

set /p LOOKBACK_BUY="输入买入回看分钟数 (默认: 5): "
if "%LOOKBACK_BUY%"=="" set LOOKBACK_BUY=5

set /p LOOKBACK_SELL="输入卖出回看分钟数 (默认: 3): "
if "%LOOKBACK_SELL%"=="" set LOOKBACK_SELL=3

set /p MIN_TRIGGER_REST="输入最小触发休息时间 (默认: 40): "
if "%MIN_TRIGGER_REST%"=="" set MIN_TRIGGER_REST=40

set /p REST="输入休息时间 (默认: 0): "
if "%REST%"=="" set REST=0

echo.
echo 将使用以下参数执行策略分析:
echo 时间范围: %START_DATE% %START_TIME% 至 %END_DATE% %END_TIME%
echo 买入阈值: %BUY_RATE%
echo 卖出阈值: %SELL_RATE%
echo 币种: %CURRENCY%
echo 买入回看分钟数: %LOOKBACK_BUY%
echo 卖出回看分钟数: %LOOKBACK_SELL%
echo 最小触发休息时间: %MIN_TRIGGER_REST%
echo 休息时间: %REST%
echo.

set /p CONFIRM="确认执行? (Y/N): "
if /i not "%CONFIRM%"=="Y" goto :end

echo.
echo 开始执行策略分析...
python strategy_analyzer.py --start "%START_DATE% %START_TIME%" --end "%END_DATE% %END_TIME%" --buy-rate %BUY_RATE% --sell-rate %SELL_RATE% --currency %CURRENCY% --lookback-minutes-buy %LOOKBACK_BUY% --lookback-minutes-sell %LOOKBACK_SELL% --min-trigger-rest %MIN_TRIGGER_REST% --rest %REST%
echo 策略分析完成！

:end
pause
