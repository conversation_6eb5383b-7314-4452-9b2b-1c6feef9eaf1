import pandas as pd
from datetime import datetime, timedelta

# 深度分析2025-06-01 22:02:00这笔交易的触发逻辑
log_file = 'log/strategy/strategy_line_892810_live_0.log'

print('=== 深度分析 2025-06-01 22:02:00 交易触发逻辑 ===')
print()

try:
    # 读取文件
    df = pd.read_csv(log_file, sep='\t', encoding='utf-8', low_memory=False)
    
    # 跳过标题行
    if df.iloc[0]['时间'] == 'timestamp':
        df = df.iloc[1:].reset_index(drop=True)
    
    # 转换时间列和数值列
    df['时间'] = pd.to_datetime(df['时间'])
    numeric_columns = ['开盘价', '收盘价', '最高价', '最低价', '触发价', '实际触发价', '保护价', '当前仓位', '连续上升', '连续下跌']
    for col in numeric_columns:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 找到交易时间
    trade_time = datetime(2025, 6, 1, 22, 2, 0)
    trade_row = df[df['时间'] == trade_time].iloc[0]
    
    print('📊 交易详情:')
    print(f'   时间: {trade_time}')
    print(f'   开盘价: {trade_row["开盘价"]:.5f}')
    print(f'   收盘价: {trade_row["收盘价"]:.5f}')
    print(f'   最高价: {trade_row["最高价"]:.5f}')
    print(f'   最低价: {trade_row["最低价"]:.5f}')
    print(f'   触发价: {trade_row["触发价"]:.5f}')
    print(f'   实际触发价: {trade_row["实际触发价"]:.5f}')
    print(f'   保护价: {trade_row["保护价"]:.5f}')
    print(f'   连续上升: {trade_row["连续上升"]}')
    print(f'   连续下跌: {trade_row["连续下跌"]}')
    print(f'   当前仓位: {trade_row["当前仓位"]:.2f}')
    print()
    
    # 分析触发逻辑
    print('🔍 触发逻辑分析:')
    
    # 获取交易前的K线数据（用于分析策略条件）
    before_trade = df[df['时间'] < trade_time].tail(10)
    
    print('   交易前10个K线:')
    for _, row in before_trade.iterrows():
        timestamp = row['时间']
        open_price = row['开盘价']
        close_price = row['收盘价']
        high_price = row['最高价']
        low_price = row['最低价']
        up_count = row['连续上升']
        down_count = row['连续下跌']
        
        trend = "↑" if close_price > open_price else "↓" if close_price < open_price else "→"
        print(f'     {timestamp}: 开{open_price:.5f} 收{close_price:.5f} 高{high_price:.5f} 低{low_price:.5f} {trend} 上{up_count}下{down_count}')
    
    print()
    
    # 分析价格计算逻辑
    print('💰 价格计算分析:')
    
    trigger_price = trade_row["触发价"]  # 0.18892
    actual_trigger_price = trade_row["实际触发价"]  # 0.18948
    protection_price = trade_row["保护价"]  # 0.18937
    open_price = trade_row["开盘价"]  # 0.18937
    high_price = trade_row["最高价"]  # 0.18948
    low_price = trade_row["最低价"]  # 0.18929
    
    print(f'   触发价: {trigger_price:.5f} (来自历史K线)')
    print(f'   开盘价: {open_price:.5f}')
    print(f'   保护价: {protection_price:.5f} (等于开盘价)')
    print(f'   实际触发价: {actual_trigger_price:.5f} (等于最高价)')
    print(f'   K线范围: [{low_price:.5f}, {high_price:.5f}]')
    print()
    
    # 分析价格调整逻辑
    exceed_ratio = (high_price - trigger_price) / trigger_price
    print(f'   价格超出比例: {exceed_ratio*100:.2f}%')
    
    if exceed_ratio > 0.001:  # 0.1%
        potential_trigger_price = open_price * (1 + 0.001)
        print(f'   计算的潜在触发价: 开盘价 × 1.001 = {open_price:.5f} × 1.001 = {potential_trigger_price:.5f}')
        
        # 边界检查
        bounded_price = max(low_price, min(high_price, potential_trigger_price))
        print(f'   边界检查: max({low_price:.5f}, min({high_price:.5f}, {potential_trigger_price:.5f})) = {bounded_price:.5f}')
        
        if bounded_price == actual_trigger_price:
            print('   ✅ 价格计算逻辑正确')
        else:
            print(f'   ❌ 价格计算有误，期望{bounded_price:.5f}，实际{actual_trigger_price:.5f}')
    
    print()
    
    # 分析交易类型
    print('📈 交易类型分析:')
    
    # 检查前一个K线的仓位
    prev_position = before_trade.iloc[-1]['当前仓位']
    current_position = trade_row['当前仓位']
    
    print(f'   交易前仓位: {prev_position:.2f}')
    print(f'   交易后仓位: {current_position:.2f}')
    
    if prev_position == 0 and current_position > 0:
        print('   交易类型: 做多开仓')
    elif prev_position == 0 and current_position < 0:
        print('   交易类型: 做空开仓')
    elif prev_position > 0 and current_position == 0:
        print('   交易类型: 平多')
    elif prev_position < 0 and current_position == 0:
        print('   交易类型: 平空')
    elif prev_position > 0 and current_position > prev_position:
        print('   交易类型: 做多加仓')
    elif prev_position < 0 and current_position < prev_position:
        print('   交易类型: 做空加仓')
    else:
        print('   交易类型: 其他')
    
    print()
    
    # 分析策略条件
    print('⚙️ 策略条件分析:')
    
    up_count = trade_row['连续上升']
    down_count = trade_row['连续下跌']
    
    print(f'   连续上升: {up_count}')
    print(f'   连续下跌: {down_count}')
    
    # 根据策略参数判断
    # 从日志可以看出：买入回看时间4分钟，卖出回看时间3分钟
    if down_count >= 4 and up_count >= 3:
        print('   ✅ 满足做多开仓条件 (4跌3涨)')
    elif up_count >= 4 and down_count >= 3:
        print('   ✅ 满足做空开仓条件 (4涨3跌)')
    else:
        print(f'   ⚠️ 不满足标准开仓条件')
    
    print()
    
    # 总结
    print('📋 总结:')
    print('   1. 这是一笔做多开仓交易')
    print('   2. 触发价来自历史K线，不在当前K线范围内（正常）')
    print('   3. 由于现价超过触发价，使用开盘价+0.1%作为成交价')
    print('   4. 经过边界检查，最终成交价为最高价（在K线范围内）')
    print('   5. 保护价设为开盘价（在K线范围内）')
    print('   6. 所有价格都正确地限制在K线范围内 ✅')

except Exception as e:
    print(f'错误: {e}')
    import traceback
    traceback.print_exc()
