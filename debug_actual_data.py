#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分析策略分析器在00:42:00时实际使用的数据
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def debug_actual_data():
    """分析策略分析器在00:42:00时实际使用的数据"""
    
    print("=== 分析策略分析器在00:42:00时实际使用的数据 ===")
    print()
    
    # 创建策略分析器实例
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 获取00:00:00到00:45:00的数据
    try:
        with analyzer.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT timestamp, open_price as open, close_price as close, 
                       high_price as high, low_price as low
                FROM crypto_prices 
                WHERE currency = 'DOGE' 
                AND timestamp >= '2025-06-06 00:00:00' 
                AND timestamp <= '2025-06-06 00:45:00'
                ORDER BY timestamp
            """)
            results = cursor.fetchall()
            
            if not results:
                print("没有找到数据")
                return
                
            print(f"找到 {len(results)} 条数据")
            print()
            
            # 模拟策略分析器的逻辑，构建minute_candles
            minute_candles = []
            
            for i, row in enumerate(results):
                timestamp = row['timestamp']
                open_price = float(row['open'])
                close_price = float(row['close'])
                high_price = float(row['high'])
                low_price = float(row['low'])
                
                # 模拟策略分析器中的涨跌判断逻辑
                if len(minute_candles) > 0:
                    prev_candle = minute_candles[-1]
                    # 上涨：当前最高值和最低值都比上一分钟高
                    if high_price > prev_candle['high'] and low_price > prev_candle['low']:
                        is_up = True
                    # 下跌：当前最高值和最低值都比上一分钟低
                    elif high_price < prev_candle['high'] and low_price < prev_candle['low']:
                        is_up = False
                    else:
                        # 横盘或混合情况，暂时按收盘价判断
                        is_up = close_price > open_price
                else:
                    # 第一条数据，按收盘价判断
                    is_up = close_price > open_price
                
                # 如果是00:42:00，分析实际使用的数据
                if '00:42:00' in str(timestamp):
                    print(f"=== 00:42:00时的分析 ===")
                    print(f"当前时间: {timestamp}")
                    print(f"历史K线数量: {len(minute_candles)}")
                    
                    # 模拟analyze_correct_consecutive_moves函数的逻辑
                    lookback_buy = 5
                    lookback_sell = 2
                    total_needed = lookback_buy + lookback_sell - 1  # 6分钟
                    
                    if len(minute_candles) >= total_needed:
                        # 创建当前K线数据
                        current_candle = {
                            'timestamp': timestamp,
                            'open': open_price,
                            'close': close_price,
                            'high': high_price,
                            'low': low_price,
                            'is_up': is_up
                        }
                        
                        # 获取历史数据 + 当前数据（模拟策略分析器的逻辑）
                        historical_candles = minute_candles[-total_needed:]
                        all_candles = historical_candles + [current_candle]
                        
                        print(f"\n策略分析器实际使用的7分钟数据:")
                        for j, candle in enumerate(all_candles):
                            trend = "涨" if candle['is_up'] else "跌"
                            print(f"  {j+1}. {candle['timestamp']} - {trend} (开:{candle['open']:.6f}, 收:{candle['close']:.6f}, 高:{candle['high']:.6f}, 低:{candle['low']:.6f})")
                        
                        # 检查做多条件：先跌5分钟，再涨2分钟
                        down_period = all_candles[:lookback_buy]  # 前5分钟
                        up_period = all_candles[lookback_buy:]    # 后2分钟（包含当前）
                        
                        print(f"\n前5分钟（下跌期）:")
                        for j, candle in enumerate(down_period):
                            trend = "涨" if candle['is_up'] else "跌"
                            status = "✅" if not candle['is_up'] else "❌"
                            print(f"  {status} {j+1}. {candle['timestamp']} - {trend}")
                        
                        print(f"\n后2分钟（上涨期）:")
                        for j, candle in enumerate(up_period):
                            trend = "涨" if candle['is_up'] else "跌"
                            status = "✅" if candle['is_up'] else "❌"
                            print(f"  {status} {j+1}. {candle['timestamp']} - {trend}")
                        
                        # 检查前期是否连续下跌
                        all_down_in_down_period = all(not candle['is_up'] for candle in down_period)
                        # 检查后期是否连续上涨
                        all_up_in_up_period = all(candle['is_up'] for candle in up_period)
                        
                        print(f"\n前5分钟连续下跌: {all_down_in_down_period}")
                        print(f"后2分钟连续上涨: {all_up_in_up_period}")
                        print(f"满足做多条件: {all_down_in_down_period and all_up_in_up_period}")
                        
                        if all_down_in_down_period and all_up_in_up_period:
                            # 找到下跌期间的最低点
                            lowest_candle = min(down_period, key=lambda x: x['low'])
                            print(f"\n最低点K线: {lowest_candle['timestamp']} - 最低价: {lowest_candle['low']:.6f}, 最高价: {lowest_candle['high']:.6f}")
                            
                            # 计算做多触发价格
                            buy_rate = 1.0
                            if buy_rate == 1:
                                long_trigger_price = lowest_candle['high']
                            else:
                                price_range = lowest_candle['high'] - lowest_candle['low']
                                long_trigger_price = lowest_candle['low'] + (price_range * buy_rate)
                            
                            print(f"做多触发价格: {long_trigger_price:.6f}")
                            print(f"当前最高价: {current_candle['high']:.6f}")
                            print(f"价格达到触发条件: {current_candle['high'] >= long_trigger_price}")
                            
                            print(f"\n✅ 00:42:00 满足开多条件")
                        else:
                            print(f"\n❌ 00:42:00 不满足开多条件")
                    else:
                        print(f"历史数据不足，需要{total_needed}条，实际只有{len(minute_candles)}条")
                    
                    break  # 只分析00:42:00，然后退出
                
                # 添加到历史数据
                candle_data = {
                    'timestamp': timestamp,
                    'open': open_price,
                    'close': close_price,
                    'high': high_price,
                    'low': low_price,
                    'is_up': is_up
                }
                minute_candles.append(candle_data)
                    
    except Exception as e:
        print(f"调试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_actual_data()
