# 新策略命令行工具使用说明

## 概述

这是一个基于连续分钟K线涨跌模式的新交易策略分析工具。策略的核心思想是：
- **开仓条件**：先连续下跌N分钟，再连续上涨M分钟，价格达到触发点
- **平仓条件**：连续涨跌M分钟即可平仓

## 文件说明

- `test_new_strategy_cli.py` - 命令行测试工具
- `simple_test.py` - 简化版测试工具
- `run_test.bat` - Windows批处理测试脚本
- `run_test.sh` - Linux/Mac测试脚本

## 使用方法

### 基本命令

```bash
python test_new_strategy_cli.py --start "2025-06-06 00:00:00" --end "2025-06-06 23:59:59" --buy_rate 1.0 --lookback_buy 5 --lookback_sell 2
```

### 参数说明

#### 必需参数
- `--start` - 开始时间，格式: "YYYY-MM-DD HH:MM:SS"
- `--end` - 结束时间，格式: "YYYY-MM-DD HH:MM:SS"

#### 策略参数
- `--buy_rate` - 买入倍率 (默认: 1.0)
  - 1.0: 价格需要涨到最低K线的最高值
  - 0.5: 价格需要涨到最低K线价格区间的50%
  - 2.0: 价格需要涨到最低K线价格区间的200%
- `--sell_rate` - 卖出倍率 (默认: 0.02)
- `--rest` - 休息时间(分钟) (默认: 0)
- `--lookback_buy` - 连续下跌分钟数 (默认: 5)
- `--lookback_sell` - 连续上涨分钟数 (默认: 2)

#### 其他参数
- `--currency` - 交易币种 (默认: DOGE)
- `--clear` - 清除之前的分析结果
- `--threads` - 线程数 (默认: 1)
- `--split-by-day` - 按天分割分析

## 使用示例

### 示例1: 基本参数测试
```bash
python test_new_strategy_cli.py --start "2025-06-06 00:00:00" --end "2025-06-06 23:59:59" --buy_rate 1.0 --lookback_buy 5 --lookback_sell 2
```

### 示例2: 不同参数组合
```bash
python test_new_strategy_cli.py --start "2025-06-06 00:00:00" --end "2025-06-06 23:59:59" --buy_rate 0.5 --lookback_buy 3 --lookback_sell 3
```

### 示例3: 使用批处理脚本
```bash
# Windows
run_test.bat

# Linux/Mac
chmod +x run_test.sh
./run_test.sh
```

## 策略逻辑说明

### 开仓逻辑
1. **连续下跌期**：需要连续N分钟下跌（lookback_buy参数）
2. **连续上涨期**：需要连续M分钟上涨（lookback_sell参数）
3. **价格触发**：当前价格需要达到下跌期最低K线的触发价格

### 平仓逻辑
1. **多仓平仓**：连续M分钟下跌即可平多
2. **空仓平仓**：连续M分钟上涨即可平空

### 涨跌判断标准
- **上涨**：当前最高值和最低值都比上一分钟高
- **下跌**：当前最高值和最低值都比上一分钟低
- **横盘**：其他情况，按收盘价与开盘价比较判断

## 输出结果说明

### 分析结果
- **初始资金**: 起始资金（默认$100）
- **最终资金**: 分析结束时的资金
- **总收益**: 绝对收益金额
- **收益率**: 百分比收益率
- **交易次数**: 总交易次数
- **总手续费**: 累计手续费

### 交易记录
每条交易记录包含：
- **时间戳**: 交易发生时间
- **操作**: BUY/SELL/开多/开空/平多/平空
- **价格**: 交易价格
- **收益**: 该笔交易的收益
- **原因**: 触发交易的具体原因

## 策略优势

1. **精确的趋势识别**：基于连续分钟K线的涨跌模式
2. **减少假信号**：需要满足连续涨跌条件才触发
3. **更好的风控**：使用关键价位进行止损
4. **适应性强**：可以通过调整lookback参数适应不同市场

## 注意事项

1. **数据要求**：需要完整的分钟级K线数据
2. **参数调优**：不同市场条件下需要调整参数
3. **风险控制**：策略仅供参考，实际交易需要考虑更多因素
4. **回测局限**：历史表现不代表未来收益

## 故障排除

### 常见问题
1. **没有找到价格数据**：检查时间范围和币种是否正确
2. **数据库连接错误**：检查db_config.py配置
3. **参数错误**：检查命令行参数格式

### 调试模式
如需开启调试信息，可以在strategy_analyzer.py中取消注释调试代码：
```python
# 调试信息（可选）
if '00:42:00' in str(current_row['timestamp']):
    print(f"DEBUG: 00:42:00 - down_period: {[c['timestamp'] + ' ' + ('涨' if c['is_up'] else '跌') for c in down_period]}")
    print(f"DEBUG: 00:42:00 - up_period: {[c['timestamp'] + ' ' + ('涨' if c['is_up'] else '跌') for c in up_period]}")
    print(f"DEBUG: 00:42:00 - all_down_in_down_period: {all_down_in_down_period}")
    print(f"DEBUG: 00:42:00 - all_up_in_up_period: {all_up_in_up_period}")
```

## 更新日志

### v1.0 (2025-06-09)
- 实现基于连续涨跌模式的新策略
- 修正时间序列处理逻辑
- 添加命令行工具
- 完善调试功能
