#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
详细分析00:43:00的平仓逻辑
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def debug_43_close():
    """详细分析00:43:00的平仓逻辑"""
    
    print("=== 详细分析00:43:00的平仓逻辑 ===")
    print()
    
    # 创建策略分析器实例
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 获取00:40:00到00:45:00的数据
    try:
        with analyzer.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT timestamp, open_price as open, close_price as close, 
                       high_price as high, low_price as low
                FROM crypto_prices 
                WHERE currency = 'DOGE' 
                AND timestamp >= '2025-06-06 00:40:00' 
                AND timestamp <= '2025-06-06 00:45:00'
                ORDER BY timestamp
            """)
            results = cursor.fetchall()
            
            if not results:
                print("没有找到数据")
                return
                
            print(f"找到 {len(results)} 条数据")
            print()
            
            # 分析每分钟的涨跌情况
            minute_candles = []
            
            for i, row in enumerate(results):
                timestamp = row['timestamp']
                open_price = float(row['open'])
                close_price = float(row['close'])
                high_price = float(row['high'])
                low_price = float(row['low'])
                
                print(f"=== {timestamp} ===")
                print(f"开盘: {open_price:.6f}, 收盘: {close_price:.6f}")
                print(f"最高: {high_price:.6f}, 最低: {low_price:.6f}")
                
                # 模拟策略分析器中的涨跌判断逻辑
                if len(minute_candles) > 0:
                    prev_candle = minute_candles[-1]
                    print(f"上一分钟: 最高{prev_candle['high']:.6f}, 最低{prev_candle['low']:.6f}")
                    
                    # 上涨：当前最高值和最低值都比上一分钟高
                    if high_price > prev_candle['high'] and low_price > prev_candle['low']:
                        is_up = True
                        reason = "最高值和最低值都比上一分钟高"
                    # 下跌：当前最高值和最低值都比上一分钟低
                    elif high_price < prev_candle['high'] and low_price < prev_candle['low']:
                        is_up = False
                        reason = "最高值和最低值都比上一分钟低"
                    else:
                        # 横盘或混合情况，暂时按收盘价判断
                        is_up = close_price > open_price
                        reason = f"横盘或混合情况，按收盘价判断: {close_price:.6f} {'>' if is_up else '<='} {open_price:.6f}"
                    
                    print(f"涨跌判断: {'涨' if is_up else '跌'} ({reason})")
                else:
                    # 第一条数据，按收盘价判断
                    is_up = close_price > open_price
                    print(f"涨跌判断: {'涨' if is_up else '跌'} (第一条数据，按收盘价判断)")
                
                # 添加到历史数据
                candle_data = {
                    'timestamp': timestamp,
                    'open': open_price,
                    'close': close_price,
                    'high': high_price,
                    'low': low_price,
                    'is_up': is_up
                }
                minute_candles.append(candle_data)
                print()
            
            print("="*60)
            print("涨跌情况汇总:")
            for candle in minute_candles:
                trend = "涨" if candle['is_up'] else "跌"
                print(f"  {candle['timestamp']} - {trend}")
            
            print("\n" + "="*60)
            print("分析00:43:00的平仓条件:")
            print("平多条件：连续2分钟下跌（含当前）")
            
            # 找到00:43:00
            target_candle = None
            target_index = -1
            for i, candle in enumerate(minute_candles):
                if '00:43:00' in str(candle['timestamp']):
                    target_candle = candle
                    target_index = i
                    break
            
            if target_candle:
                print(f"\n找到00:43:00，索引: {target_index}")
                print(f"00:43:00涨跌: {'涨' if target_candle['is_up'] else '跌'}")
                
                # 检查连续2分钟下跌
                if target_index >= 1:
                    prev_candle = minute_candles[target_index - 1]
                    print(f"00:42:00涨跌: {'涨' if prev_candle['is_up'] else '跌'}")
                    
                    # 检查是否连续2分钟下跌
                    recent_2_minutes = [prev_candle, target_candle]
                    all_down = all(not candle['is_up'] for candle in recent_2_minutes)
                    
                    print(f"\n连续2分钟下跌检查:")
                    for j, candle in enumerate(recent_2_minutes):
                        trend = "涨" if candle['is_up'] else "跌"
                        status = "✅" if not candle['is_up'] else "❌"
                        print(f"  {status} {candle['timestamp']} - {trend}")
                    
                    print(f"\n连续2分钟下跌: {all_down}")
                    print(f"满足平多条件: {all_down}")
                    
                    if all_down:
                        print(f"\n✅ 00:43:00 应该触发平多")
                    else:
                        print(f"\n❌ 00:43:00 不应该触发平多")
                        print(f"原因: 00:42:00是涨，00:43:00是{'涨' if target_candle['is_up'] else '跌'}，不满足连续2分钟下跌")
                else:
                    print("没有足够的历史数据进行连续2分钟检查")
            else:
                print("没有找到00:43:00的数据")
                    
    except Exception as e:
        print(f"调试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_43_close()
