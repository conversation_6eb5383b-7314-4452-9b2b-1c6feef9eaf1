import logging
from config import API_CONFIG
from datetime import datetime, timedelta
import time
from typing import Dict, Optional

from okx.Account import AccountAPI
from okx.Trade import TradeAPI
from okx.MarketData import MarketAPI
from okx.PublicData import PublicAPI
import hashlib
import base64
import hmac

class OKXAPIHandler:
    def __init__(self, api_key=None, secret_key=None, passphrase=None, use_demo=False):
        try:
            self.api_key = api_key or API_CONFIG['api_key']
            self.secret_key = secret_key or API_CONFIG['secret_key']
            self.passphrase = passphrase or API_CONFIG['passphrase']
            self.logger = logging.getLogger(__name__)
            flag = "1" if use_demo else "0"  # 实盘:0, 模拟盘:1

            # 初始化各个API客户端
            self.account_api = AccountAPI(
                api_key=self.api_key,
                api_secret_key=self.secret_key,
                passphrase=self.passphrase,
                use_server_time=False,
                flag=flag
            )

            self.trade_api = TradeAPI(
                api_key=self.api_key,
                api_secret_key=self.secret_key,
                passphrase=self.passphrase,
                use_server_time=False,
                flag=flag
            )

            self.market_api = MarketAPI(
                api_key=self.api_key,
                api_secret_key=self.secret_key,
                passphrase=self.passphrase,
                use_server_time=False,
                flag=flag
            )

            self.public_api = PublicAPI(
                api_key=self.api_key,
                api_secret_key=self.secret_key,
                passphrase=self.passphrase,
                use_server_time=False,
                flag=flag
            )

            self.logger.info("OKX API handler initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize OKX API handler: {str(e)}")
            raise

    def get_account_balance(self, currency='USDT'):
        try:
            balance = self.account_api.get_account_balance(ccy=currency)
            self.logger.info(f"Retrieved balance for {currency}")
            return balance
        except Exception as e:
            self.logger.error(f"Failed to get account balance: {str(e)}")
            raise

    def execute_perpetual_trade(self, product_id, trade_mode, order_type, size):
        try:
            order = self.trade_api.place_order(
                instId=product_id,
                tdMode=trade_mode,
                side='buy' if order_type == 'long' else 'sell',
                ordType='market',
                sz=size
            )
            self.logger.info(f"Executed {order_type} trade for {product_id}")
            return order
        except Exception as e:
            self.logger.error(f"Failed to execute trade: {str(e)}")
            raise

    def get_position_info(self, product_id):
        try:
            position = self.account_api.get_positions(instId=product_id)
            self.logger.info(f"Retrieved position info for {product_id}")
            return position
        except Exception as e:
            self.logger.error(f"Failed to get position info: {str(e)}")
            raise

    def get_instrument_info(self, product_id):
        try:
            info = self.public_api.get_instruments(instId=product_id)
            self.logger.info(f"Retrieved instrument info for {product_id}")
            return info
        except Exception as e:
            self.logger.error(f"Failed to get instrument info: {str(e)}")
            raise

    def get_instruments(self, instType=None):
        """
        获取产品列表信息

        参数:
            instType (str): 产品类型
                SPOT: 现货
                SWAP: 永续合约
                FUTURES: 交割合约
                OPTION: 期权
                如果为None，则获取所有类型

        返回:
            dict: 包含状态码和数据的字典
        """
        try:
            self.logger.info(f"开始获取产品列表, 类型: {instType or '所有'}")

            # 调用OKX API获取产品信息
            result = self.public_api.get_instruments(instType=instType)

            if result['code'] != '0':
                self.logger.error(f"获取产品列表失败: {result['msg']}")
                return {
                    'code': int(result['code']),
                    'message': result['msg'],
                    'data': []
                }

            # 处理返回的数据
            instruments = result['data']

            # 提取需要的信息
            formatted_data = []
            for inst in instruments:
                # 强制类型检查
                if not isinstance(inst, dict):
                    continue

                # 关键字段验证
                required_fields = ['instId', 'instType', 'ctValCcy', 'settleCcy']
                if not all(field in inst for field in required_fields):
                    continue

                # 安全转换函数
                def safe_float(value, default=0.0):
                    try:
                        return float(value) if value not in ['', None] else default
                    except (ValueError, TypeError):
                        return default

                def safe_timestamp(timestamp_str):
                    try:
                        if timestamp_str and str(timestamp_str).strip():
                            return datetime.fromtimestamp(int(timestamp_str) / 1000).strftime('%Y-%m-%d %H:%M:%S')
                        return None
                    except (ValueError, TypeError):
                        return None

                instrument_data = {
                    'instrument_id': inst['instId'],
                    'instrument_type': inst['instType'],
                    'state': inst['state'],  # live: 交易中, suspend: 暂停中
                    'tick_size': safe_float(inst['tickSz']),  # 最小价格变动单位
                    'lot_size': safe_float(inst['lotSz']),  # 最小交易数量
                    'min_size': safe_float(inst['minSz']),  # 最小委托数量
                    'listing_time': safe_timestamp(inst.get('listTime'))
                }

                # 根据产品类型添加特定字段
                if inst['instType'] == 'SPOT':
                    instrument_data.update({
                        'base_currency': inst['baseCcy'],  # 基础货币，如BTC
                        'quote_currency': inst['quoteCcy'],  # 计价货币，如USDT
                        'min_notional': safe_float(inst.get('minNotional', '0'))  # 最小名义价值
                    })
                else:
                    instrument_data.update({
                        'base_currency': inst['ctValCcy'],  # 合约面值计价币种
                        'quote_currency': inst['settleCcy'],  # 计价货币
                        'leverage': safe_float(inst.get('lever', '')),  # 杠杆倍数
                        'expiry_time': safe_timestamp(inst.get('expTime'))
                    })

                formatted_data.append(instrument_data)

            self.logger.info(f"成功获取 {len(formatted_data)} 个产品信息")

            # 添加空数据检查
            if not formatted_data:
                return {
                    'code': 1,
                    'message': '获取到空数据列表',
                    'data': []
                }

            return {
                'code': 0,
                'data': formatted_data,
                'message': 'success'
            }

        except Exception as e:
            import traceback
            error_msg = f"获取产品列表时出错: {str(e)} \n{traceback.format_exc()}"
            self.logger.error(error_msg)
            return {
                'code': 1,
                'message': error_msg,
                'data': []
            }

    def get_kline_data(self, currency_pair, start_time=None, end_time=None, bar='1m'):
        """
        获取K线数据

        参数:
            currency_pair (str): 交易对，如 'BTC-USDT'
            start_time (datetime): 开始时间
            end_time (datetime): 结束时间
            bar (str): K线周期，如 '1m', '5m', '15m', '1H', '4H', '1D'

        返回:
            dict: 包含状态码和数据的字典
        """
        try:
            self.logger.info(f"开始获取{currency_pair}的K线数据")

            # 设置默认时间范围
            if end_time is None:
                end_time = int(time.time())
            else:
                end_time = int(end_time.timestamp())

            if start_time is None:
                start_time = end_time - (24 * 60 * 60)  # 默认获取24小时数据
            else:
                start_time = int(start_time.timestamp())

            # OKX API 要求时间戳为毫秒
            start_ts = str(int(start_time * 1000))
            end_ts = str(int(end_time * 1000))

            # 由于API返回的是倒序数据，我们需要多次请求来获取完整的时间范围
            all_candles = []
            current_end = end_time

            while current_end > start_time:
                result = self.market_api.get_history_candlesticks(
                    instId=currency_pair,
                    bar=bar,
                    after=str(int(end_time * 1000)),  # 起始时间
                   # limit='100'  # OKX API 的最大限制是100
                )

                if result['code'] != '0':
                    self.logger.error(f"获取K线数据失败: {result['msg']}")
                    return {
                        'code': int(result['code']),
                        'message': result['msg'],
                        'data': []
                    }

                if not result['data']:
                    break

                # 处理数据
                for candle in result['data']:
                    ts = int(candle[0]) / 1000
                    if ts < start_time or ts > end_time:
                        continue

                    candle_data = {
                        'timestamp': datetime.fromtimestamp(ts).strftime('%Y-%m-%d %H:%M:%S'),
                        'open': float(candle[1]),
                        'high': float(candle[2]),
                        'low': float(candle[3]),
                        'close': float(candle[4]),
                        'volume': float(candle[5]),
                        'volume_currency': float(candle[6])
                    }

                    # 避免重复数据
                    if not any(x['timestamp'] == candle_data['timestamp'] for x in all_candles):
                        all_candles.append(candle_data)

                # 更新结束时间为最早一条数据的时间
                earliest_ts = int(result['data'][-1][0]) / 1000
                if earliest_ts >= current_end:
                    break
                current_end = earliest_ts

                # 添加短暂延迟避免请求过快
                time.sleep(0.2)

            # 按时间正序排序
            all_candles.sort(key=lambda x: x['timestamp'])

            return {
                'code': 0,
                'message': 'success',
                'data': all_candles
            }

        except Exception as e:
            self.logger.error(f"获取K线数据时出错: {str(e)}")
            return {
                'code': -1,
                'message': str(e),
                'data': []
            }

    def get_tickers(self, instType=None):
        """
        获取所有产品行情信息

        参数:
            instType (str): 产品类型，如 SPOT, SWAP, FUTURES, OPTION

        返回:
            dict: 包含状态码和数据的字典
        """
        try:
            self.logger.info(f"开始获取行情数据, 类型: {instType or '所有'}")

            result = self.market_api.get_tickers(instType=instType)

            if result['code'] != '0':
                self.logger.error(f"获取行情数据失败: {result['msg']}")
                return {
                    'code': int(result['code']),
                    'message': result['msg'],
                    'data': []
                }

            # 处理数据
            tickers = []
            for ticker in result['data']:
                tickers.append({
                    'instrument_id': ticker['instId'],
                    'last_price': float(ticker['last']),
                    'open_24h': float(ticker['open24h']),
                    'high_24h': float(ticker['high24h']),
                    'low_24h': float(ticker['low24h']),
                    'volume_24h': float(ticker['vol24h']),
                    'volume_currency_24h': float(ticker['volCcy24h']),
                    'timestamp': datetime.fromtimestamp(int(ticker['ts']) / 1000).strftime('%Y-%m-%d %H:%M:%S')
                })

            return {
                'code': 0,
                'data': tickers,
                'message': 'success'
            }

        except Exception as e:
            error_msg = f"获取行情数据时出错: {str(e)}"
            self.logger.error(error_msg)
            return {
                'code': 1,
                'message': error_msg,
                'data': []
            }

    def place_order(self,
                   instId: str,           # 产品ID，如 BTC-USDT
                   tdMode: str,           # 交易模式
                   side: str,             # 订单方向 buy/sell
                   ordType: str,          # 订单类型 market/limit/post_only/fok/ioc
                   sz: str,               # 委托数量
                   px: Optional[str] = None,     # 委托价格，市价单不需要
                   clOrdId: Optional[str] = None,  # 客户自定义订单ID
                   tag: Optional[str] = None,      # 订单标签
                   tgtCcy: Optional[str] = None,   # 市价单委托数量单位
                   banAmend: bool = False,         # 是否禁止币币市价改单
                   **kwargs                        # 其他参数
                   ) -> Dict:
        """
        下单交易

        Args:
            instId: 产品ID，如 BTC-USDT
            tdMode: 交易模式 cash/isolated/cross
            side: 订单方向 buy/sell
            ordType: 订单类型 market/limit/post_only/fok/ioc
            sz: 委托数量
            px: 委托价格（限价单必需）
            clOrdId: 客户自定义订单ID
            tag: 订单标签
            tgtCcy: 市价单委托数量单位
            banAmend: 是否禁止币币市价改单

        Returns:
            Dict: API返回结果
        """
        try:
            # 准备下单参数
            order_data = {
                "instId": instId,
                "tdMode": tdMode,
                "side": side,
                "ordType": ordType,
                "sz": sz
            }

            # 添加可选参数
            if px is not None:
                order_data["px"] = px
            if clOrdId is not None:
                order_data["clOrdId"] = clOrdId
            if tag is not None:
                order_data["tag"] = tag
            if tgtCcy is not None:
                order_data["tgtCcy"] = tgtCcy
            if banAmend:
                order_data["banAmend"] = "true"

            # 添加其他可选参数
            order_data.update(kwargs)

            self.logger.info(f"下单参数: {order_data}")

            # 执行下单
            result = self.trade_api.place_order(**order_data)

            if result.get('code') == '0':
                self.logger.info(f"下单成功: {result}")
            else:
                self.logger.error(f"下单失败: {result} order_data: {order_data}")

            return result

        except Exception as e:
            error_msg = f"下单失败12: {str(e)}"
            self.logger.error(error_msg)
            return {
                'code': '500',
                'msg': error_msg,
                'data': []
            }

    def get_balance(self, ccy: Optional[str] = None) -> Dict:
        """获取账户余额"""
        try:
            result = self.account_api.get_account_balance(ccy)
            return result
        except Exception as e:
            self.logger.error(f"获取账户余额失败: {str(e)}")
            return {
                'code': '500',
                'msg': str(e),
                'data': []
            }

    def set_position_mode(self, posMode: str = 'single_side') -> Dict:
        """设置持仓方式"""
        try:
            result = self.account_api.set_position_mode(posMode=posMode)
            return result
        except Exception as e:
            self.logger.error(f"设置持仓方式失败: {str(e)}")
            return {
                'code': '500',
                'msg': str(e),
                'data': []
            }

    def set_leverage(self, instId: str, lever: str, mgnMode: str) -> Dict:
        """设置杠杆倍数"""
        try:
            result = self.account_api.set_leverage(
                instId=instId,
                lever=lever,
                mgnMode=mgnMode
            )
            return result
        except Exception as e:
            self.logger.error(f"设置杠杆倍数失败: {str(e)}")
            return {
                'code': '500',
                'msg': str(e),
                'data': []
            }

    def get_positions(self, instId: str) -> Dict:
        """获取持仓信息"""
        try:
            result = self.account_api.get_positions(instId=instId)

            if result.get('code') == '0':
                data = result.get('data', [])
                if not data:  # 如果没有持仓数据
                    return {
                        'code': '0',
                        'msg': '无持仓',
                        'data': [{
                            'instId': instId,
                            'pos': '0',
                            'avgPx': '0',
                            'upl': '0',
                            'mgnRatio': '0',
                            'lever': '0',
                            'liqPx': '0',
                            'mgnMode': 'cross',
                            'posSide': 'net'
                        }]
                    }
                return result
            else:
                self.logger.error(f"获取持仓信息失败: {result}")
                return result

        except Exception as e:
            self.logger.error(f"获取持仓信息失败: {str(e)}")
            return {
                'code': '500',
                'msg': str(e),
                'data': []
            }

    def get_history_orders(self,
                          instId: Optional[str] = None,
                          ordType: Optional[str] = None,
                          state: Optional[str] = None,
                          begin: Optional[str] = None,
                          end: Optional[str] = None,
                          limit: Optional[str] = None,
                          instType: str = 'SWAP') -> Dict:
        """
        获取历史订单记录

        Args:
            instId: 产品ID，如 BTC-USDT
            ordType: 订单类型 market/limit/post_only/fok/ioc
            state: 订单状态 live/filled/canceled
            begin: 请求此时间戳之前（更旧的数据）的分页内容
            end: 请求此时间戳之后（更新的数据）的分页内容
            limit: 返回结果的数量，最大100条
            instType: 产品类型，如 SPOT/MARGIN/SWAP/FUTURES/OPTION，默认SWAP

        Returns:
            Dict: API返回结果，包含历史订单列表
        """
        try:
            # 准备请求参数
            params = {
                'instType': instType  # 添加必需的instType参数
            }
            if instId:
                params['instId'] = instId
            if ordType:
                params['ordType'] = ordType
            if state:
                params['state'] = state
            if begin:
                params['begin'] = begin
            if end:
                params['end'] = end
            if limit:
                params['limit'] = limit

            # 获取最近7天的订单历史
            result = self.trade_api.get_orders_history(**params)

            if result.get('code') == '0':
                self.logger.info(f"成功获取历史订单记录: {len(result.get('data', []))}条")

                # 处理返回数据
                orders = []
                for order in result.get('data', []):
                    order_info = {
                        'order_id': order.get('ordId'),
                        'client_order_id': order.get('clOrdId'),
                        'instrument_id': order.get('instId'),
                        'order_type': order.get('ordType'),
                        'price': order.get('px'),
                        'size': order.get('sz'),
                        'side': order.get('side'),
                        'position_side': order.get('posSide'),
                        'trade_mode': order.get('tdMode'),
                        'state': order.get('state'),
                        'create_time': datetime.fromtimestamp(int(order.get('cTime', '0')) / 1000).strftime('%Y-%m-%d %H:%M:%S'),
                        'update_time': datetime.fromtimestamp(int(order.get('uTime', '0')) / 1000).strftime('%Y-%m-%d %H:%M:%S'),
                        'filled_size': order.get('accFillSz'),
                        'filled_price': order.get('avgPx'),
                        'fee': order.get('fee'),
                        'fee_currency': order.get('feeCcy'),
                        'profit_loss': order.get('pnl'),
                        'leverage': order.get('lever')
                    }
                    orders.append(order_info)

                return {
                    'code': '0',
                    'msg': 'success',
                    'data': orders
                }
            else:
                self.logger.error(f"获取历史订单记录失败: {result}")
                return result

        except Exception as e:
            error_msg = f"获取历史订单记录失败: {str(e)}"
            self.logger.error(error_msg)
            return {
                'code': '500',
                'msg': error_msg,
                'data': []
            }

    def get_order_history_by_time(self,
                                start_time: str,
                                end_time: str,
                                instId: str = 'DOGE-USDT-SWAP',
                                instType: str = 'SWAP') -> Dict:
        """
        获取指定时间范围内的历史委托数据，并按分钟组织
        支持分页获取超过100条的数据

        Args:
            start_time: 开始时间，格式为 'YYYY-MM-DD HH:MM:SS'
            end_time: 结束时间，格式为 'YYYY-MM-DD HH:MM:SS'
            instId: 产品ID，如 'DOGE-USDT-SWAP'
            instType: 产品类型，如 'SWAP'

        Returns:
            Dict: 包含按分钟组织的历史委托数据
        """
        try:
            # 转换时间格式为时间戳（毫秒）
            try:
                start_dt = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
                end_dt = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                # 尝试其他时间格式
                try:
                    # 尝试ISO格式
                    start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00').replace(' ', 'T'))
                    end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00').replace(' ', 'T'))
                except ValueError:
                    # 如果还是失败，使用通用解析
                    from dateutil import parser
                    start_dt = parser.parse(start_time)
                    end_dt = parser.parse(end_time)

            # 确保时间范围有效
            now = datetime.now()
            if start_dt > now:
                self.logger.warning(f"开始时间 {start_time} 是未来时间，使用当前时间前24小时")
                start_dt = now - timedelta(days=1)
            if end_dt > now:
                self.logger.warning(f"结束时间 {end_time} 是未来时间，使用当前时间")
                end_dt = now

            # 转换为毫秒时间戳
            start_ts = str(int(start_dt.timestamp() * 1000))
            end_ts = str(int(end_dt.timestamp() * 1000))

            self.logger.info(f"处理后的时间范围: {start_dt.strftime('%Y-%m-%d %H:%M:%S')} 至 {end_dt.strftime('%Y-%m-%d %H:%M:%S')}")

            self.logger.info(f"获取历史委托数据: {instId}, 时间范围: {start_time} 至 {end_time}")

            # 实现分页获取所有订单数据
            all_orders = []
            page = 1
            has_more = True
            last_order_time = None

            while has_more:
                # 准备请求参数
                params = {
                    'instId': instId,
                    'instType': instType,
                    'limit': '100'  # 每页最多100条记录
                }

                # 设置时间范围
                if page == 1:
                    # 第一页使用原始时间范围
                    params['begin'] = start_ts
                    params['end'] = end_ts
                else:
                    # 后续页面使用上一页最后一条记录的时间作为结束时间
                    # 注意：OKX API的分页是基于时间的，不是基于ID
                    params['begin'] = start_ts
                    params['end'] = last_order_time

                self.logger.info(f"获取第{page}页订单数据，参数: {params}")

                # 调用API获取当前页数据
                result = self.get_history_orders(**params)

                if result.get('code') != '0':
                    self.logger.error(f"获取历史委托数据失败: {result.get('msg')}")
                    # 如果是第一页就失败，直接返回错误
                    if page == 1:
                        return {
                            'code': result.get('code'),
                            'msg': result.get('msg'),
                            'data': {}
                        }
                    # 如果不是第一页，使用已获取的数据继续处理
                    break

                # 获取当前页的订单数据
                current_page_orders = result.get('data', [])
                page_order_count = len(current_page_orders)
                self.logger.info(f"第{page}页获取到 {page_order_count} 条历史委托记录")

                # 添加到总订单列表
                all_orders.extend(current_page_orders)

                # 判断是否还有更多数据
                if page_order_count < 100:
                    has_more = False
                    self.logger.info(f"当前页订单数量少于100，分页结束")
                elif page_order_count == 0:
                    has_more = False
                    self.logger.info(f"当前页没有数据，分页结束")
                else:
                    # 获取最后一条记录的创建时间作为下一页的结束时间点
                    if current_page_orders:
                        # 按创建时间排序，找到最早的记录
                        sorted_orders = sorted(current_page_orders, key=lambda x: x.get('create_time', ''))
                        earliest_order = sorted_orders[0]

                        # 获取创建时间的毫秒时间戳
                        try:
                            create_time_str = earliest_order.get('create_time', '')
                            create_dt = datetime.strptime(create_time_str, '%Y-%m-%d %H:%M:%S')
                            # 减去1毫秒，确保不会重复获取同一条记录
                            create_ts = int(create_dt.timestamp() * 1000) - 1
                            last_order_time = str(create_ts)
                            self.logger.info(f"下一页结束时间点: {create_time_str}, 时间戳: {last_order_time}")
                        except Exception as e:
                            self.logger.error(f"解析订单时间失败: {e}")
                            has_more = False
                    else:
                        has_more = False

                page += 1

                # 添加短暂延迟避免请求过快
                time.sleep(0.2)

                # 安全检查：如果页数过多，防止无限循环
                if page > 50:
                    self.logger.warning("页数超过50，强制结束分页")
                    has_more = False

            # 使用所有获取到的订单数据
            orders = all_orders
            self.logger.info(f"总共获取到 {len(orders)} 条历史委托记录")

            # 打印前几条订单数据样例
            if orders:
                sample_orders = orders[:min(3, len(orders))]
                for i, order in enumerate(sample_orders):
                    self.logger.info(f"订单样例 {i+1}: "
                                    f"create_time={order.get('create_time')}, "
                                    f"order_id={order.get('order_id')}, "
                                    f"side={order.get('side')}, "
                                    f"price={order.get('price')}, "
                                    f"state={order.get('state')}")

            # 按分钟组织数据
            minute_data = {}
            processed_orders = 0

            for order in orders:
                # 获取创建时间
                create_time = order.get('create_time')
                if not create_time:
                    self.logger.warning(f"订单缺失create_time字段: {order.get('order_id')}")
                    continue

                # 尝试标准化时间格式
                try:
                    # 尝试解析时间并重新格式化，确保格式一致
                    dt = datetime.strptime(create_time, '%Y-%m-%d %H:%M:%S')
                    create_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                except Exception as e:
                    self.logger.warning(f"时间格式解析失败: {create_time}, 错误: {str(e)}")
                    # 尝试其他格式
                    try:
                        # 尝试带毫秒的格式
                        dt = datetime.strptime(create_time, '%Y-%m-%d %H:%M:%S.%f')
                        create_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                    except Exception:
                        # 如果还是失败，使用原始格式
                        pass

                # 提取分钟级时间戳 (YYYY-MM-DD HH:MM:00)
                try:
                    # 确保时间格式正确
                    minute_ts = create_time[:17] + '00'
                    self.logger.debug(f"原始时间: {create_time}, 分钟级时间戳: {minute_ts}")
                except Exception as e:
                    self.logger.warning(f"无法提取分钟时间戳: {create_time}, 错误: {str(e)}")
                    continue

                # 确保分钟键存在
                if minute_ts not in minute_data:
                    minute_data[minute_ts] = []
                    self.logger.debug(f"创建新的分钟数据条目: {minute_ts}")

                # 添加秒级数据
                order_data = {
                    'timestamp': create_time,
                    'minute_timestamp': minute_ts,  # 添加分钟时间戳字段
                    'order_id': order.get('order_id'),
                    'client_order_id': order.get('client_order_id'),
                    'instrument_id': order.get('instrument_id'),
                    'order_type': order.get('order_type'),
                    'price': float(order.get('price', 0) or 0),
                    'size': float(order.get('size', 0) or 0),
                    'side': order.get('side'),  # buy/sell
                    'position_side': order.get('position_side'),  # long/short
                    'trade_mode': order.get('trade_mode'),
                    'state': order.get('state'),  # filled/canceled/live
                    'filled_size': float(order.get('filled_size', 0) or 0),
                    'filled_price': float(order.get('filled_price', 0) or 0),
                    'fee': float(order.get('fee', 0) or 0),
                    'fee_currency': order.get('fee_currency'),
                    'profit_loss': float(order.get('profit_loss', 0) or 0),
                    'leverage': float(order.get('leverage', 0) or 0)
                }

                minute_data[minute_ts].append(order_data)
                processed_orders += 1

            # 添加中文状态和类型描述
            for minute, orders in minute_data.items():
                for order in orders:
                    # 添加中文状态描述
                    state_map = {
                        'filled': '完全成交',
                        'canceled': '已撤单',
                        'live': '等待成交',
                        'partially_filled': '部分成交'
                    }
                    order['state_cn'] = state_map.get(order['state'], order['state'])

                    # 添加中文订单类型描述
                    order_type_map = {
                        'market': '市价单',
                        'limit': '限价单',
                        'post_only': '只做挂单',
                        'fok': '全部成交或立即取消',
                        'ioc': '立即成交并取消剩余'
                    }
                    order['order_type_cn'] = order_type_map.get(order['order_type'], order['order_type'])

                    # 添加中文交易方向描述
                    if order['side'] == 'buy' and order['position_side'] == 'long':
                        order['direction_cn'] = '开多'
                    elif order['side'] == 'sell' and order['position_side'] == 'long':
                        order['direction_cn'] = '平多'
                    elif order['side'] == 'sell' and order['position_side'] == 'short':
                        order['direction_cn'] = '开空'
                    elif order['side'] == 'buy' and order['position_side'] == 'short':
                        order['direction_cn'] = '平空'
                    else:
                        order['direction_cn'] = f"{order['side']}/{order['position_side']}"

            # 打印最终结果的数据结构
            total_minutes = len(minute_data)
            total_orders = sum(len(orders) for orders in minute_data.values())
            self.logger.info(f"最终结果: {total_minutes}个分钟数据点, 共{total_orders}条订单, 处理了{processed_orders}条原始订单")

            # 打印所有分钟时间戳
            if minute_data:
                minute_keys = list(minute_data.keys())
                minute_keys.sort()
                self.logger.info(f"分钟时间戳列表(前10个): {minute_keys[:min(10, len(minute_keys))]}")

                # 打印各分钟的订单数量统计
                orders_per_minute = {minute: len(orders) for minute, orders in minute_data.items()}
                top_minutes = sorted(orders_per_minute.items(), key=lambda x: x[1], reverse=True)[:5]
                self.logger.info(f"订单数量最多的前5个分钟: {top_minutes}")

                # 打印第一个分钟的数据样例
                if minute_keys:
                    first_minute = minute_keys[0]
                    first_minute_orders = minute_data[first_minute]
                    self.logger.info(f"第一个分钟 {first_minute} 有 {len(first_minute_orders)} 条订单")

                    # 打印该分钟内的所有订单的时间戳
                    timestamps_in_minute = [order.get('timestamp') for order in first_minute_orders]
                    self.logger.info(f"该分钟内的所有时间戳: {timestamps_in_minute}")

                    if first_minute_orders:
                        sample_order = first_minute_orders[0]
                        self.logger.info(f"样例订单: timestamp={sample_order.get('timestamp')}, "
                                        f"minute_timestamp={sample_order.get('minute_timestamp')}, "
                                        f"price={sample_order.get('price')}, "
                                        f"side={sample_order.get('side')}, "
                                        f"state={sample_order.get('state')}")

            return {
                'code': '0',
                'msg': 'success',
                'data': minute_data,
                'metadata': {
                    'start_time': start_time,
                    'end_time': end_time,
                    'instrument_id': instId,
                    'total_minutes': total_minutes,
                    'total_orders': total_orders
                }
            }

        except Exception as e:
            error_msg = f"获取历史委托数据失败: {str(e)}"
            self.logger.error(error_msg)
            import traceback
            self.logger.error(traceback.format_exc())
            return {
                'code': '500',
                'msg': error_msg,
                'data': {}
            }

    def get_ticker(self, instId):
        """
        获取产品的实时行情数据

        参数:
            instId (str): 产品ID，例如 "DOGE-USDT-SWAP"

        返回:
            dict: 包含实时行情数据的字典
        """
        try:
            result = self.market_api.get_ticker(instId=instId)
            self.logger.info(f"Retrieved ticker for {instId}")
            return result
        except Exception as e:
            self.logger.error(f"Failed to get ticker: {str(e)}")
            return {"code": "-1", "msg": str(e), "data": []}

    def generate_signature(self, message: str) -> str:
        """修正版签名生成方法"""
        if not self.secret_key:  # 使用实例的secret_key属性
            raise ValueError("API_SECRET未配置")

        signature = hmac.new(
            self.secret_key.encode('utf-8'),  # 使用实例属性
            message.encode('utf-8'),
            hashlib.sha256
        ).digest()

        return base64.b64encode(signature).decode('utf-8')

    def cancel_order(self, ordId, instId=None, clOrdId=None):
        """实现撤单功能"""
        try:
            # instId是必需参数，不能为None
            if not instId:
                self.logger.error("撤单失败: instId参数不能为空")
                return {
                    'code': '400',
                    'msg': "撤单失败: instId参数不能为空",
                    'data': []
                }

            params = {
                'ordId': ordId,
                'instId': instId  # 确保instId始终存在
            }

            if clOrdId:
                params['clOrdId'] = clOrdId

            # 使用trade_api调用撤单API
            result = self.trade_api.cancel_order(**params)

            self.logger.info(f"撤单请求已发送: {ordId}, 交易对: {instId}")
            return result
        except Exception as e:
            error_msg = f"撤单失败: {str(e)}"
            self.logger.error(error_msg)
            return {
                'code': '500',
                'msg': error_msg,
                'data': []
            }

# 创建API实例
okx_api = OKXAPIHandler(
    api_key=API_CONFIG['api_key'],
    secret_key=API_CONFIG['secret_key'],
    passphrase=API_CONFIG['passphrase'],
    use_demo=API_CONFIG['is_simulated']
)


#if __name__ == "__main__":

    # 测试获取产品列表
    #products = okx_api.get_tickers("SWAP")
    #print(products)