"""
模拟last_high low值有问题.
命令行执行
python strategy_analyzer.py --start "2024-11-24 00:00:00" --end "2024-11-24 23:59:59" --buy_rate 0.02 --sell_rate 0.02 --rest 1 --clear --threads 4 --split-by-day
python strategy_analyzer.py --start "2024-12-22 00:00:00" --end "2025-01-02 23:59:59"  --clear --threads 20 --split-by-day
只出结果
python strategy_analyzer.py --start "2024-11-01 00:00:00" --end "2025-01-05 23:59:59" --buy_rate 0.02 --sell_rate 0.03 --rest 0 --clear --no-save
python strategy_analyzer.py --start "2024-11-17 00:00:00" --end "2024-12-27 23:59:59" --rest 0  --clear

python strategy_analyzer.py --start "2024-11-01 00:00:00" --end "2024-11-03 23:59:59" --buy_rate 0.02 --sell_rate 0.03 --rest 0 --clear

python strategy_analyzer.py --start "2024-11-01 00:00:00" --end "2025-01-17 23:59:59" --buy_rate 0.01 --sell_rate 0.01  --currency DOGE --lookback-minutes 2 --min-trigger-rest 30 --rest 0
python strategy_analyzer.py --start "2025-03-28 00:00:00" --end "2025-03-28 23:59:59" --buy_rate 0.01 --sell_rate 0.01  --currency DOGE --lookback-minutes 2 --min-trigger-rest 30 --rest 0
python strategy_analyzer.py --start "2025-03-28 00:00:00" --end "2025-03-28 23:59:59" --buy_rate 0.001 --sell_rate 0.001  --currency DOGE --lookback-minutes 2 --min-trigger-rest 30 --rest 0

python strategy_analyzer.py --start "2025-03-01 00:00:00" --end "2025-04-09 23:59:59" --buy_rate 0.001  --sell_rate 0.001  --currency DOGE  --rest

python strategy_analyzer.py --start "2025-03-11 00:00:00" --end "2025-04-15 23:59:59"  --currency DOGE


python strategy_analyzer.py --start "2025-04-19 22:00:00" --end "2025-04-19 23:59:59"  --currency DOGE
python strategy_analyzer.py --start "2025-04-20 00:00:00" --end "2025-04-20 01:59:59"  --currency DOGE
python strategy_analyzer.py --start "2025-04-20 02:00:00" --end "2025-04-20 03:59:59"  --currency DOGE
python strategy_analyzer.py --start "2025-04-20 04:00:00" --end "2025-04-20 05:59:59"  --currency DOGE
python strategy_analyzer.py --start "2025-04-20 06:00:00" --end "2025-04-20 07:59:59"  --currency DOGE
python strategy_analyzer.py --start "2025-04-20 08:00:00" --end "2025-04-20 09:59:59"  --currency DOGE
python strategy_analyzer.py --start "2025-04-20 10:00:00" --end "2025-04-20 11:59:59"  --currency DOGE
python strategy_analyzer.py --start "2025-04-20 12:00:00" --end "2025-04-20 13:59:59"  --currency DOGE
python strategy_analyzer.py --start "2025-04-20 14:00:00" --end "2025-04-20 15:59:59"  --currency DOGE
python strategy_analyzer.py --start "2025-04-20 16:00:00" --end "2025-04-20 17:59:59"  --currency DOGE
python strategy_analyzer.py --start "2025-04-20 18:00:00" --end "2025-04-20 19:59:59"  --currency DOGE
python strategy_analyzer.py --start "2025-04-20 20:00:00" --end "2025-04-20 21:59:59"  --currency DOGE
python strategy_analyzer.py --start "2025-04-20 22:00:00" --end "2025-04-20 23:59:59"  --currency DOGE

python strategy_analyzer.py --start "2025-01-01 00:00:00" --end "2025-02-03 23:59:59"  --currency BTC --rest 0


python strategy_analyzer.py --start "2025-04-22 00:00:00" --end "2025-04-22 23:59:59"  --buy-rate 0.006 --sell-rate 0.004  --currency DOGE --lookback-minutes-buy 5 --lookback-minutes-sell 3 --min-trigger-rest 40 --rest 0

python strategy_analyzer.py --start "2025-04-01 00:00:00" --end "2025-06-01 23:59:59" --rest 0   --currency DOGE


python
python strategy_analyzer.py   --client-id a3

重新执行策略
python strategy_analyzer.py   --rerun 859659

"""



import shutil
import json
import os
from datetime import datetime, timedelta

import sys

from sympy import N

def print_log(message):
    """打印日志"""
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
    with open(os.path.join(os.path.dirname(__file__), 'sa_log.log'), 'a', encoding='utf-8') as log_file:
        print(f"{current_time} - {message}", file=log_file)

    print(f"{current_time} - {message}")
    sys.stdout.flush()  # 立即刷新输出缓冲区

print("\n")
print_log("分析启动")

def print_params(self, params, row=None):
    """
    打印策略参数到文件

    Args:
        params: 策略参数字典
        row: K线数据行
        filename: 输出文件名
    """

    # 定义要打印的关键参数及其中文名称
    key_mapping = {

        'current_percentage': '当前幅度',
        'last_low': '最后最低价',
        'last_high': '最后最高价',
        'continuous_up_count': '连续上升',
        'continuous_down_count': '连续下跌',
        'rest_minutes': '休息时间',
        'min_trigger_price': '最小触发价',
        'trigger_price': '触发价',
        'account_value': '账户价值',
        'total_trades': '交易次数',
        'current_position': '当前仓位',
        'last_position_price': '上次持仓价',
        'total_profit': '总收益',
        'total_fees': '总手续费',
        'entry_time_diff': '开仓时间差'
    }

    pid = os.getpid()

    # 根据是否为实盘交易生成文件名
    if params.get('is_live_trading', 0) == 1:
        filename = f"params_log_{pid}_live.txt"
    else:
        filename = f"params_log_{pid}.txt"

    # 准备输出数据
    output_data = {}

    # 添加K线数据
    if row:
        output_data['timestamp'] = row.get('timestamp', '').replace('T', ' ').replace('+00:00', '')
        output_data['open'] = f"{float(row.get('open', 0)):.5f}"
        output_data['close'] = f"{float(row.get('close', 0)):.5f}"
        output_data['high'] = f"{float(row.get('high', 0)):.5f}"
        output_data['low'] = f"{float(row.get('low', 0)):.5f}"

    # 添加关键参数
    for k in key_mapping:
        if k in params:
            value = params[k]
            if isinstance(value, float):
                output_data[k] = f"{value:.5f}"
            else:
                output_data[k] = value
        else:
            output_data[k] = 0

    # 获取所有键
    available_keys = []
    if row:
        available_keys = ['timestamp', 'open', 'close', 'high', 'low']
    # 不检查键是否存在于params，直接使用所有key_mapping中的键
    available_keys.extend(list(key_mapping.keys()))

    # 准备输出行，确保空值也有tab分隔
    values = []
    for k in available_keys:
        # 如果键不存在，使用0代替
        v=output_data.get(k, 0)
        if v==None:
            v=0
        values.append(v)

    # 确保所有值都是字符串，不过滤空值
    values = [str(v) for v in values]
    values_str = '\t'.join(values)

    # 使用全局变量跟踪已创建的文件和文件句柄
    if not hasattr(print_log, 'created_files'):
        print_log.created_files = set()

    # 使用全局变量存储文件句柄
    if not hasattr(print_log, 'file_handles'):
        print_log.file_handles = {}

    # 检查文件是否已经在我们的记录中
    if filename not in print_log.created_files:
        # 如果不在记录中，检查文件是否存在
        file_exists = os.path.isfile(filename)
        if not file_exists:
            # 创建文件并写入头部
            f = open(filename, 'a', encoding='utf-8')
            header_cn = '\t'.join(['时间', '开盘价', '收盘价', '最高价', '最低价'] + [key_mapping[k] for k in available_keys if k in key_mapping])
            header_en = '\t'.join(available_keys)
            f.write(f"{header_cn}\n{header_en}\n")
            # 将文件句柄保存到全局变量
            print_log.file_handles[filename] = f
            # 将文件添加到已创建文件集合中
            print_log.created_files.add(filename)
        else:
            # 文件已存在，打开并保存句柄
            f = open(filename, 'a', encoding='utf-8')
            print_log.file_handles[filename] = f
            print_log.created_files.add(filename)

    # 使用已有的文件句柄写入数据
    print_log.file_handles[filename].write(f"{values_str}\n")
    print_log.file_handles[filename].flush()  # 确保数据立即写入








import pymysql

import pymysql.cursors

import traceback

from decimal import Decimal

from config import *


import time

from db_config import DB_CONFIG

import argparse

import signal
import threading


# from trading_executor import trading_executor
# print_log("分析启动12")
# from okx_api_handler import okx_api
# print_log("分析启动13")

# 在文件顶部添加全局变量
_stop_event = False  # 用于控制程序停止的全局变量

class StrategyConfig:
    """策略配置类，集中管理所有策略参数"""
    def __init__(self):
        self.fee_rate = 0.0005  # 0.05% 交易手续费
        self.initial_capital = 100  # 初始资金
        self.window_size = 3  # 价格窗口大小（用于计算局部最低点）
        self.enable_profit_check = True  # 是否启用收益检查
        self.stop_loss = -0.05  # 止损比例 (-5%)
        self.rest_minutes = 0  # 交易间隔休息时间（分钟）
        self.last_close_time = None  # 上次平仓时间
        self.is_live_trading = False  # 默认为模拟交易

class StrategyAnalyzer:
    def __init__(self, db_config, trading_executor=None, price_collector=None):
        """初始化策略分析器"""
        self.config = StrategyConfig()
        self.fee_rate = self.config.fee_rate
        self.initial_capital = self.config.initial_capital
        self.db_config = db_config
        self.trading_executor = trading_executor  # 新增属性
        self.price_collector = price_collector  # 新增属性
        self.price_data = {}
        self.is_live_trading = False  # 默认为模拟交易
        self.row_reson_list = {}
        self.save_row_reson = 0
        self.save_trades = 0
        self.reverse_buy = 0

    def _signal_handler(self, signum, frame):
        """处理信号"""
        print_log("\n收到停止信号，正在安全退出...")
        global _stop_event
        _stop_event = True

    def is_same_minute(self, current_time, last_entry_time=None, last_close_time=None):
        """
        检查当前时间是否与最后入场时间或最后平仓时间在同一分钟

        参数:
            current_time: 当前时间字符串 (ISO格式)
            last_entry_time: 最后入场时间字符串 (ISO格式)，可能为None
            last_close_time: 最后平仓时间字符串 (ISO格式)，可能为None

        返回:
            bool: 如果在同一分钟返回True，否则返回False
        """
        try:
            # 处理current_time为None的情况
            if not current_time:
                return False

            # 将当前时间转换为datetime对象
            current_dt = datetime.fromisoformat(current_time)

            # 检查last_entry_time
            if last_entry_time:
                try:
                    last_entry_dt = datetime.fromisoformat(last_entry_time)
                    if current_dt.minute == last_entry_dt.minute and current_dt.hour == last_entry_dt.hour and current_dt.day == last_entry_dt.day:
                        return True
                except (ValueError, TypeError):
                    # 如果转换失败，忽略这个时间
                    pass

            # 检查last_close_time
            if last_close_time:
                try:
                    last_close_dt = datetime.fromisoformat(last_close_time)
                    if current_dt.minute == last_close_dt.minute and current_dt.hour == last_close_dt.hour and current_dt.day == last_close_dt.day:
                        return True
                except (ValueError, TypeError):
                    # 如果转换失败，忽略这个时间
                    pass

            # 如果都不在同一分钟，返回False
            return False

        except Exception as e:
            print(f"检查时间是否在同一分钟时出错: {str(e)}")
            # 出错时返回False，避免影响交易
            return False

    def get_db_connection(self):
        """获取数据库连接"""
        return pymysql.connect(
            host=self.db_config['host'],
            user=self.db_config['user'],
            password=self.db_config['password'],
            database=self.db_config['database'],
            port=self.db_config['port'],
            cursorclass=pymysql.cursors.DictCursor
        )

    def get_cache_key(self, start_time, end_time, currency):
        """生成缓存键"""
        return f"{currency}_{start_time.strftime('%Y%m%d%H%M%S')}_{end_time.strftime('%Y%m%d%H%M%S')}"

    def get_price_data(self, start_time, end_time, currency):
        """获取指定时间范围的价格数据"""
        try:
            if _stop_event:  # 检查全局停止事件
                return None

            # 从数据库获取数据
            with self.get_db_connection() as conn:
                cursor = conn.cursor()

                # 首先获取数据总量
                count_sql = """
                SELECT COUNT(*) as count
                FROM crypto_prices
                WHERE currency = %s
                AND timestamp >= %s
                AND timestamp <= %s
                """
                cursor.execute(count_sql, (currency, start_time, end_time))
                total_count = cursor.fetchone()['count']

                # 如果数据量大于10000，则分批获取
                batch_size = 10000
                all_data = []

                if total_count > batch_size:
                    for offset in range(0, total_count, batch_size):
                        sql = """
                        SELECT timestamp, open_price as open, close_price as close,
                               high_price as high, low_price as low, volume
                        FROM crypto_prices
                        WHERE currency = %s
                        AND timestamp >= %s
                        AND timestamp <= %s
                        ORDER BY timestamp ASC
                        LIMIT %s OFFSET %s
                        """
                        cursor.execute(sql, (currency, start_time, end_time, batch_size, offset))
                        batch_data = cursor.fetchall()
                        all_data.extend(batch_data)
                else:
                    sql = """
                    SELECT timestamp, open_price as open, close_price as close,
                           high_price as high, low_price as low, volume
                    FROM crypto_prices
                    WHERE currency = %s
                    AND timestamp >= %s
                    AND timestamp <= %s
                    ORDER BY timestamp ASC
                    """
                    cursor.execute(sql, (currency, start_time, end_time))
                    all_data = cursor.fetchall()


                if not all_data:
                    print_log(f"警告：在指定时间范围内没有找到数据")
                    return []

                for data in all_data:
                    data['timestamp'] = data['timestamp'].isoformat()
                    data['is_complete'] = 1

                return all_data

        except TimeoutError:
            print_log("获取价格数据超时")
            return None
        except Exception as e:
            error_msg = traceback.format_exc()
            print_log(f"获取价格数据时发生错误: [{str(e)}]\n错误详情:\n{error_msg}")
            raise

    def save_strategy_results(self, result):
        """
        保存策略分析结果到数据库
        :param result: 策略分析结果
        """
        conn = None
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()

            # 生成交易日志文本
            trade_log = []
            trade_log.append("=== 策略分析结果 ===")
            trade_log.append(f"分析时间范围: {result.get('start_time', '未知')} 至 {result.get('end_time', '未知')}")
            trade_log.append(f"策略参数: 卖出阈值={result.get('sell_rate', 0)*100:.1f}%, 买入阈值={result.get('buy_rate', 0)*100:.1f}%, 休息时间={result.get('rest_minutes', 0)}分钟 , 最小触发时间={result.get('min_trigger_rest', 0)}分钟")
            trade_log.append(f"初始资金: ${result.get('initial_capital', 0):.5f}")
            trade_log.append(f"最终资金: ${result.get('final_capital', 0):.5f}")
            trade_log.append(f"总收益: ${result.get('profit_loss', 0):.5f} ({result.get('profit_percentage', 0):.1f}%)")
            trade_log.append(f"总手续费: ${result.get('total_fees', 0):.5f}")
            trade_log.append(f"总交易次数: {len(result.get('trades', []))}")
            trade_log.append("")
            trade_log.append("=== 详细交易记录 ===")
            max_drawdown = 0
            successful_trades = 0
            trade_log.append("=== 详细交易记录 ===")

            # 检查是否有交易记录
            if result['trades'] and self.save_trades:
                for trade in result['trades']:


                    log_entry = (
                        f"{trade['timestamp']} | "
                        f"操作: {trade['action']} | "
                        f"类型: {trade['position_type']} | "
                        f"价格: ${trade['price']:<6.4f} | "
                        f"数量: {trade['amount']:.4f} | "
                        f"手续费: ${trade['fee']:.4f} | "
                        f"账户价值: ${trade['account_value']:.4f}"
                    )

                    if trade['trigger_reason']:
                        log_entry += f" | 触发原因: {trade['trigger_reason']}"

                    trade_log.append(log_entry)
            else:
                trade_log.append("没有交易记录")

            trade_log_text = "\n".join(trade_log)

            # 保存策略结果
            strategy_sql = """
                INSERT INTO strategy_results
                (strategy_name, strategy_type, start_time, end_time, currency,
                sell_rate, buy_rate, initial_capital, final_capital,
                total_trades, successful_trades, total_profit, total_fees,
                roi, trade_log, created_at, rest_minutes,
                highest_account_value, lowest_account_value, min_trigger_rest, lookback_minutes_buy, lookback_minutes_sell, is_live_trading,reverse_buy)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s , %s)
            """

            strategy_values = (
                'rebound_strategy',  # strategy_name
                'rebound',          # strategy_type
                result['start_time'],
                result['end_time'],
                result['currency'],            # currency
                result['sell_rate'],
                result['buy_rate'],
                result['initial_capital'],
                result.get('final_capital', 0),
                len(result.get('trades', [])),  # total_trades
                successful_trades,      # successful_trades
                result.get('total_profit', 0),  # total_profit
                result.get('total_fees', 0),    # total_fees
                result.get('profit_percentage', 0),  # roi
                "",
                datetime.now(),
                result['rest_minutes'],  # 从结果参数中获取rest_minutes
                result['highest_account_value'],  # 从结果参数中获取highest_account_value
                result['lowest_account_value'],  # 从结果参数中获取lowest_account_value
                result['min_trigger_rest'],
                int(result.get('lookback_minutes_buy', 4)),  # 添加lookback_minutes参数
                int(result.get('lookback_minutes_sell', 4)),  # 添加lookback_minutes参数
                result['is_live_trading'],
                result['reverse_buy']
            )

            cursor.execute(strategy_sql, strategy_values)
            strategy_id = cursor.lastrowid

            # 保存交易记录
            if result['trades']:
                # 分批保存交易记录
                batch_size = 100
                trade_sql = """
                    INSERT INTO strategy_trades
                    (strategy_id, timestamp, action, position_type, price, amount,
                     entry_price, capital_before, capital_after, fee, reason, profit_amount,
                     profit_percentage, trigger_reason, market_price, account_value, created_at, rest_minutes, currency,
                     sell_rate, buy_rate, last_high, last_low, position_size, min_trigger_price, min_trigger_rest, lookback_minutes_buy, lookback_minutes_sell, reverse_buy,
                     live_available_balance, live_total_equity, live_position_value, live_unrealized_pnl)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                available_capital = result['initial_capital']
                trades = result['trades']
                highest_account_value = 0
                lowest_account_value_after_peak = float('inf')


                for i in range(0, len(trades), batch_size):
                    batch = trades[i:i + batch_size]
                    trade_values = []

                    for trade in batch:

                        profit = trade.get('profit', 0)
                        if profit > 0:
                            successful_trades += 1

                        # 获取实盘数据（如果存在）
                        live_available_balance = trade.get('live_available_balance', 0)
                        live_total_equity = trade.get('live_total_equity', 0)
                        live_position_value = trade.get('live_position_value', 0)
                        live_unrealized_pnl = trade.get('live_unrealized_pnl', 0)

                        trade_values.append((
                            strategy_id,
                            trade['timestamp'].split('+')[0],
                            trade['action'],
                            trade['position_type'],
                            trade['price'],
                            trade['amount'],
                            trade.get('entry_price', trade['price']),
                            available_capital,
                            available_capital + trade.get('profit', 0) + trade.get('fee', 0),
                            trade['fee'],
                            trade.get('reason'),
                            trade.get('profit', 0),
                            trade.get('profit', 0) / available_capital * 100 if available_capital else 0,
                            trade.get('trigger_reason'),
                            trade['price'],
                            trade['account_value'],
                            datetime.now(),
                            result['rest_minutes'],
                            result['currency'],
                            result['sell_rate'],
                            result['buy_rate'],
                            trade['last_high'],
                            trade['last_low'],
                            trade['position_size'],
                            trade['min_trigger_price'],
                            result['min_trigger_rest'],
                            int(result.get('lookback_minutes_buy', 4)),  # 添加lookback_minutes参数
                            int(result.get('lookback_minutes_sell', 4)),  # 添加lookback_minutes参数
                            result['reverse_buy'],
                            live_available_balance,  # 添加实盘可用保证金
                            live_total_equity,       # 添加实盘总资产
                            live_position_value,     # 添加实盘持仓价值
                            live_unrealized_pnl      # 添加实盘未实现盈亏
                        ))
                        available_capital += trade.get('profit', 0) + trade.get('fee', 0)

                        # 更新最高账户价值
                        if available_capital > highest_account_value:
                            highest_account_value = available_capital
                            lowest_account_value_after_peak = highest_account_value  # 重置最低账户价值
                        # 更新最低账户价值（在最高点之后）
                        if available_capital < lowest_account_value_after_peak:
                            lowest_account_value_after_peak = available_capital
                            # 计算最大回撤百分比
                            drawdown = (highest_account_value - lowest_account_value_after_peak) / highest_account_value * 100
                            if drawdown > max_drawdown:
                                max_drawdown = drawdown

                    if self.save_trades:
                        cursor.executemany(trade_sql, trade_values)
                        conn.commit()
                        trade_values.clear()

                # 清理内存
                del trades

                # 更新策略结果表中的最大回撤值
                update_sql = "UPDATE strategy_results SET max_drawdown = %s , successful_trades = %s WHERE id = %s"
                cursor.execute(update_sql, (max_drawdown, successful_trades, strategy_id))
                conn.commit()



            if self.row_reson_list and self.save_row_reson:
                with open(f'./static/strategy_info/row_reason_list_{strategy_id}.json', 'w', encoding='utf-8') as f:
                    json.dump(self.row_reson_list, f, ensure_ascii=False, indent=4)


            print_log(f"策略结果保存成功! 策略ID: {strategy_id}")
            print_log(f"总交易次数: {len(result['trades'])}")
            print_log(f"成功交易次数: {successful_trades}")
            print_log(f"最高账户价值: ${result['highest_account_value']:.4f}")
            print_log(f"最低账户价值: ${result['lowest_account_value']:.4f}")
            print_log(f"初始资金: ${result.get('initial_capital', 0):.4f}")
            print_log(f"最终资金: ${result.get('final_capital', 0):.4f}")


            print_log(f"最大回撤: {max_drawdown:.4f}%")
            print_log(f"总手续费: ${result.get('total_fees', 0):.4f}")
            print_log(f"总收益: ${result.get('profit_loss', 0):.4f} ({result.get('profit_percentage', 0):.1f}%)")


            return strategy_id


        except Exception as e:
            error_msg = traceback.format_exc()
            print_log(f"保存策略结果时发生错误: {str(e)} {error_msg}")
            print_log("错误详情:")
            traceback.print_exc()
            if 'conn' in locals():
                conn.rollback()
            sys.exit(1)  # 出错时终止程序
        finally:
            if 'conn' in locals():
                conn.close()

    def clear_strategy_data(self):
        """清空策略相关的数据表"""
        connection = None
        try:
            connection = self.get_db_connection()

            with connection.cursor() as cursor:
                # 清空strategy_trades表
                cursor.execute("TRUNCATE TABLE strategy_trades")
                # 清空strategy_results表
                cursor.execute("TRUNCATE TABLE strategy_results")

            connection.commit()
            print_log("已清空策略数据表")

        except Exception as e:
            error_msg = traceback.format_exc()
            print_log(f"清空数据表时发生错误: [{str(e)}]\n错误详情:\n{error_msg}")
            if connection:
                connection.rollback()
            raise
        finally:
            if connection:
                connection.close()

    def rerun_strategies(self, strategy_ids):
        """
        根据指定的策略ID重新执行策略分析

        Args:
            strategy_ids: 单个策略ID或策略ID列表

        Returns:
            list: 重新执行后的策略ID列表
        """
        if isinstance(strategy_ids, (int, str)):
            strategy_ids = [strategy_ids]  # 将单个ID转换为列表

        new_strategy_ids = []
        connection = None
        self.save_trades=1
        self.save_row_reson=1

        try:
            connection = self.get_db_connection()

            for strategy_id in strategy_ids:
                print_log(f"开始重新执行策略 ID: {strategy_id}")

                # 查询策略参数
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT start_time, end_time, currency, sell_rate, buy_rate,
                               rest_minutes, min_trigger_rest,
                               lookback_minutes_buy, lookback_minutes_sell
                        FROM strategy_results
                        WHERE id = %s
                    """, (strategy_id,))

                    strategy_params = cursor.fetchone()

                if not strategy_params:
                    print_log(f"未找到策略 ID: {strategy_id} 的参数")
                    continue

                # 提取参数
                start_time = strategy_params['start_time']
                end_time = strategy_params['end_time']
                currency = strategy_params['currency']
                sell_rate = float(strategy_params['sell_rate'])
                buy_rate = float(strategy_params['buy_rate'])
                rest_minutes = int(strategy_params['rest_minutes'])
                min_trigger_rest = int(strategy_params['min_trigger_rest'])
                lookback_minutes_buy = int(strategy_params['lookback_minutes_buy'])
                lookback_minutes_sell = int(strategy_params['lookback_minutes_sell'])

                # 重新执行策略
                result = self.analyze_rebound_strategy(
                    start_time=start_time,
                    end_time=end_time,
                    currency=currency,
                    sell_rate=sell_rate,
                    buy_rate=buy_rate,
                    rest_minutes=rest_minutes,
                    min_trigger_rest=min_trigger_rest,
                    lookback_minutes_buy=lookback_minutes_buy,
                    lookback_minutes_sell=lookback_minutes_sell,
                    is_live_trading=False
                )


                strategy_id=analyzer.save_strategy_results(result)


                pid = os.getpid()
                normal_log_file = f"params_log_{pid}.txt"
                
                if hasattr(print_log, 'file_handles'):
                    for filename, handle in print_log.file_handles.items():
                        try:
                            handle.close()
                            print_log(f"已关闭文件句柄: {filename}")
                        except Exception as e:
                            print_log(f"关闭文件句柄出错: {str(e)}")
                
                # 等待文件释放
                time.sleep(1)

                # 创建目标目录
                log_dir = os.path.join(".", "log", "strategy")
                os.makedirs(log_dir, exist_ok=True)
                
                # 检查并移动实盘日志文件
                if os.path.exists(normal_log_file):
                    target_path = os.path.join(log_dir, f"strategy_line_{strategy_id}_live_0.log")
                    shutil.move(normal_log_file, target_path)
                    print_log(f"已移动实盘策略日志到: {target_path}")


                new_strategy_ids.append(strategy_id)

                print_log(f"策略 ID: {strategy_id} 重新执行完成，新策略 ID: {strategy_id}")

            return new_strategy_ids

        except Exception as e:
            error_msg = traceback.format_exc()
            print_log(f"重新执行策略时发生错误: [{str(e)}]\n错误详情:\n{error_msg}")
            if connection:
                connection.rollback()
            raise
        finally:
            if connection:
                connection.close()

    def trade_condition(self, row, params):


        """判断当前价格是否符合买入或卖出条件"""
        current_position = params['current_position']
        last_position_price = params['last_position_price']
        last_high = params['last_high']
        last_low = params['last_low']
        last_close_time = params['last_close_time']
        last_entry_time = params['last_entry_time']
        sell_rate = params['sell_rate']
        buy_rate = params['buy_rate']
        rest_minutes = params['rest_minutes']
        current_index = params.get('current_index')  # 获取当前索引
        lookback_minutes_buy = params.get('lookback_minutes_buy', 4)  # 连续K线判断的回看时间
        lookback_minutes_sell = params.get('lookback_minutes_sell', 4)  # 连续K线判断的回看时间
        continuous_up_count = params.get('continuous_up_count', 0)  # 连续上涨计数器
        continuous_down_count = params.get('continuous_down_count', 0)  # 连续下跌计数器


        # 确保时间戳是字符串格式

        current_time = row['timestamp']

        # 计算持仓时间（分钟）
        if last_entry_time is None:
            entry_time_diff = 0
        else:
            # 去掉秒，秒设为00
            current_time_no_seconds = current_time.split(':')[0] + ':' + current_time.split(':')[1] + ':00'
            last_entry_time_no_seconds = last_entry_time.split(':')[0] + ':' + last_entry_time.split(':')[1] + ':00'
            entry_time_diff = (datetime.fromisoformat(current_time_no_seconds) - datetime.fromisoformat(last_entry_time_no_seconds)).total_seconds() / 60

        if last_close_time is None:
            time_diff=9999
        else:
            # 去掉秒，秒设为00
            current_time_no_seconds = current_time.split(':')[0] + ':' + current_time.split(':')[1] + ':00'
            last_close_time_no_seconds = last_close_time.split(':')[0] + ':' + last_close_time.split(':')[1] + ':00'
            time_diff = (datetime.fromisoformat(current_time_no_seconds) - datetime.fromisoformat(last_close_time_no_seconds)).total_seconds() / 60

        # 对比上次交易时间，同一分钟内跳过交易
        if self.is_same_minute(row['timestamp'], params.get('last_entry_time'), params.get('last_close_time')):
                return {
                    'action': None,
                    'reason': '同一分钟内跳过交易',
                    'trigger_price': 0,
                    'min_trigger_price': 0
                }


        now_time = time.time()


        # 从params中获取lock_time，如果不存在则设为0
        if 'lock_time' not in params:
            params['lock_time'] = 0

        if params['run_type']=='live' and now_time < params['lock_time']:
            print_log(f"连续交易区间，暂停交易逻辑 剩余时间：{round((params['lock_time']-now_time),1)}秒")
            return {
                'action': None,
                'reason': f'连续交易区间，暂停交易逻辑 剩余时间：{round((params["lock_time"]-now_time),1)}秒',
                'trigger_price': 0,
                            'min_trigger_price': 0
                        }





        if rest_minutes > 0 and time_diff <= rest_minutes:
            return {
                'action': None,
                'reason': f'休息时间未到，还需等待{rest_minutes-time_diff:.4f}分钟',
                'trigger_price': 0,
                            'min_trigger_price': 0
                        }
        # 无仓状态
        if current_position == 0:

            short_trigger_price = round(last_high * (1 - buy_rate), 5)  # 做空触发价格
            long_trigger_price = round(last_low * (1 + buy_rate), 5)  # 做多触发价格

            if last_low==0:
                current_rise_percentage=0.00000
            else:
                current_rise_percentage = round((row['close'] - last_low) / last_low * 100,3)

            if last_high==0:
                current_down_percentage = 0.00000
            else:
                current_down_percentage = round((row['close'] - last_high) / last_high * 100,3)

            # 检查是否可以开空

            if ( row['close'] <= short_trigger_price):

                #判断当前是上升,不满足开空条件
                if row['close'] > row['open']:

                    return {
                        'action': None,
                        'reason': f'{current_rise_percentage}% 当前K线为上升趋势，不满足开空条件',
                        'trigger_price': short_trigger_price,
                        'min_trigger_price': 0,
                        'current_percentage': current_rise_percentage
                    }


                # 使用计数器判断连续下跌趋势 和 连续下跌保持计数大于5
                if continuous_down_count >= lookback_minutes_buy:
                    if (params['run_type']=='live' and params['continuous_down_keep_count']<=params['max_continuous_keep_count']):
                        return {
                            'action': None,
                            'reason': f'{current_down_percentage}% 下跌状态保持只有{params["continuous_down_keep_count"]}次，不满足开空条件',
                            'trigger_price': short_trigger_price,
                            'min_trigger_price': 0,
                            'current_percentage': current_down_percentage
                        }


                    return {
                        'action': '开多' if params['reverse_buy'] == 1 else '开空',
                        'reason': f'{current_down_percentage}% 价格回调至触发价格{short_trigger_price:.5f}且连续{continuous_down_count}分钟下跌, 反向买入为1, 开多' if params['reverse_buy'] == 1 else f'{current_down_percentage}% 价格回调至触发价格{short_trigger_price:.5f}且连续{continuous_down_count}分钟下跌',
                        'trigger_price': row['close'],
                        'min_trigger_price': 0,
                        'current_percentage': current_down_percentage
                    }
                else:
                    return {
                        'action': None,
                        'reason': f'{current_down_percentage}% 未达到连续下跌开空条件,当前连续下跌{continuous_down_count}次',
                        'trigger_price': short_trigger_price,
                        'min_trigger_price': 0,
                        'current_percentage': current_down_percentage
                    }



            if (row['close'] >= long_trigger_price):


                 #判断下跌,不满足开多条件
                if row['close'] < row['open']:
                    return {
                        'action': None,
                        'reason': f'{current_down_percentage}% 当前K线为下跌趋势，不满足开多条件',
                        'trigger_price': long_trigger_price,
                        'min_trigger_price': 0,
                        'current_percentage': current_down_percentage
                    }

                # 使用计数器判断连续上涨趋势
                if continuous_up_count >= lookback_minutes_buy:

                    if (params['run_type']=='live' and params['continuous_up_keep_count']<=params['max_continuous_keep_count']):
                        return {
                            'action': None,
                            'reason': f'{current_rise_percentage}% 上升状态保持只有{params["continuous_up_keep_count"]}次，不满足开多条件',
                            'trigger_price': long_trigger_price,
                            'min_trigger_price': 0,
                            'current_percentage': current_rise_percentage
                        }



                    min_trigger_price = round(last_position_price * (1 + params['min_trigger_rate']), 5)
                    return {
                        'action': '开空' if params['reverse_buy'] == 1 else '开多',
                        'reason': f'{current_rise_percentage}% 价格反弹至触发价格{long_trigger_price:.5f}且连续{continuous_up_count}分钟上涨, 反向买入为1, 开空' if params['reverse_buy'] == 1 else f'{current_rise_percentage}% 价格反弹至触发价格{long_trigger_price:.5f}且连续{continuous_up_count}分钟上涨',
                        'trigger_price': row['close'],
                        'min_trigger_price': min_trigger_price,
                        'current_percentage': current_rise_percentage
                    }
                else:
                    return {
                        'action': None,
                        'reason': f'{current_rise_percentage}% 未达到连续上涨开多条件,当前连续上涨{continuous_up_count}分钟',
                        'trigger_price': long_trigger_price,
                        'min_trigger_price': 0,
                        'current_percentage': current_rise_percentage
                    }


            else:
                return {
                    'action': None,
                    'reason': f'未达到开仓条件 开多价{long_trigger_price:.5f} {current_rise_percentage}%  开空价{short_trigger_price:.5f} {current_down_percentage}% ',
                    'trigger_price': long_trigger_price,
                    'min_trigger_price': 0
                }

        # 空仓状态
        elif current_position < 0:

            close_short_trigger_price = round(last_low * (1 + sell_rate), 5)  # 反弹触发价格

            if last_low==0:
                current_rise_percentage=0;
            else:
                current_rise_percentage = round((row['close'] - last_low) / last_low * 100,3)

            min_trigger_price = round(last_position_price * (1 - params['min_trigger_rate']), 5)  # 最小触发止损价格


            #判断当前是下跌,不满足平空条件
            if row['close'] < row['open']:
                return {
                    'action': None,
                    'reason': f'当前K线为下降趋势，不满足平空条件 从低{current_rise_percentage}%',
                    'trigger_price': close_short_trigger_price,
                    'min_trigger_price': min_trigger_price,
                    'current_percentage': current_rise_percentage
                }




                # 检查是否平空
            if entry_time_diff > params['min_trigger_rest'] and row['close'] >= min_trigger_price:
                # if row['open'] >= min_trigger_price:
                #     tmp_trigger_price = row['open']
                # elif(row['low']<=min_trigger_price and row['high']>=min_trigger_price):
                #     tmp_trigger_price=min_trigger_price
                # else:
                #     tmp_trigger_price = row['high']
                tmp_trigger_price=row['close']

                return {
                    'action': '平空',
                    'reason': f' {current_rise_percentage}% 持仓超过{entry_time_diff}分钟大于系统{params["min_trigger_rest"]}分钟且价格突破{params["min_trigger_rate"]}%,收盘价{row["close"]:.5f}触发价{tmp_trigger_price:.5f}',
                    'trigger_price': tmp_trigger_price,
                    'min_trigger_price': min_trigger_price,
                    'current_percentage': current_rise_percentage
                }

            elif (row['close'] >= close_short_trigger_price):



                if continuous_up_count >= lookback_minutes_sell:

                    if (params['run_type']=='live' and params['continuous_up_keep_count']<=params['max_continuous_keep_count']):
                        return {
                            'action': None,
                            'reason': f'上升状态保持只有{params["continuous_up_keep_count"]}次，不满足平空条件 {current_rise_percentage}%',
                            'trigger_price': row['close'],
                            'min_trigger_price': min_trigger_price,
                            'current_percentage': current_rise_percentage,
                            'entry_time_diff': entry_time_diff
                        }

                    return {
                        'action': '平空',
                        'reason': f'价格反弹至触发价格{close_short_trigger_price:.5f}且连续{continuous_up_count}分钟上涨 {current_rise_percentage}%',
                        'trigger_price': row['close'],
                        'min_trigger_price': min_trigger_price,
                        'current_percentage': current_rise_percentage,
                        'entry_time_diff': entry_time_diff
                    }
                else:
                    return {
                        'action': None,
                        'reason': f'未达到连续上升平仓条件,目前差连续升{lookback_minutes_sell-continuous_up_count}次 {current_rise_percentage}%',
                        'trigger_price': close_short_trigger_price,
                        'min_trigger_price': min_trigger_price,
                        'current_percentage': current_rise_percentage,
                        'entry_time_diff': entry_time_diff
                    }



            else:
                return {
                    'action': None,
                    'reason': f'最高价{row["high"]:.5f}未达到平空触发价{close_short_trigger_price:.5f}，目前{current_rise_percentage}%',
                    'trigger_price': close_short_trigger_price,
                    'min_trigger_price': min_trigger_price,
                    'current_percentage': current_rise_percentage,
                    'entry_time_diff': entry_time_diff
                }

        # 多仓状态
        elif current_position > 0:

            close_long_trigger_price = round(last_high * (1 - sell_rate), 5)  # 下跌触发价格
            min_trigger_price = round(float(last_position_price) * (1 + params['min_trigger_rate']), 5)  # 最小触发价格

            if last_high==0:
                current_down_percentage=0.00000
            else:
                current_down_percentage = round((row['close'] - last_high) / last_high * 100,3)

            #判断当前是上升,不满足平多条件
            if row['close'] > row['open']:
                return {
                    'action': None,
                    'reason': f'当前K线为上升趋势，不满足平多条件 从高:{current_down_percentage}%',
                    'trigger_price': close_long_trigger_price,
                    'min_trigger_price': min_trigger_price,
                    'current_percentage': current_down_percentage,
                    'entry_time_diff': entry_time_diff
                }



            # 检查是否平多
            if entry_time_diff > params['min_trigger_rest'] and row['close'] <= min_trigger_price:

                        return {
                            'action': '平多',
                            'reason': f'{current_down_percentage}% 持仓超过{entry_time_diff}分钟 大于系统{params["min_trigger_rest"]}分钟且价格跌破{params["min_trigger_rate"]}% {row["close"]:.5f}',
                            'trigger_price': row['close'],
                            'min_trigger_price': min_trigger_price,
                            'current_percentage': current_down_percentage,
                            'entry_time_diff': entry_time_diff
                        }

            elif (row['close'] <= close_long_trigger_price):


                if continuous_down_count >= lookback_minutes_sell:


                    if (params['run_type']=='live' and params['continuous_down_keep_count']<=params['max_continuous_keep_count']):
                        return {
                            'action': None,
                                        'reason': f'{current_down_percentage}% 下跌状态保持只有{params["continuous_down_keep_count"]}次，不满足平多条件',
                                        'trigger_price': row['close'],
                                        'min_trigger_price': min_trigger_price,
                                        'current_percentage': current_down_percentage,
                                        'entry_time_diff': entry_time_diff
                        }

                    return {
                        'action': '平多',
                                    'reason': f'{current_down_percentage} % 开盘价格回调超过触发价格{close_long_trigger_price:.5f}且连续{continuous_down_count}分钟下跌',
                                    'trigger_price': row['close'],
                                    'min_trigger_price': min_trigger_price,
                                    'current_percentage': current_down_percentage,
                                    'entry_time_diff': entry_time_diff
                                }
                else:
                    return {
                        'action': None,
                        'reason': f'{current_down_percentage}% 未达到连续下跌平仓条件,还差连续下跌{lookback_minutes_sell-continuous_down_count}次',
                        'trigger_price': close_long_trigger_price,
                        'min_trigger_price': min_trigger_price,
                        'current_percentage': current_down_percentage,
                        'entry_time_diff': entry_time_diff
                    }


            else:
                return {
                    'action': None,
                'reason': f'{current_down_percentage}% 收盘价{row["close"]:.5f}未达到平多触发价 {close_long_trigger_price:.5f}',
                'trigger_price': close_long_trigger_price,
                'min_trigger_price': min_trigger_price,
                'current_percentage': current_down_percentage,
                'entry_time_diff': entry_time_diff
            }

        else:
            return {
                'action': None,
                'reason': f'{current_down_percentage}% 未达到开仓条件2',
                'trigger_price': close_long_trigger_price,
                'min_trigger_price': min_trigger_price,
                'current_percentage': current_down_percentage,
                'entry_time_diff': entry_time_diff
            }

    # 策略交易条件分析
    def trade_condition_analyze(self, row, params):


        """判断当前价格是否符合买入或卖出条件"""
        current_position = params['current_position']
        last_position_price = params['last_position_price']
        last_high = params['last_high']
        last_low = params['last_low']
        last_close_time = params['last_close_time']
        last_entry_time = params['last_entry_time']
        sell_rate = params['sell_rate']
        buy_rate = params['buy_rate']
        rest_minutes = params['rest_minutes']
        current_index = params.get('current_index')  # 获取当前索引
        lookback_minutes_buy = params.get('lookback_minutes_buy', 4)  # 连续K线判断的回看时间
        lookback_minutes_sell = params.get('lookback_minutes_sell', 4)  # 连续K线判断的回看时间
        continuous_up_count = params.get('continuous_up_count', 0)  # 连续上涨计数器
        continuous_down_count = params.get('continuous_down_count', 0)  # 连续下跌计数器


        analyze_about=0.3

        # 确保时间戳是字符串格式
        current_time = row['timestamp']

        # 计算持仓时间（分钟）
        if last_entry_time is None:
            entry_time_diff = 0
        else:
            entry_time_diff = (datetime.fromisoformat(current_time) - datetime.fromisoformat(last_entry_time)).total_seconds() / 60

        if last_close_time is None:
            time_diff=9999
        else:
            time_diff = (datetime.fromisoformat(current_time) - datetime.fromisoformat(last_close_time)).total_seconds() / 60



        if rest_minutes > 0 and time_diff <= rest_minutes:
            return {
                'action': None,
                'reason': f'休息时间未到，还需等待{rest_minutes-time_diff:.4f}分钟',
                'trigger_price': 0,
                            'min_trigger_price': 0
                        }




        # 无仓状态
        if current_position == 0:

            short_trigger_price = round(last_high * (1 - buy_rate), 5)  # 做空触发价格
            long_trigger_price = round(last_low * (1 + buy_rate), 5)  # 做多触发价格


            if last_low==0:
                current_rise_percentage=0.00000
            else:
                current_rise_percentage = round((row['high'] - last_low) / last_low * 100,3)

            if last_high==0:
                current_down_percentage = 0.00000
            else:
                current_down_percentage = round((row['low'] - last_high) / last_high * 100,3)

            # 检查是否可以开空

            if ( ((row['low'] <= short_trigger_price and short_trigger_price<=row['open'] ) or short_trigger_price>=row['high'])):


                if row['open']>short_trigger_price:
                    continuous_down_count=params['pre_continuous_down_count']+1
                elif row['high'] < short_trigger_price:
                    continuous_down_count=params['pre_continuous_down_count']+1
                else:
                    continuous_down_count=params['pre_continuous_down_count']



                # 使用计数器判断连续下跌趋势 和 连续下跌保持计数大于5
                if continuous_down_count >= lookback_minutes_buy:

                    min_trigger_price = last_position_price * (1 - params['min_trigger_rate'])

                    if row['high'] < short_trigger_price:
                        short_trigger_price=row['open']

                    return {
                        'action': '开多' if params['reverse_buy'] == 1 else '开空',
                        'reason': f'{current_down_percentage}% 价格回调至触发价格{short_trigger_price:.5f}且连续{continuous_down_count}分钟下跌, 反向买入为1, 开多' if params['reverse_buy'] == 1 else f'{current_down_percentage}% 价格回调至触发价格{short_trigger_price:.5f}且连续{continuous_down_count}分钟下跌',
                        'trigger_price': short_trigger_price,
                        'min_trigger_price': min_trigger_price,
                        'current_percentage': current_down_percentage,
                        'entry_time_diff': entry_time_diff
                    }
                else:
                    return {
                        'action': None,
                        'reason': f'{current_down_percentage}% 未达到连续下跌开空条件,当前连续下跌{continuous_down_count}次',
                        'trigger_price': short_trigger_price,
                        'min_trigger_price': 0,
                        'current_percentage': current_down_percentage,
                        'entry_time_diff': entry_time_diff
                    }




            if (((row['high'] >= long_trigger_price and long_trigger_price>=row['open']) or long_trigger_price<=row['low'])):

                if row['open']<=long_trigger_price:
                    continuous_up_count=params['pre_continuous_up_count']+1;
                else:
                    continuous_up_count=params['pre_continuous_up_count']

                # 使用计数器判断连续上涨趋势
                if continuous_up_count >= lookback_minutes_buy:

                    if row['low'] >= long_trigger_price:
                        long_trigger_price=row['open']

                    min_trigger_price = round(last_position_price * (1 + params['min_trigger_rate']), 5)
                    return {
                        'action': '开空' if params['reverse_buy'] == 1 else '开多',
                        'reason': f'{current_rise_percentage}% 价格反弹至触发价格{long_trigger_price:.5f}且连续{continuous_up_count}分钟上涨, 反向买入为1, 开空' if params['reverse_buy'] == 1 else f'{current_rise_percentage}% 价格反弹至触发价格{long_trigger_price:.5f}且连续{continuous_up_count}分钟上涨',
                        'trigger_price': long_trigger_price,
                        'min_trigger_price': min_trigger_price,
                        'current_percentage': current_rise_percentage,
                        'entry_time_diff': entry_time_diff
                    }
                else:
                    return {
                        'action': None,
                        'reason': f'{current_rise_percentage}% 未达到连续上涨开多条件,当前连续上涨{continuous_up_count}分钟',
                        'trigger_price': 0,
                        'min_trigger_price': 0,
                        'current_percentage': current_rise_percentage,
                        'entry_time_diff': entry_time_diff
                    }


            else:
                return {
                    'action': None,
                    'reason': f'未达到开仓条件 开多价{long_trigger_price:.5f} {current_rise_percentage}%  开空价{short_trigger_price:.5f} {current_down_percentage}% 最后最高价{last_high:.5f} 最后最低价{last_low:.5f}',
                    'trigger_price': long_trigger_price,
                    'min_trigger_price': 0,
                    'current_percentage': current_rise_percentage,
                    'entry_time_diff': entry_time_diff
                }

        # 空仓状态
        elif current_position < 0:

            close_short_trigger_price = round(last_low * (1 + sell_rate), 5)  # 反弹触发价格

            if last_low==0:
                current_rise_percentage=0;
            else:
                current_rise_percentage = round((row['close'] - last_low) / last_low * 100,3)

            min_trigger_price = round(last_position_price * (1 - params['min_trigger_rate']), 5)  # 最小触发止损价格



                # 检查是否平空
            if entry_time_diff > params['min_trigger_rest'] and row['high'] >= min_trigger_price:

                if row['low'] <= min_trigger_price and row['high']>=min_trigger_price:
                    tmp_trigger_price=min_trigger_price
                elif row['low']>min_trigger_price:
                    tmp_trigger_price=row['open']

                return {
                    'action': '平空',
                    'reason': f' {current_rise_percentage}% 持仓超过{entry_time_diff}分钟大于系统{params["min_trigger_rest"]}分钟且价格突破0.2%,收盘价{row["close"]:.5f}触发价{tmp_trigger_price:.5f}',
                    'trigger_price': tmp_trigger_price,
                    'min_trigger_price': min_trigger_price,
                    'current_percentage': current_rise_percentage,
                    'entry_time_diff': entry_time_diff
                }

            elif ((row['high'] >= close_short_trigger_price and close_short_trigger_price>=row['open'] ) or close_short_trigger_price<=row['low']):

                if row['open']<=close_short_trigger_price and close_short_trigger_price<=row['high']:
                    continuous_up_count=params['pre_continuous_up_count']+1;
                elif row['low']>close_short_trigger_price and row['open']<row['close']:
                    continuous_up_count=params['pre_continuous_up_count']+1;
                else:
                    continuous_up_count=params['pre_continuous_up_count']

                if continuous_up_count >= lookback_minutes_sell:

                    if row['low'] >= close_short_trigger_price:
                        close_short_trigger_price=row['open']

                    return {
                        'action': '平空',
                        'reason': f'价格反弹至触发价格{close_short_trigger_price:.5f}且连续{continuous_up_count}分钟上涨 {current_rise_percentage}%',
                        'trigger_price': close_short_trigger_price,
                        'min_trigger_price': min_trigger_price,
                        'current_percentage': current_rise_percentage,
                        'entry_time_diff': entry_time_diff
                    }
                else:
                    return {
                        'action': None,
                        'reason': f'未达到连续上升平仓条件,目前差连续升{lookback_minutes_sell-continuous_up_count}次 {current_rise_percentage}%',
                        'trigger_price': close_short_trigger_price,
                        'min_trigger_price': min_trigger_price,
                        'current_percentage': current_rise_percentage,   
                        'entry_time_diff': entry_time_diff
                    }



            else:
                return {
                    'action': None,
                    'reason': f'最高价{row["high"]:.5f}未达到平空触发价{close_short_trigger_price:.5f}，目前{current_rise_percentage}%',
                    'trigger_price': close_short_trigger_price,
                    'min_trigger_price': min_trigger_price,
                    'current_percentage': current_rise_percentage,
                    'entry_time_diff': entry_time_diff
                }

        # 多仓状态
        elif current_position > 0:

            close_long_trigger_price = round(last_high * (1 - sell_rate), 5)  # 下跌触发价格
            min_trigger_price = round(float(last_position_price) * (1 + params['min_trigger_rate']), 5)  # 最小触发价格

            if last_high==0:
                current_down_percentage=0
            else:
                current_down_percentage = round((row['close'] - last_high) / last_high * 100,3)



            # 检查是否平多
            if entry_time_diff > params['min_trigger_rest'] and row['high'] >= min_trigger_price:

                if row['low']<=min_trigger_price and row['high']>=min_trigger_price:
                    tmp_trigger_price=min_trigger_price
                elif row['high']<min_trigger_price:
                    tmp_trigger_price=row['open']
                else:
                    tmp_trigger_price=row['open']


                return {
                    'action': '平多',
                    'reason': f'{current_down_percentage}% 持仓超过{entry_time_diff}分钟 大于系统{params["min_trigger_rest"]}分钟且价格跌破{min_trigger_price}% {row["close"]:.5f}',
                    'trigger_price': tmp_trigger_price,
                    'min_trigger_price': min_trigger_price,
                    'current_percentage': current_down_percentage,
                    'entry_time_diff': entry_time_diff
                }



            elif ((row['low'] <= close_long_trigger_price and close_long_trigger_price<=row['open']) or close_long_trigger_price>=row['high']):


                if row['open']<=close_long_trigger_price and close_long_trigger_price>=row['low']:
                    continuous_down_count=params['pre_continuous_down_count']+1;
                elif row['high']<=close_long_trigger_price and row['open']>=row['close']:
                    continuous_down_count=params['pre_continuous_down_count']+1;
                else:
                    continuous_down_count=params['pre_continuous_down_count']




                if continuous_down_count >= lookback_minutes_sell:

                    if row['high'] <= close_long_trigger_price:
                        close_long_trigger_price=row['open']

                    return {
                        'action': '平多',
                                    'reason': f'{current_down_percentage} % 开盘价格回调超过触发价格{close_long_trigger_price:.5f}且连续{continuous_down_count}分钟下跌',
                                    'trigger_price': close_long_trigger_price,
                                    'min_trigger_price': min_trigger_price,
                                    'current_percentage': current_down_percentage,
                                    'entry_time_diff': entry_time_diff
                                }
                else:
                    return {
                        'action': None,
                        'reason': f'{current_down_percentage}% 未达到连续下跌平仓条件,还差连续下跌{lookback_minutes_sell-continuous_down_count}次',
                        'trigger_price': close_long_trigger_price,
                        'min_trigger_price': min_trigger_price,
                        'current_percentage': current_down_percentage,
                        'entry_time_diff': entry_time_diff
                    }


            else:
                return {
                    'action': None,
                'reason': f'{current_down_percentage}% 收盘价{row["close"]:.5f}未达到平多触发价 {close_long_trigger_price:.5f}',
                'trigger_price': close_long_trigger_price,
                'min_trigger_price': min_trigger_price,
                'current_percentage': current_down_percentage,
                'entry_time_diff': entry_time_diff
            }

        else:
            return {
                'action': None,
                'reason': f'{current_down_percentage}% 未达到开仓条件2',
                'trigger_price': 0,
                'min_trigger_price': 0,
                'current_percentage': current_down_percentage,
                'entry_time_diff': entry_time_diff
            }



    def execute_trade(self, price, available_amount, action: str, position_type: str, is_live_trading: bool, currency: str,last_position_price:float) -> dict:


            """
            执行交易并计算手续费和实际交易量

            测试调用示例:
            # 调试模式：
            # 1. 使用Python解释器直接调用:
            # python -c "from strategy_analyzer import StrategyAnalyzer; from db_config import DB_CONFIG; analyzer = StrategyAnalyzer(DB_CONFIG); result = analyzer.execute_trade(price=0.20240, available_amount=10, action='BUY', position_type='long', is_live_trading=True, currency='DOGE'); print_log(result)"
            #
            # 2. 使用pdb调试器:
            # python -m pdb -c "from strategy_analyzer import StrategyAnalyzer; from db_config import DB_CONFIG; analyzer = StrategyAnalyzer(DB_CONFIG); result = analyzer.execute_trade(price=0.21600, available_amount=20, action='BUY', position_type='long', is_live_trading=True, currency='DOGE'); print_log(result); quit()"
            #
            # 3. 使用IPython交互式调试:
            # ipython -c "from strategy_analyzer import StrategyAnalyzer; from db_config import DB_CONFIG; analyzer = StrategyAnalyzer(DB_CONFIG); result = analyzer.execute_trade(price=0.21600, available_amount=20, action='BUY', position_type='long', is_live_trading=True, currency='DOGE'); print_log(result)"
            """
            """
            执行交易并计算手续费和实际交易量

            Args:
                price: 交易价格
                available_amount: 可用数量(买入时为资金量，卖出时为币数量)
                action: 交易动作 ('BUY' or 'SELL')
                position_type: 仓位类型 ('long' or 'short')
                is_live_trading: 是否实盘交易
                currency: 币种
            Returns:
                dict: 包含交易结果的字典
            """
            fee = 0
            capital_after = 0
            amount = available_amount

            # 锁定触发交易事件.防止重复触发
            if is_live_trading==1:
                self.price_collector.lock_time = time.time()+5;


            if action == 'BUY':
                if position_type == 'long':
                    # 买入开多：先计算手续费，剩余资金用于购买
                    fee = -available_amount * self.fee_rate
                    capital_before = available_amount

                    capital_after = capital_before+fee

                    amount = capital_after / price
                    position_size = amount

                    side = 'buy'
                    pos_side = 'long'
                    profit=0

                else:  # short
                    # 买入平空：先计算手续费，剩余资金用于购买
                    fee = - available_amount * self.fee_rate
                    capital_before = available_amount

                    capital_after = capital_before+fee

                    amount = capital_after / price
                    position_size = amount
                    profit=0
                    side = 'sell'
                    pos_side = 'short'

            else:  # SELL
                if position_type == 'long':
                    # 卖出平多：直接使用数量计算总额和手续费
                    trade_value = price * amount

                    capital_before = last_position_price*amount

                    fee = -trade_value * self.fee_rate

                    position_size = amount


                    profit=(price-last_position_price)*amount
                    side = 'sell'
                    pos_side = 'long'




                else:  # short
                    # 卖出开空：先计算手续费，剩余资金用于做空
                    trade_value = price * amount

                    capital_before = last_position_price*amount

                    fee = -trade_value * self.fee_rate

                    position_size = amount


                    profit=(last_position_price-price)*amount
                    side = 'buy'
                    pos_side = 'short'



            result = {
                'fee': fee,
                'amount': amount,
                'entry_price': price,
                'position_size': position_size,
                'trade_status': 'success',
                'available_balance': 0,
                'profit': profit,
                'capital_before': capital_before,
                'capital_after': capital_before+fee+profit,
                'account_value': capital_before+fee+profit
            }

            if is_live_trading:


                # 计算实际合约张数
                contract = self.trading_executor.convert_contract_size(
                    currency=currency,
                    amount=available_amount-fee,
                    is_usdt=True,
                    price=price
                )
                if action == 'BUY':

                    contract_num = contract['contract_num']
                    coins_per_contract = contract['coins_per_contract']
                else:
                    coins_per_contract=contract['coins_per_contract']
                    contract_num = available_amount/coins_per_contract

                live_trade = self.trading_executor.execute_live_trade(side, pos_side, contract_num, price,coins_per_contract)
                self.price_collector.params['live_trade_detail'] = live_trade

                result['live_order_no'] = live_trade['order_id']
                result['live_trade_status'] = live_trade['status']
                result['entry_price'] = live_trade['price']
                result['position_size'] = live_trade['size']
                result['amount'] = live_trade['size']
                result['available_balance'] = self.price_collector.params['available_capital']-(live_trade['price']*live_trade['size'])
                result['fee']=-live_trade['price']*live_trade['size']*self.fee_rate

                if action == 'SELL' and position_type == 'short':
                    profit = (self.price_collector.params['last_position_price'] - result['entry_price']) * result['position_size']
                elif action == 'SELL' and position_type == 'long':
                    profit = (result['entry_price'] - self.price_collector.params['last_position_price']) * result['position_size']
                else:
                    profit = 0

                result['profit'] = profit
                result['capital_after'] = self.price_collector.params['available_capital']+result['fee']+profit
                result['account_value'] = result['capital_after']
                result['capital_before'] = self.price_collector.params['available_capital']

                self.price_collector.params['pre_last_high']=self.price_collector.params['last_high']
                self.price_collector.params['pre_last_low']=self.price_collector.params['last_low']

            return result


    def process_trade_logic(self, row, params):
        """
        处理交易逻辑
        params: {
            'position': str,  # 当前持仓状态：无仓、多仓、空仓
            'entry_price': Decimal,  # 入场价格
            'position_size': Decimal,  # 持仓数量
            'available_capital': Decimal,  # 可用资金
            'total_fee': Decimal,  # 总手续费
            'total_fees': Decimal,  # 总手续费
            'trades': list,  # 交易记录
            'last_high': Decimal,  # 最近高点
            'last_low': Decimal,  # 最近低点
            'total_profit': Decimal,  # 总收益
            'total_trades': int,  # 总交易次数
            'successful_trades': int,  # 成功交易次数
            'trade_log': list,  # 交易日志
            'profit': Decimal,  # 当前收益
            'account_value': Decimal,  # 账户价值(实时变动)
            'current_position': Decimal,  # 当前持仓数量
            'last_position_price': Decimal,  # 上次持仓价格
            'size': Decimal,  # 持仓数量
            'account_value_history': list,  # 账户价值历史记录
            'highest_account_value': Decimal,  # 最高账户价值
            'lowest_account_value': Decimal,  # 最低账户价值
            'is_live_trading': bool,  # 是否实盘交易
            'buy_rate': Decimal,  # 买入阈值
            'sell_rate': Decimal,  # 卖出阈值
        }
        """
        try:

            row_close = row.get('close')
            row_open = row.get('open')
            row_high = row.get('high')
            row_low = row.get('low')

            if params['run_type'] == 'live':
                row_close = row['close']
            else:
                params['pre_continuous_up_count'] = params.get('continuous_up_count')
                params['pre_continuous_down_count'] = params.get('continuous_down_count')


            if not hasattr(self, 'trading_executor'):
                print_log("策略分析器缺少交易执行器实例")
            current_price = row_close
            timestamp = row['timestamp']

            # 从参数中解构变量
            position = params.get('position', '无仓')  # 代替 params['position']
            entry_price = params['entry_price']
            position_size = params['position_size']
            available_capital = params['available_capital']
            total_fees = params['total_fees']
            trades = params['trades']

            if params['last_high'] == 0:
                last_high = row_high
                params['last_high']=last_high
            else:
                last_high = params['last_high']
                
            if params['last_low'] == 9999:  
                last_low = row_low
                params['last_low']=last_low
            else:
                last_low = params['last_low']
                
            total_profit = params['total_profit']
            total_trades = params['total_trades']
            successful_trades = params['successful_trades']
            trade_log = params['trade_log']
            profit = params['profit']
            account_value = params['account_value']
            current_position = params['current_position']
            last_position_price = params['last_position_price']
            size = params.get('size', 0)  # 使用get方法，如果没有size则默认为0
            high_to_low_pullback = 0
            low_to_high_rebound = 0
            rest_minutes = params['rest_minutes']
            last_close_time = params['last_close_time']
            last_entry_time = params['last_entry_time']

            # 初始化账户价值历史记录相关参数
            account_value_history = params.get('account_value_history', [])
            highest_account_value = params.get('highest_account_value', 0)
            lowest_account_value = params.get('lowest_account_value', 0)

            # 初始化局部变量
            action = "无操作"
            reason = "无理由"
            profit_rate = 0
            fee = 0

            # 在params初始化时添加新的计数器和上一次K线的价格记录
            continuous_up_count = params.get('pre_continuous_up_count', 0)
            continuous_down_count = params.get('pre_continuous_down_count', 0)
            last_candle_high = params.get('last_candle_high', row_high)  # 上一次K线的最高价
            last_candle_low = params.get('last_candle_low', row_low)    # 上一次K线的最低价

            # 判断当前K线趋势并更新计数器
            if row_close > row_open or (last_candle_high and row_high > last_candle_high and row_low > last_candle_low):  # 上涨趋势
                continuous_up_count += 1
                continuous_down_count = 0  # 重置下跌计数
            elif row_close < row_open or (last_candle_high and row_high < last_candle_high and row_low < last_candle_low):  # 下跌趋势
                continuous_down_count += 1
                continuous_up_count = 0  # 重置上涨计数
            else:  # 无明确趋势
                continuous_up_count = 0
                continuous_down_count = 0

            # 上涨下跌保持计数
            if row_close > row_open:
                params['continuous_up_keep_count'] += 1
                params['continuous_down_keep_count'] = 0
            else:
                params['continuous_up_keep_count'] = 0
                params['continuous_down_keep_count'] += 1

            if row['is_complete'] == 1:
                # 重置连续交易计数
                params['continuous_up_keep_count'] = 0
                params['continuous_down_keep_count'] = 0

                params['pre_continuous_up_count'] = params.get('continuous_up_count')
                params['pre_continuous_down_count'] = params.get('continuous_down_count')


            # 更新params中的计数器和上一次K线价格
            params['continuous_up_count'] = continuous_up_count
            params['continuous_down_count'] = continuous_down_count

            # 判断趋势
            trend = "上升" if row_close > row_open else "下降"


            if params['run_type']=='live':
                trade_condition = self.trade_condition(row, params)
            else:
                trade_condition = self.trade_condition_analyze(row, params)


            params['trade_condition'] = trade_condition
            params['entry_time_diff'] = trade_condition.get('entry_time_diff', 0)

            if trade_condition['action'] == '开空':
                action = "开空"
                position = "空仓"
                entry_price = trade_condition['trigger_price']  # 使用触发价格作为入场价

                # 使用execute_trade方法执行交易
                trade_result = self.execute_trade(
                    price=entry_price,
                    available_amount=params['available_capital'],
                    action='BUY',
                    position_type='short',
                    is_live_trading=params['is_live_trading'],
                    currency=params.get('currency', 'DOGE'),
                    last_position_price=params['last_position_price']
                )
                params['live_trade_status'] = trade_result.get('live_trade_status', '')
                params['live_order_no'] = trade_result.get('live_order_no', '')
                position_size = trade_result['position_size']
                fee = trade_result['fee']

                params['total_fees'] += fee
                params['size'] = position_size  # 更新size参数
                params['position_size'] = position_size  # 同时更新position_size

                reason = trade_condition['reason']

                # 更新账户价值
                params['account_value'] = trade_result['capital_after']
                params['available_capital'] = trade_result['capital_after']

                # 获取实盘数据（如果存在）
                live_available_balance = params.get('live_available_balance', 0)
                live_total_equity = params.get('live_total_equity', 0)
                live_position_value = params.get('live_position_value', 0)
                live_unrealized_pnl = params.get('live_unrealized_pnl', 0)

                trades.append({
                    'timestamp': timestamp,
                    'position_type': 'short',
                    'price': float(trade_result['entry_price']),
                    'size': float(position_size),  # 使用position_size作为size
                    'amount': float(trade_result['amount']),
                    'reason': reason,
                    'action': 'BUY',
                    'profit': trade_result['profit'],
                    'fee': float(fee),
                    'capital_before': float(trade_result['capital_before']),
                    'capital_after': float(trade_result['capital_after']),
                    'trigger_reason': reason,
                    'market_price': float(entry_price),
                    'account_value': float(trade_result['capital_after']),
                    'entry_price': float(trade_result['entry_price']),  # 添加入场价格
                    'position_size': float(position_size),  # 添加当前持仓
                    'last_low': float(params['last_low']),
                    'last_high': float(last_high),
                    'live_order_no': trade_result.get('live_order_no', ''),
                    'min_trigger_price': float(trade_condition['min_trigger_price']),
                    'is_live_trading': params['is_live_trading'],
                    'order_book': [],
                    'live_available_balance': float(live_available_balance),  # 添加实盘可用保证金
                    'live_total_equity': float(live_total_equity),            # 添加实盘总资产
                    'live_position_value': float(live_position_value),        # 添加实盘持仓价值
                    'live_unrealized_pnl': float(live_unrealized_pnl)         # 添加实盘未实现盈亏
                })

                # 重置最高点和最低点为当前价格，避免重复触发
                last_high = entry_price
                last_low = entry_price

                # params['pre_continuous_up_count'] = params['continuous_up_count']
                # params['pre_continuous_down_count'] = params['continuous_down_count']

                continuous_up_count = 0
                continuous_down_count = 0

                params['current_position'] = -position_size
                last_position_price = entry_price
                last_entry_time = timestamp

            if trade_condition['action'] == '开多':
                action = "开多"
                position = "多仓"
                entry_price = trade_condition['trigger_price'] # 使用触发价格作为入场价

                # 使用execute_trade方法执行交易
                trade_result = self.execute_trade(
                    price=entry_price,
                    available_amount=params['available_capital'],
                    action='BUY',
                    position_type='long',
                    is_live_trading=params['is_live_trading'],
                    currency=params.get('currency', 'DOGE'),
                    last_position_price=params['last_position_price']
                )

                params['live_trade_status'] = trade_result.get('live_trade_status', '')
                params['live_order_no'] = trade_result.get('live_order_no', '')

                params['position_size'] = trade_result['position_size']
                fee = trade_result['fee']

                params['total_fees'] += fee
                params['account_value'] = trade_result['capital_after']
                params['size'] = trade_result['position_size']  # 更新size参数

                params['available_capital'] = trade_result['capital_after']

                reason = trade_condition['reason']

                # 获取实盘数据（如果存在）
                live_available_balance = params.get('live_available_balance', 0)
                live_total_equity = params.get('live_total_equity', 0)
                live_position_value = params.get('live_position_value', 0)
                live_unrealized_pnl = params.get('live_unrealized_pnl', 0)

                trades.append({
                    'timestamp': timestamp,
                    'position_type': 'long',
                    'price': float(trade_result['entry_price']),
                    'size': float(params['position_size']),  # 使用position_size作为size
                    'amount': float(trade_result['amount']),
                    'reason': reason,
                    'action': 'BUY',
                    'profit': trade_result['profit'],
                    'fee': float(fee),
                    'capital_before': float(trade_result['capital_before']),
                    'capital_after': float(trade_result['capital_after']),
                    'trigger_reason': reason,
                    'market_price': float(entry_price),
                    'account_value': float(trade_result['capital_after']),
                    'entry_price': float(trade_result['entry_price']),  # 添加入场价格
                    'position_size': float(params['position_size']),
                    'last_low': float(params['last_low']),
                    'last_high': float(params['last_high']),
                    'live_order_no': trade_result.get('live_order_no', ''),
                    'min_trigger_price': float(trade_condition['min_trigger_price']),
                    'is_live_trading': params['is_live_trading'],
                    'order_book': [],
                    'live_available_balance': float(live_available_balance),  # 添加实盘可用保证金
                    'live_total_equity': float(live_total_equity),            # 添加实盘总资产
                    'live_position_value': float(live_position_value),        # 添加实盘持仓价值
                    'live_unrealized_pnl': float(live_unrealized_pnl)         # 添加实盘未实现盈亏
                })

                # 重置最高点和最低点为当前价格，避免重复触发
                last_high = entry_price
                last_low = row['low']

                # params['pre_continuous_up_count'] = params['continuous_up_count']
                # params['pre_continuous_down_count'] = params['continuous_down_count']

                continuous_up_count = 0
                continuous_down_count = 0

                params['current_position'] = params['position_size']
                last_position_price = entry_price
                last_entry_time = timestamp

            if trade_condition['action'] == '平空':
                action = "平空"
                # 使用触发价格（即达到阈值时的价格）作为交易价格
                trigger_price = trade_condition['trigger_price']

                # 平空仓
                trade_result = self.execute_trade(trigger_price, abs(position_size), 'SELL', 'short', params['is_live_trading'], params.get('currency', 'DOGE'), params['last_position_price'])
                params['live_trade_status'] = trade_result.get('live_trade_status', '')
                params['live_order_no'] = trade_result.get('live_order_no', '')

                params['total_fees'] += trade_result['fee']

                # 更新账户价值
                params['account_value'] = trade_result['capital_after']
                params['available_capital'] = trade_result['capital_after']

                action = "平空"

                reason = trade_condition['reason']
                # 获取实盘数据（如果存在）
                live_available_balance = params.get('live_available_balance', 0)
                live_total_equity = params.get('live_total_equity', 0)
                live_position_value = params.get('live_position_value', 0)
                live_unrealized_pnl = params.get('live_unrealized_pnl', 0)

                trades.append({
                    'timestamp': timestamp,
                    'position_type': 'close_short',
                    'price': float(trade_result['entry_price']),
                    'size': float(abs(trade_result['position_size'])),
                    'amount': float(trade_result['amount']),
                    'profit': float(trade_result['profit']),
                    'fee': float(trade_result['fee']),
                    'reason': reason,
                    'action': 'SELL',
                    'capital_before': float(trade_result['capital_before']),
                    'capital_after': float(trade_result['capital_after']),
                    'trigger_reason': reason,
                    'market_price': float(trigger_price),
                    'account_value': float(trade_result['capital_after']),
                    'entry_price': float(trade_result['entry_price']),  # 添加入场价格
                    'position_size': float(abs(trade_result['position_size'])),  # 持仓数量
                    'last_low': float(params['last_low']),
                    'last_high': float(params['last_high']),
                    'live_order_no': trade_result.get('live_order_no', ''),
                    'min_trigger_price': float(trade_condition['min_trigger_price']),
                    'is_live_trading': params['is_live_trading'],
                    'order_book': [],
                    'live_available_balance': float(live_available_balance),  # 添加实盘可用保证金
                    'live_total_equity': float(live_total_equity),            # 添加实盘总资产
                    'live_position_value': float(live_position_value),        # 添加实盘持仓价值
                    'live_unrealized_pnl': float(live_unrealized_pnl)         # 添加实盘未实现盈亏
                })

                # 平仓后重置所有持仓相关的变量
                params['position'] = "无仓"
                params['entry_price'] = 0
                params['position_size'] = 0
                params['current_position'] = 0
                params['last_position_price'] = 0
                params['size'] = 0
                entry_price = 0
                position_size = 0
                last_position_price = 0
                size = 0

                params['total_profit'] += float(trade_result['profit'])
                params['available_capital'] = trade_result['capital_after']

                last_low = trigger_price
                last_high = trigger_price

                # params['pre_continuous_up_count'] = params['continuous_up_count']
                # params['pre_continuous_down_count'] = params['continuous_down_count']

                continuous_up_count = 0
                continuous_down_count = 0

                # 更新上次平仓时间
                last_close_time = timestamp
                last_entry_time = None

            if trade_condition['action'] == '平多':
                trigger_price = trade_condition['trigger_price']
                # 平多仓，使用触发价格
                trade_result = self.execute_trade(trigger_price, position_size, 'SELL', 'long', params['is_live_trading'], params.get('currency', 'DOGE'), params['last_position_price'])

                params['live_trade_status'] = trade_result.get('live_trade_status', '')
                params['live_order_no'] = trade_result.get('live_order_no', '')

                params['total_fees'] += trade_result['fee']

                action = "平多"
                reason = trade_condition['reason']
                # 获取实盘数据（如果存在）
                live_available_balance = params.get('live_available_balance', 0)
                live_total_equity = params.get('live_total_equity', 0)
                live_position_value = params.get('live_position_value', 0)
                live_unrealized_pnl = params.get('live_unrealized_pnl', 0)

                trades.append({
                    'timestamp': timestamp,
                    'position_type': 'close_long',
                    'price': float(trade_result['entry_price']),
                    'size': float(trade_result['position_size']),
                    'amount': float(trade_result['amount']),
                    'profit': float(trade_result['profit']),
                    'fee': float(trade_result['fee']),
                    'reason': reason,
                    'action': 'SELL',
                    'capital_before': float(trade_result['capital_before']),
                    'capital_after': float(trade_result['capital_after']),
                    'trigger_reason': reason,
                    'market_price': float(trigger_price),
                    'account_value': float(trade_result['capital_after']),
                    'entry_price': float(entry_price),  # 添加入场价格
                    'position_size': float(trade_result['position_size']),
                    'last_low': float(params['last_low']),
                    'last_high': float(params['last_high']),
                    'live_order_no': trade_result.get('live_order_no', ''),
                    'min_trigger_price': float(trade_condition['min_trigger_price']),
                    'is_live_trading': params['is_live_trading'],
                    'order_book': [],
                    'live_available_balance': float(live_available_balance),  # 添加实盘可用保证金
                    'live_total_equity': float(live_total_equity),            # 添加实盘总资产
                    'live_position_value': float(live_position_value),        # 添加实盘持仓价值
                    'live_unrealized_pnl': float(live_unrealized_pnl)         # 添加实盘未实现盈亏
                })

                # 平仓后重置所有持仓相关的变量
                params['position'] = "无仓"
                params['entry_price'] = 0
                params['position_size'] = 0
                params['current_position'] = 0
                params['last_position_price'] = 0
                params['size'] = 0
                params['account_value'] = float(trade_result['capital_after'])
                params['available_capital'] = trade_result['capital_after']

                params['total_profit'] += float(trade_result['profit'])

                position = "无仓"
                entry_price = 0
                position_size = 0
                last_position_price = 0
                size = 0

                last_low = trigger_price
                last_high = trigger_price

                # params['pre_continuous_up_count'] = params['continuous_up_count']
                # params['pre_continuous_down_count'] = params['continuous_down_count']

                continuous_up_count = 0
                continuous_down_count = 0
                # 更新上次平仓时间
                last_close_time = timestamp
                last_entry_time = None

            else:
                action = "无操作"
                reason = "无理由"

            # 确保所有变量都有默认值
            profit_rate = profit_rate if 'profit_rate' in locals() else 0.0
            action = action if 'action' in locals() else "无操作"
            reason = reason if 'reason' in locals() else "无理由"


            params['min_trigger_price'] = trade_condition.get('min_trigger_price',0)
            params['trigger_price'] = trade_condition.get('trigger_price',0)
            params['current_percentage'] = round(trade_condition.get('current_percentage',0),3)

            # 更新交易统计
            if action in ["开多", "开空"]:
                params['total_trades'] += 1
                if profit > 0:
                    params['successful_trades'] += 1

            if params['entry_price'] and params['entry_price'] > 0:
                profit_rate = ((current_price - params['entry_price']) / params['entry_price'] - self.fee_rate) * 100
            else:
                profit_rate = 0

            # 更新持仓信息
            if position == "无仓":
                # 无仓位时，所有持仓相关的数值都应该为0
                position_size = 0
                size = 0
                params['position_size'] = 0
                params['size'] = 0
                params['current_position'] = 0
                params['entry_price'] = 0
                params['last_position_price'] = 0
            else:
                # 有仓位时正常更新持仓信息
                position_size = params['position_size']
                params['size'] = position_size  # 确保size和position_size一致

            # 更新账户价值
            if position == "无仓":
                params['account_value'] = params['available_capital']
            else:
                # 有仓位时才计算未实现盈亏
                if position == "多仓":
                    unrealized_profit = (current_price - entry_price) * position_size if entry_price > 0 else 0
                else:  # 空仓
                    unrealized_profit = (entry_price - current_price) * position_size if entry_price > 0 else 0
                params['account_value'] = params['available_capital'] + unrealized_profit

            if row['is_complete'] == 1 and params["run_type"] == 'live' and len(account_value_history) > 0:
                # 更新账户价值历史记录
                if round(float(params['account_value']), 2) != round(float(account_value_history[-1]['account_value']), 2):
                    account_value_history.append({
                        'timestamp': timestamp,
                        'account_value': round(float(params['account_value']), 4),
                        'total_fees': round(float(params['total_fees']), 4),
                        'total_profit': round(float(params['total_profit']), 4)
                    })

            # 更新最高和最低账户价值
            if params['account_value'] > highest_account_value:
                highest_account_value = params['account_value']
            if params['account_value'] < lowest_account_value:
                lowest_account_value = params['account_value']

            # 更新params中的历史记录
            params['account_value_history'] = account_value_history
            params['highest_account_value'] = highest_account_value
            params['lowest_account_value'] = lowest_account_value

            # 更新最高点和最低点的逻辑
            if params['run_type'] == 'analyze':
                # 判断当前K线趋势并更新计数器
                if row['close'] > row['open'] or (last_candle_high >0 and last_candle_low>0 and row['high'] >= last_candle_high and row['low'] >= last_candle_low):  # 上涨趋势
                    continuous_up_count = params['pre_continuous_up_count'] + 1
                    continuous_down_count = 0  # 重置下跌计数
                elif row['close'] < row['open'] or (last_candle_high >0 and last_candle_low>0 and row['high'] <= last_candle_high and row['low'] <= last_candle_low):  # 下跌趋势
                    continuous_down_count = params['pre_continuous_down_count'] + 1
                    continuous_up_count = 0  # 重置上涨计数
                else:  # 无明确趋势
                    continuous_up_count = 0
                    continuous_down_count = 0

            




            params.update({
                'position': position,
                'entry_price': entry_price,
                'trades': trades,
                'total_trades': total_trades,
                'successful_trades': successful_trades,
                'trade_log': trade_log,
                'profit': profit,
                'trade_condition': trade_condition,
                'account_value_history': account_value_history,  # 添加账户价值历史记录
                'highest_account_value': highest_account_value,  # 添加最高账户价值
                'lowest_account_value': lowest_account_value,     # 添加最低账户价值
                'final_capital': params['account_value'],  # 添加最终账户价值
            })


            params['end_time'] = row['timestamp'].split('+')[0];



            if row['is_complete'] == 1 and self.save_trades == 1:
                print_params(self, params, row)


            if row['is_complete'] == 1:
                    
                params['last_candle_high'] = row['high']
                params['last_candle_low'] = row['low']

                if row_high > params['last_high']:
                    last_high = row_high
                if row_low < params['last_low']:
                    last_low = row_low

            params.update({
                'last_high': last_high,
                'last_low': last_low,
                'continuous_up_count': continuous_up_count,
                'continuous_down_count': continuous_down_count,
                'last_position_price': last_position_price,
                'last_close_time': last_close_time,
                'last_entry_time': last_entry_time
            })

            return params

        except Exception as e:
            error_msg = traceback.format_exc()
            print_log(f"处理交易逻辑时发生错误: [{str(e)}]\n错误详情:\n{error_msg}")
            raise


    def analyze_rebound_strategy_task(self, sell_rate=0.02, buy_rate=0.02, min_trigger_rest=5, rest_minutes=0, start_time=None, end_time=None, lookback_minutes_buy=4, lookback_minutes_sell=4, currency='DOGE', is_live_trading=False, reverse_buy=0):
        """
        将反弹策略分析任务写入数据库
        :param sell_rate: 卖出阈值 (0.01-0.10)
        :param buy_rate: 买入阈值 (0.01-0.10)
        :param min_trigger_rest: 最小触发间隔时间（分钟）
        :param rest_minutes: 交易间隔休息时间（分钟）
        :param start_time: 开始时间，格式为datetime对象
        :param end_time: 结束时间，格式为datetime对象
        :param lookback_minutes_buy: 买入连续K线判断的回看时间（分钟）
        :param lookback_minutes_sell: 卖出连续K线判断的回看时间（分钟）
        :param currency: 交易币种
        :param is_live_trading: 是否实盘交易
        :param reverse_buy: 是否反向买入
        :return: 任务ID
        """
        try:
            # 获取当前时间作为任务创建时间
            created_at = datetime.now()

            # 获取主机名作为客户端标识
            import socket
            client_id = socket.gethostname()

            # 连接数据库
            with self.get_db_connection() as conn:
                cursor = conn.cursor()

                # 插入任务记录
                insert_sql = """
                INSERT INTO strategy_analysis_tasks (
                    sell_rate, buy_rate,
                    min_trigger_rest, rest_minutes, start_time, end_time, lookback_minutes_buy,
                    lookback_minutes_sell, currency, status, client_id, created_at, reverse_buy
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                cursor.execute(insert_sql, (
                    sell_rate, buy_rate,
                    min_trigger_rest, rest_minutes, start_time, end_time, lookback_minutes_buy,
                    lookback_minutes_sell, currency, 'pending', client_id, created_at, reverse_buy
                ))

                # 获取插入的任务ID
                task_id = cursor.lastrowid

                # 提交事务
                conn.commit()

                print_log(f"已创建策略分析任务，ID: {task_id}")
                return task_id

        except Exception as e:
            error_msg = traceback.format_exc()
            print_log(f"创建策略分析任务时发生错误: [{str(e)}]\n错误详情:\n{error_msg}")
            return None

    def analyze_rebound_strategy(self, sell_rate=0.02, buy_rate=0.02, min_trigger_rest=5, rest_minutes=0, start_time=None, end_time=None, lookback_minutes_buy=4, lookback_minutes_sell=4, currency='DOGE', is_live_trading=False, reverse_buy=0):
        """
        分析反弹策略（包含做多和做空）
        :param sell_rate: 卖出阈值 (0.01-0.10)
        :param buy_rate: 买入阈值 (0.01-0.10)
        :param min_trigger_rest: 最小触发间隔时间（分钟）
        :param rest_minutes: 交易间隔休息时间（分钟）
        :param start_time: 开始时间，格式为datetime对象，如果为None则使用昨天的开始时间
        :param end_time: 结束时间，格式为datetime对象，如果为None则使用昨天的结束时间
        :param lookback_minutes: 连续K线判断的回看时间（分钟）
        :param currency: 交易币种
        :param is_live_trading: 是否实盘交易
        :param reverse_buy: 是否反向买入
        :return: 策略分析结果
        """
        # 设置实盘交易标志
        self.is_live_trading = is_live_trading

        try:
            if _stop_event:  # 检查全局停止事件
                return None

            # 如果没有提供时间参数，使用昨天的时间范围
            if start_time is None or end_time is None:
                yesterday = (datetime.now() - timedelta(days=1))
                start_time = start_time or yesterday.replace(hour=0, minute=0, second=0)
                end_time = end_time or yesterday.replace(hour=23, minute=59, second=59)

            # 获取价格数据
            key = f"{start_time}{end_time}{currency}"

            if not hasattr(self, 'price_data'):
                self.price_data = {}

            if key not in self.price_data:
                print_log(f"没有价格数据，开始获取价格数据: [{key}]")
                self.price_data[key] = self.get_price_data(start_time, end_time, currency)

            print_log("获取价格数据完成")

            if key not in self.price_data or not self.price_data[key] or _stop_event:
                print_log(f"未找到从 {start_time} 到 {end_time} 的价格数据")
                return None

            # 初始化策略报告
            strategy_report = []

            # 初始化变量
            position = "无仓"  # 当前持仓状态：无仓、多仓、空仓
            entry_price = 0  # 入场价格
            position_size = 0  # 持仓数量
            available_capital = float(self.initial_capital)  # 可用资金
            total_fees = 0  # 总手续费
            trades = []  # 交易记录
            total_profit = 0  # 总收益
            total_trades = 0  # 总交易次数
            successful_trades = 0  # 成功交易次数
            trade_log = []  # 交易日志
            profit = 0  # 初始化profit变量
            account_value = self.initial_capital  # 账户价值
            available_capital = self.initial_capital  # 可用资金
            current_position = 0  # 当前持仓数量
            last_position_price = 0  # 上次持仓价格

            strategy_report.append("\n时间                | 操作       | 持仓状态 | 开盘价   | 最高价   | 最低价   | 收盘价   | 最高点 | 最低点 | 趋势   | 跌幅/涨幅       | 盈利率 | 手续费| 账户价值  | 操作   | 原因  | 可用资金")
            strategy_report.append("-" * 140)

            # 初始化参数字典
            params = {
                'position': "无仓",
                'entry_price': 0,
                'position_size': 0,
                'available_capital': float(self.initial_capital),
                'total_fees': 0,
                'trades': [],
                'last_high': 0,
                'last_low': 9999,
                'total_profit': 0,
                'total_trades': 0,
                'successful_trades': 0,
                'trade_log': [],
                'profit': 0,
                'account_value': self.initial_capital,
                'current_position': 0,
                'last_position_price': 0,
                'sell_rate': sell_rate,
                'buy_rate': buy_rate,
                'size': 0,
                'rest_minutes': rest_minutes,
                'min_trigger_rest': min_trigger_rest,
                'last_close_time': None,
                'last_entry_time': None,
                'account_value_history': [],
                'highest_account_value': self.initial_capital,
                'lowest_account_value': self.initial_capital,
                'initial_capital': self.initial_capital,
                'lookback_minutes_buy': lookback_minutes_buy,  # 添加连续K线判断的回看时间
                'lookback_minutes_sell': lookback_minutes_sell,  # 添加连续K线判断的回看时间
                'strategy_report': strategy_report,  # 添加策略报告列表
                'continuous_up_count': 0,  # 添加连续上涨计数器
                'continuous_down_count': 0,  # 添加连续下跌计数器
                'last_candle_high': 0,  # 添加上一次K线最高价
                'last_candle_low': 0,   # 添加上一次K线最低价
                'is_live_trading': is_live_trading,  # 添加实盘交易标志
                'available_balance': 0,
                'continuous_up_keep_count': 0,
                'continuous_down_keep_count': 0,
                'max_continuous_keep_count': 5,
                'min_trigger_rate': 0.0004,
                'pre_row': None,
                'run_type': 'analyze',
                'reverse_buy': reverse_buy,  # 反向买入标志,达到上升条件. 反而做空
                'start_time': start_time,  # 添加开始时间
                'end_time': end_time,  # 添加结束时间
                'currency': currency,
            }

            if os.path.exists("log/info.log"):
                os.remove("log/info.log")

            self.row_reson_list = {}

            # 遍历每个时间点
            for index, row in enumerate(self.price_data[key]):
                if _stop_event:  # 检查全局停止事件
                    print_log("策略分析被中断")
                    break

                try:
                    # 转换数据类型
                    row['high'] = float(row['high'])
                    row['low'] = float(row['low'])
                    row['close'] = float(row['close'])
                    row['open'] = float(row['open'])

                    # 添加当前索引到参数中
                    params['current_index'] = index

                    # 调用交易逻辑处理方法
                    params = self.process_trade_logic(row, params)
                    # 将日志信息封装成字典
                    row_reson_dict = {
                        "timestamp": row['timestamp'],
                        "close": row['close'],
                        "last_high": params['last_high'],
                        "last_low": params['last_low'],
                        "continuous_up_count": params['continuous_up_count'],
                        "continuous_down_count": params['continuous_down_count'],
                        "trade_condition": params['trade_condition']['reason']
                    }
                    # 将日志字典添加到列表中
                    self.row_reson_list[row['timestamp']] = row_reson_dict

                    params['pre_row'] = row

                except Exception as e:
                    error_msg = traceback.format_exc()
                    print_log(f"处理交易逻辑时发生错误: [{str(e)}]\n错误详情:\n{error_msg}")
                    raise

            # 在结束前检查是否还有未平仓的位位
            if params['current_position'] != 0:
                timestamp = row['timestamp']
                current_price = row['close']

                # 计算平仓的收益和手续费
                if params['current_position'] > 0:  # 持有多仓
                    trade_result = self.execute_trade(current_price, params['current_position'], 'SELL', 'long', params['is_live_trading'], params.get('currency', 'DOGE'), params['last_position_price'])

                    params['total_fees'] += trade_result['fee']
                    params['total_profit'] += trade_result['profit']
                    # 添加除零保护
                    if params['entry_price'] and params['entry_price'] > 0:
                        profit_rate = ((current_price - params['entry_price']) / params['entry_price'] - self.fee_rate) * 100
                    else:
                        profit_rate = 0
                    action = "SELL"
                    position_type = "close_long"
                    reason = "策略结束强制平仓"
                else:  # 持有空仓
                    trade_result = self.execute_trade(current_price, abs(params['current_position']), 'SELL', 'short', params['is_live_trading'], params.get('currency', 'DOGE'), params['last_position_price'])
                    params['total_fees'] += trade_result['fee']
                    params['total_profit'] += trade_result['profit']
                    # 添加除零保护
                    if params['entry_price'] and params['entry_price'] > 0:
                        profit_rate = ((params['entry_price'] - current_price) / params['entry_price'] * 100) if params['entry_price'] and params['entry_price'] > 0 else 0
                    else:
                        profit_rate = 0
                    action = "SELL"
                    position_type = "close_short"
                    reason = f"策略结束强制平仓 利润: {trade_result['profit']:.4f} 利润率: {float(profit_rate):.4f}%"

                # 更新账户状态
                capital_after = params['available_capital'] + trade_result['profit'] + trade_result['fee']
                params['account_value'] = capital_after

                params['trades'].append({
                    'timestamp': timestamp,
                    'position_type': position_type,
                    'price': float(current_price),
                    'size': float(abs(params['current_position'])),
                    'amount': float(capital_after),
                    'profit': float(trade_result['profit']),
                    'fee': float(trade_result['fee']),
                    'reason': reason,
                    'action': action,
                    'capital_before': float(params['available_capital']),
                    'capital_after': float(capital_after),
                    'trigger_reason': "策略结束平仓",
                    'market_price': float(current_price),
                    'account_value': float(capital_after),
                    'entry_price': float(params['entry_price']),  # 添加入场价格
                    'position_size': float(params['position_size']),
                    'last_high': float(params['last_high']),
                    'last_low': float(params['last_low']),
                    'min_trigger_price': float(params['last_low']),
                })

                params['available_capital'] = capital_after
                params['current_position'] = 0
                params['entry_price'] = 0  # 重置入场价格
                params['size'] = 0  # 重置持币数量

                # 更新上次平仓时间
                params['last_close_time'] = timestamp

            # 计算最终结果
            params['final_capital'] = params['available_capital']
            params['profit_loss'] = params['total_profit']+params['total_fees']
            params['profit_percentage'] = ((float(params['available_capital']) - float(params['initial_capital'])) / float(params['initial_capital']) * 100) if params['initial_capital'] and params['initial_capital'] > 0 else 0



            strategy_report = []
            strategy_report.append(f"策略参数:")
            strategy_report.append(f"- 卖出阈值: {sell_rate*100:.1f}%")
            strategy_report.append(f"- 买入阈值: {buy_rate*100:.1f}%")
            strategy_report.append(f"- 休息时间: {rest_minutes}分钟")
            strategy_report.append(f"- 最小触发间隔: {min_trigger_rest}分钟")
            strategy_report.append(f"\n交易统计:")
            strategy_report.append(f"- 初始资金: ${params['initial_capital']:.4f}")
            strategy_report.append(f"- 最终资金: ${params['available_capital']:.4f}")
            strategy_report.append(f"- 总收益: ${params['profit_loss']:.4f}  ({params['profit_percentage']:.1f}%)")
            strategy_report.append(f"- 总手续费: ${params['total_fees']:.4f}")
            strategy_report.append(f"- 交易次数: {len(params['trades'])}")

            if params['trades']:
                strategy_report.append(f"\n交易记录:")
                for trade in params['trades']:
                    strategy_report.append(f"时间: {trade['timestamp']}")
                    strategy_report.append(f"操作: {trade['action']}")
                    strategy_report.append(f"类型: {trade['position_type']}")
                    strategy_report.append(f"价格: ${trade['price']:.4f}")
                    strategy_report.append(f"数量: {float(trade['amount']):.4f}")
                    strategy_report.append(f"手续费: ${float(trade['fee']):.4f}")
                    strategy_report.append(f"原因: {trade['reason']}")
                    if 'profit_amount' in trade:
                        strategy_report.append(f"收益: ${trade['profit_amount']:.4f}")
                    strategy_report.append("-" * 50)



            return params

        except TimeoutError:
            print_log("策略分析超时")
            return None
        except Exception as e:
            error_msg = traceback.format_exc()
            print_log(f"分析策略时出错: [{str(e)}]\n错误详情:\n{error_msg}")
            if self.row_reson_list:
                print_log(self.row_reson_list.get(max(self.row_reson_list.keys()), None))  # 获取最后一条记录
            return None

    # 添加创建存储过程的方法到StrategyAnalyzer类
    def create_batch_strategy_procedure(self):
        """创建批量插入策略任务的存储过程"""
        with self.get_db_connection() as conn:
            cursor = conn.cursor()  # 使用普通游标而不是DictCursor

            # 删除已存在的存储过程
            cursor.execute("DROP PROCEDURE IF EXISTS create_batch_strategy_tasks")

            # 创建新的存储过程，使用批量插入提高性能
            procedure = """
            CREATE PROCEDURE create_batch_strategy_tasks(
                IN time_ranges_json JSON,
                IN buy_rates_json JSON,
                IN sell_rates_json JSON,
                IN min_trigger_rests_json JSON,
                IN rest_minutes_json JSON,
                IN lookback_minutes_buy_json JSON,
                IN lookback_minutes_sell_json JSON,
                IN currency VARCHAR(20),
                IN client_id VARCHAR(100),
                IN created_at DATETIME,
                IN reverse_buy INT,
                OUT total_tasks_created INT
            )
            BEGIN
                DECLARE i, j, k, l, m, n, o, p, q INT DEFAULT 0;
                DECLARE time_range_start, time_range_end VARCHAR(50);
                DECLARE buy_rate_val, sell_rate_val DECIMAL(10,6);
                DECLARE min_trigger_rest, rest_minute, lookback_buy, lookback_sell INT;
                DECLARE task_count INT DEFAULT 0;
                DECLARE batch_size INT DEFAULT 100; -- 减小批量大小以避免insert_values过长
                DECLARE current_batch INT DEFAULT 0;
                DECLARE insert_values TEXT DEFAULT '';

                -- 循环遍历所有参数组合
                WHILE i < JSON_LENGTH(time_ranges_json) DO
                    SET time_range_start = JSON_UNQUOTE(JSON_EXTRACT(JSON_EXTRACT(time_ranges_json, CONCAT('$[', i, ']')), '$[0]'));
                    SET time_range_end = JSON_UNQUOTE(JSON_EXTRACT(JSON_EXTRACT(time_ranges_json, CONCAT('$[', i, ']')), '$[1]'));

                    SET j = 0;
                    WHILE j < JSON_LENGTH(min_trigger_rests_json) DO
                        SET min_trigger_rest = JSON_EXTRACT(min_trigger_rests_json, CONCAT('$[', j, ']'));

                        SET k = 0;
                        WHILE k < JSON_LENGTH(rest_minutes_json) DO
                            SET rest_minute = JSON_EXTRACT(rest_minutes_json, CONCAT('$[', k, ']'));

                            SET l = 0;
                            WHILE l < JSON_LENGTH(lookback_minutes_buy_json) DO
                                SET lookback_buy = JSON_EXTRACT(lookback_minutes_buy_json, CONCAT('$[', l, ']'));

                                SET m = 0;
                                WHILE m < JSON_LENGTH(lookback_minutes_sell_json) DO
                                    SET lookback_sell = JSON_EXTRACT(lookback_minutes_sell_json, CONCAT('$[', m, ']'));

                                    -- 循环遍历buy_rate和sell_rate
                                    SET n = 0;
                                    WHILE n < JSON_LENGTH(buy_rates_json) DO
                                        SET buy_rate_val = JSON_EXTRACT(buy_rates_json, CONCAT('$[', n, ']'));

                                        SET o = 0;
                                        WHILE o < JSON_LENGTH(sell_rates_json) DO
                                            SET sell_rate_val = JSON_EXTRACT(sell_rates_json, CONCAT('$[', o, ']'));

                                            -- 构建批量插入的值
                                            IF insert_values != '' THEN
                                                SET insert_values = CONCAT(insert_values, ',');
                                            END IF;

                                            SET insert_values = CONCAT(
                                                insert_values,
                                                '(',
                                                buy_rate_val, ',',
                                                sell_rate_val, ',',
                                                min_trigger_rest, ',',
                                                rest_minute, ',',
                                                'STR_TO_DATE(''', time_range_start, ''', ''%Y-%m-%dT%H:%i:%s'')', ',',
                                                'STR_TO_DATE(''', time_range_end, ''', ''%Y-%m-%dT%H:%i:%s'')', ',',
                                                lookback_buy, ',',
                                                lookback_sell, ',',
                                                '''', currency, ''',',
                                                '''pending'',',
                                                '''', client_id, ''',',
                                                '''', created_at, ''',',
                                                reverse_buy,
                                                ')'
                                            );

                                            SET current_batch = current_batch + 1;
                                            SET task_count = task_count + 1;

                                            -- 当达到批量大小时执行插入
                                            IF current_batch >= batch_size THEN
                                                SET @sql = CONCAT(
                                                    'INSERT INTO strategy_analysis_tasks (
                                                        buy_rate, sell_rate,
                                                        min_trigger_rest, rest_minutes,
                                                        start_time, end_time,
                                                        lookback_minutes_buy, lookback_minutes_sell,
                                                        currency, status, client_id, created_at, reverse_buy
                                                    ) VALUES ',
                                                    insert_values
                                                );
                                                PREPARE stmt FROM @sql;
                                                EXECUTE stmt;
                                                DEALLOCATE PREPARE stmt;

                                                SET insert_values = '';
                                                SET current_batch = 0;
                                            END IF;

                                            SET o = o + 1;
                                        END WHILE;

                                        SET n = n + 1;
                                    END WHILE;

                                    SET m = m + 1;
                                END WHILE;

                                SET l = l + 1;
                            END WHILE;

                            SET k = k + 1;
                        END WHILE;

                        SET j = j + 1;
                    END WHILE;

                    SET i = i + 1;
                END WHILE;

                -- 插入剩余的记录
                IF insert_values != '' THEN
                    SET @sql = CONCAT(
                        'INSERT INTO strategy_analysis_tasks (
                            buy_rate, sell_rate,
                            min_trigger_rest, rest_minutes,
                            start_time, end_time,
                            lookback_minutes_buy, lookback_minutes_sell,
                            currency, status, client_id, created_at, reverse_buy
                        ) VALUES ',
                        insert_values
                    );
                    PREPARE stmt FROM @sql;
                    EXECUTE stmt;
                    DEALLOCATE PREPARE stmt;
                END IF;

                -- 设置输出参数
                SET total_tasks_created = task_count;
            END;
            """

            cursor.execute(procedure)
            conn.commit()
            print_log("成功创建批量策略任务存储过程")
        return True





def validate_date(date_str):
    """验证日期是否有效，如果无效则返回该月的最后一天"""
    try:
        if isinstance(date_str, datetime):
            return date_str
        return datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
    except ValueError as e:
        try:
            # 尝试解析日期部分
            date_parts = date_str.split(' ')[0].split('-')
            year = int(date_parts[0])
            month = int(date_parts[1])
            day = int(date_parts[2])

            # 获取该月的最后一天
            if month == 12:
                next_month = datetime(year + 1, 1, 1)
            else:
                next_month = datetime(year, month + 1, 1)
            last_day = (next_month - timedelta(days=1)).day

            # 如果日期超过最后一天，使用最后一天
            if day > last_day:
                day = last_day

            # 使用调整后的日期重新构造datetime对象
            return datetime(year, month, day, 23, 59, 59)
        except Exception as e:
            print_log(f"无效的日期格式: {date_str}")
            raise e

def process_task(args):
    """处理单个任务"""
    if _stop_event:  # 检查是否应该停止
        return None

    task, lock, times_dict = args
    (
        buy_rate, sell_rate,
        min_trigger_rest, rest, start_time, end_time, lookback_minutes_buy, lookback_minutes_sell, no_save, currency, reverse_buy
    ) = task

    process_id = os.getpid()
    print_log(f"进程 {process_id} 开始处理任务")

    # 设置进程优先级为低优先级
    try:
        import psutil
        process = psutil.Process(process_id)
        process.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS)
    except Exception as e:
        print_log(f"设置进程优先级失败: {str(e)}")

    timing_stats = {
        'strategy_calc_time': 0,
        'db_write_time': 0,
        'total_time': 0
    }

    process_start_time = time.time()
    strategy_start_time = time.time()  # 添加策略开始时间
    local_analyzer = None

    try:
        # 创建分析器实例，不传入trading_executor
        local_analyzer = StrategyAnalyzer(DB_CONFIG)

        # 定期检查停止信号
        while not _stop_event:
            try:
                with ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(
                        local_analyzer.analyze_rebound_strategy,
                        buy_rate=buy_rate,
                        sell_rate=sell_rate,
                        min_trigger_rest=min_trigger_rest,
                        rest_minutes=rest,
                        start_time=start_time,
                        end_time=end_time,
                        lookback_minutes_buy=lookback_minutes_buy,
                        lookback_minutes_sell=lookback_minutes_sell,
                        currency=currency
                    )
                    result = future.result(timeout=300)  # 5分钟超时
                    break  # 如果成功完成，跳出循环
            except TimeoutError:
                if _stop_event:  # 检查是否应该停止
                    return None
                continue
            except Exception as e:
                print_log(f"进程 {process_id} 的策略分析出错: {str(e)}  {traceback.format_exc()}")
                return None

        if _stop_event:  # 再次检查是否应该停止
            return None

        # 记录策略计算结束时间
        strategy_end_time = time.time()
        timing_stats['strategy_calc_time'] = strategy_end_time - strategy_start_time

        if not no_save and result:
            with lock:
                # 记录数据库写入开始时间
                db_write_start = time.time()

                try:
                    local_analyzer.save_strategy_results(result)
                except Exception as e:
                    print_log(f"保存结果到数据库时出错: {str(e)}  {traceback.format_exc()}")

                # 记录数据库写入结束时间
                db_write_end = time.time()
                timing_stats['db_write_time'] = db_write_end - db_write_start

                process_end_time = time.time()
                process_duration = process_end_time - process_start_time
                timing_stats['total_time'] = process_duration

                              # 计算成功交易次数和ROI
                successful_trades = sum(1 for trade in result['trades'] if trade.get('profit', 0) > 0)
                total_trades = len(result['trades'])
                roi = ((result['final_capital'] - result['initial_capital']) / result['initial_capital']) * 100
                win_rate = (successful_trades/total_trades*100) if total_trades > 0 else 0

                # 更新进程时间统计
                if process_id not in times_dict:
                    times_dict[process_id] = []
                times_dict[process_id].append(timing_stats)

                print_log(f"\n=== 策略分析结果 (买入阈值: {buy_rate*100:.1f}%, 卖出阈值: {sell_rate*100:.1f}%, 休息: {rest}分钟, 回看买: {lookback_minutes_buy}分钟 卖: {lookback_minutes_sell}分钟)  ===")
                print_log(f"分析时间范围: {start_time} 到 {end_time}")
                print_log(f"进程ID: {process_id}, 耗时: {process_duration:.4f}秒")
                print_log(f"初始资金: ${result['initial_capital']:.4f}")
                print_log(f"最终资金: ${result['final_capital']:.4f}")
                print_log(f"总收益: ${result['profit_loss']:.4f} ({result['profit_percentage']:.1f}%)")
                print_log(f"总交易次数: {total_trades}")
                print_log(f"成功交易次数: {successful_trades}")
                print_log(f"胜率: {win_rate:.1f}%")
                print_log(f"总手续费: ${result['total_fees']:.4f}")
                print_log(f"ROI: {roi:.1f}%")
                print_log("=" * 80)

                print_log(f"进程ID: {process_id}")
                print_log(f"策略计算耗时: {timing_stats['strategy_calc_time']:.4f}秒")
                print_log(f"数据库写入耗时: {timing_stats['db_write_time']:.4f}秒")
                print_log(f"总耗时: {timing_stats['total_time']:.4f}秒")


        return result

    except Exception as e:
        error_msg = traceback.format_exc()
        with lock:
            process_end_time = time.time()
            timing_stats['total_time'] = process_end_time - process_start_time
            if process_id not in times_dict:
                times_dict[process_id] = []
            times_dict[process_id].append(timing_stats)
            print_log(f"分析阈值组合时出错: [{str(e)}]\n错误详情:\n{error_msg}")
        return None
    finally:
        try:
            if local_analyzer and hasattr(local_analyzer, 'db'):
                local_analyzer.db.close()
        except Exception as e:
            print_log(f"关闭数据库连接时出错: {str(e)}\n错误详情:\n{error_msg}")

def worker_with_timeout(args):
    """带超时处理的工作函数"""
    try:
        return process_task(args)
    except Exception as e:
        error_msg = traceback.format_exc()
        print_log(f"任务执行出错: {str(e)}\n错误详情:\n{error_msg}")
        return None



if __name__ == "__main__":

    # analyzer = StrategyAnalyzer(DB_CONFIG)
    # analyzer.send_email("测试主题", "测试内容")
    # exit()

    # 设置控制台编码为 UTF-8 并禁用输出缓冲
    if sys.platform == 'win32':
        import codecs
        import msvcrt
        import os
        # 设置 stdout 为无缓冲模式
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)
        # 设置环境变量 PYTHONUNBUFFERED=1
        os.environ['PYTHONUNBUFFERED'] = '1'



    def signal_handler(signum, frame):
        """全局信号处理函数"""
        print_log("\n收到停止信号，正在安全退出...")
        global _stop_event
        _stop_event = True
        # 立即退出程序
        sys.exit()




    # 注册全局信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    parser = argparse.ArgumentParser(description="策略分析器")
    parser.add_argument("--start", type=str, help="开始时间，格式为YYYY-MM-DD HH:MM:SS")
    parser.add_argument("--end", type=str, help="结束时间，格式为YYYY-MM-DD HH:MM:SS")
    parser.add_argument("--currency", type=str, help="交易币种", default="DOGE")
    parser.add_argument("--buy-rate", type=float, help="买入阈值（用于下跌做空或上涨买入）")
    parser.add_argument("--sell-rate", type=float, help="卖出阈值（用于上涨平空或下跌平多）")
    parser.add_argument("--rest", type=int, help="交易间隔休息时间（分钟）", default=None)
    parser.add_argument("--min-trigger-rest", type=int, help="最小止损间隔时间（分钟）", default=None)
    parser.add_argument("--clear", action="store_true", help="清空策略数据表")
    parser.add_argument("--lookback-minutes-buy", type=int, help="买回看分钟数", default=None)
    parser.add_argument("--lookback-minutes-sell", type=int, help="卖回看分钟数", default=None)
    parser.add_argument("--split-by-day", action="store_true", help="按天拆分任务")
    parser.add_argument("--no-save", action="store_true", help="不保存到数据库")
    parser.add_argument("--reverse-buy", type=int, help="是否反向买入", default=0)



    # 添加客户端ID参数
    parser.add_argument("--client-id", type=str, help="客户端ID")
    parser.add_argument("--batch-size", type=int, help="每次获取的任务数量", default=10)
    parser.add_argument("--sleep-time", type=int, help="无任务时休息的秒数", default=15)
        # 添加重新运行策略的参数
    parser.add_argument("--rerun", type=str, help="重新运行指定的策略ID，多个ID用逗号分隔")

    args = parser.parse_args()



    # 解析参数后处理重新运行策略的逻辑
    if args.rerun:
        print_log(f"开始重新运行策略: {args.rerun}")
        analyzer = StrategyAnalyzer(DB_CONFIG)

        # 将逗号分隔的ID字符串转换为列表
        strategy_ids = [int(id.strip()) for id in args.rerun.split(',') if id.strip()]

        if strategy_ids:
            try:
                # 调用重新运行策略的方法
                new_strategy_ids = analyzer.rerun_strategies(strategy_ids)
                print_log(f"重新运行完成，新策略ID: {new_strategy_ids}")
                sys.exit()
            except Exception as e:
                print_log(f"重新运行策略时出错: {str(e)}")
                traceback.print_exc()
                sys.exit()
        else:
            print_log("未提供有效的策略ID")
            sys.exit()


    def process_tasks_with_client(client_id, batch_size=10, sleep_time=15):
        """
        使用指定的客户端ID处理任务

        Args:
            client_id: 客户端ID
            batch_size: 每次获取的任务数量
            sleep_time: 无任务时休息的秒数
        """
        print_log(f"以客户端ID '{client_id}' 启动任务处理模式")

        analyzer = StrategyAnalyzer(DB_CONFIG)

        # 任务处理循环
        while not _stop_event:
            try:
                # 连接数据库
                with analyzer.get_db_connection() as conn:
                    cursor = conn.cursor()

                    # 获取待处理的任务
                    select_sql = """
                    SELECT * FROM strategy_analysis_tasks
                    WHERE status = 'pending'
                    LIMIT %s
                    """
                    cursor.execute(select_sql, (batch_size,))
                    tasks = cursor.fetchall()

                    if not tasks:

                        if client_id == "main":
                            break

                        if sleep_time < 0:
                            break
                        print_log(f"没有待处理的任务，休息 {sleep_time} 秒...")
                        # 分段休眠，每秒输出一个点，以保持输出活跃
                        for i in range(sleep_time):
                            print(".", end="", flush=True)
                            time.sleep(1)
                        print("\n", flush=True)  # 换行
                        continue

                    print_log(f"获取到 {len(tasks)} 个待处理任务")

                    # 更新任务状态为处理中
                    task_ids = [task['id'] for task in tasks]
                    placeholders = ', '.join(['%s'] * len(task_ids))
                    update_sql = f"""
                    UPDATE strategy_analysis_tasks
                    SET status = 'processing', client_id = %s, started_at = %s
                    WHERE id IN ({placeholders})
                    """
                    cursor.execute(update_sql, [client_id, datetime.now()] + task_ids)
                    conn.commit()

                    # 逐个处理任务
                    # 创建结果列表，用于批量更新
                    completed_tasks = []
                    failed_tasks = []
                    error_tasks = []

                    for task in tasks:
                        if _stop_event:
                            break

                        task_id = task['id']
                        print_log(f"开始处理任务 {task_id}")

                        if len(tasks) < 10:
                            print_log("任务数小于10，强制保存交易")
                            analyzer.save_trades=1
                            analyzer.save_row_reson=1

                        try:
                            # 提取任务参数
                            start_time = task['start_time']
                            end_time = task['end_time']
                            buy_rate = task['buy_rate']
                            sell_rate = task['sell_rate']
                            min_trigger_rest = task['min_trigger_rest']
                            rest_minutes = task['rest_minutes']
                            lookback_minutes_buy = task['lookback_minutes_buy']
                            lookback_minutes_sell = task['lookback_minutes_sell']
                            currency = task['currency']
                            reverse_buy = task['reverse_buy']

                            # 执行策略分析
                            print_log(f"开始分析策略: 买入阈值={buy_rate}, 卖出阈值={sell_rate}, 休息时间={rest_minutes}分钟, 最小触发时间={min_trigger_rest}分钟, 回看买={lookback_minutes_buy}分钟, 回看卖={lookback_minutes_sell}分钟, 币种={currency}, 任务ID={task_id}, 客户端ID={client_id}, 任务创建时间={task['created_at']}, 反向买入={reverse_buy}")
                            print_log(f"时间范围: {start_time} 至 {end_time}, 币种: {currency}")

                            # 每10秒输出一个进度指示点
                            progress_thread = threading.Thread(target=lambda: [
                                print(".", end="", flush=True) or time.sleep(10)
                                for _ in range(100) if not _stop_event
                            ])
                            progress_thread.daemon = True
                            progress_thread.start()

                            result = analyzer.analyze_rebound_strategy(
                                buy_rate=buy_rate,
                                sell_rate=sell_rate,
                                min_trigger_rest=min_trigger_rest,
                                rest_minutes=rest_minutes,
                                start_time=start_time,
                                end_time=end_time,
                                lookback_minutes_buy=lookback_minutes_buy,
                                lookback_minutes_sell=lookback_minutes_sell,
                                currency=currency,
                                reverse_buy=reverse_buy
                            )

                            print("\n", flush=True)  # 进度指示结束后换行

                            # 收集任务结果
                            if result:
                                start_execution_time = datetime.now()
                                strategy_id=0
                                if not args.no_save:
                                    strategy_id=analyzer.save_strategy_results(result)

                                completed_tasks.append({
                                    'id': task_id,
                                    'execution_time': (datetime.now() - start_execution_time).total_seconds(),  # 计算任务执行时间（秒）
                                    'result_id': strategy_id,
                                    'started_at': start_execution_time.strftime('%Y-%m-%d %H:%M:%S'),
                                    'completed_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                })
                            else:
                                # 如果分析失败
                                failed_tasks.append({'id': task_id})

                            print_log(f"任务 {task_id} 处理完成")

                        except Exception as e:
                            error_msg = traceback.format_exc()
                            print_log(f"处理任务 {task_id} 时发生错误: {str(e)}\n{error_msg}")

                            # 收集错误任务
                            error_tasks.append({
                                'id': task_id,
                                'error': str(e)
                            })

                    # 批量更新已完成的任务
                    if completed_tasks:
                        with analyzer.get_db_connection() as update_conn:
                            update_cursor = update_conn.cursor()
                            for task in completed_tasks:
                                update_result_sql = """
                                UPDATE strategy_analysis_tasks
                                SET status = 'completed',
                                    completed_at = %s,
                                    started_at = %s,
                                    execution_time = %s,
                                    client_id = %s,
                                    result_id = %s
                                WHERE id = %s
                                """
                                update_cursor.execute(
                                    update_result_sql,
                                    (datetime.now().strftime('%Y-%m-%d %H:%M:%S'), task['started_at'], task['execution_time'], client_id, task['result_id'], task['id'])
                                )
                            update_conn.commit()
                            print_log(f"批量更新了 {len(completed_tasks)} 个已完成任务")

                    # 批量更新失败的任务
                    if failed_tasks:
                        with analyzer.get_db_connection() as failed_conn:
                            failed_cursor = failed_conn.cursor()
                            for task in failed_tasks:
                                update_result_sql = """
                                UPDATE strategy_analysis_tasks
                                SET status = 'failed',
                                    completed_at = %s
                                WHERE id = %s
                                """
                                failed_cursor.execute(update_result_sql, (datetime.now().strftime('%Y-%m-%d %H:%M:%S'), task['id']))
                            failed_conn.commit()
                            print_log(f"批量更新了 {len(failed_tasks)} 个失败任务")

                    # 批量更新出错的任务
                    if error_tasks:
                        with analyzer.get_db_connection() as error_conn:
                            error_cursor = error_conn.cursor()
                            for task in error_tasks:
                                error_sql = """
                                UPDATE strategy_analysis_tasks
                                SET status = 'failed',
                                    error_message = %s,
                                    completed_at = %s
                                WHERE id = %s
                                """
                                error_cursor.execute(error_sql, (task['error'], datetime.now().strftime('%Y-%m-%d %H:%M:%S'), task['id']))
                            error_conn.commit()
                            print_log(f"批量更新了 {len(error_tasks)} 个错误任务")

            except Exception as e:
                error_msg = traceback.format_exc()
                print_log(f"任务处理循环发生错误: {str(e)}\n{error_msg}")
                time.sleep(5)  # 出错后短暂休息

        print_log("任务处理模式已退出")

    # 如果提供了客户端ID，进入处理任务模式
    if args.client_id:
        process_tasks_with_client(args.client_id, args.batch_size, args.sleep_time)
        sys.exit()

    print_log("开始策略分析")

    # 在主进程中执行清空操作
    if args.clear:
        analyzer = StrategyAnalyzer(DB_CONFIG)
        try:
            print_log("正在清空数据库...")
            analyzer.clear_strategy_data()
            print_log("数据库清空完成")
        finally:
            if hasattr(analyzer, 'db'):
                analyzer.db.close()

    # 处理时间参数
    start_time = None
    end_time = None
    time_ranges = []

    if args.start and args.end:
        try:
            start_time = validate_date(args.start)
            end_time = validate_date(args.end)
            print_log(f"分析时间范围: {start_time} 到 {end_time}")

            if args.split_by_day:
                # 按天分割时间范围
                time_ranges = []
                current_date = start_time.date()
                end_date = end_time.date()

                while current_date <= end_date:
                    day_start = datetime.combine(current_date, datetime.min.time())
                    day_end = datetime.combine(current_date, datetime.max.time())

                    # 对于第一天和最后一天，使用实际的开始和结束时间
                    if current_date == start_time.date():
                        day_start = start_time
                    if current_date == end_time.date():
                        day_end = end_time

                    time_ranges.append((day_start, day_end))
                    current_date += timedelta(days=1)
            else:
                # 不分割，使用完整时间范围
                time_ranges = [(start_time, end_time)]
        except ValueError as e:
            error_msg = traceback.format_exc()
            print_log(f"日期格式错误: {str(e)}\n错误详情:\n{error_msg}")
            sys.exit()
    else:
        # 使用昨天的数据
        yesterday = datetime.now() - timedelta(days=1)
        time_ranges = [(
            yesterday.replace(hour=0, minute=0, second=0),
            yesterday.replace(hour=23, minute=59, second=59)
        )]

    # 处理阈值参数
    if args.buy_rate is not None and args.sell_rate is not None:
        buy_rates = [args.buy_rate]
        sell_rates = [args.sell_rate]
    else:
        buy_rates = [0.002,0.003,0.004,0.005,0.006,0.007,0.008,0.009,0.01,0.015,0.025]
        sell_rates = [0.002,0.003,0.004,0.005,0.006,0.007,0.008,0.009,0.01,0.015,0.025]

    if args.lookback_minutes_buy is not None:
        lookback_minutes_buy_list = [args.lookback_minutes_buy]
    else:
        lookback_minutes_buy_list = [2, 3, 4,5,6]

    if args.lookback_minutes_sell is not None:
        lookback_minutes_sell_list = [args.lookback_minutes_sell]
    else:
        lookback_minutes_sell_list = [2, 3, 4,5,6]

    rest_minutes = [0,1,2,3,4,5,6,7,8,9,10,15]

    min_trigger_rests = [2,5,8,10,15,20]

    if args.rest is not None:
        rest_minutes = [args.rest]

    if args.min_trigger_rest is not None:
        min_trigger_rests = [args.min_trigger_rest]

    print_log(f"开始分析策略")
    if args.split_by_day:
        print_log(f"按天分析时间范围: {len(time_ranges)}天")
    print_log("=" * 50)

    total_start_time = time.time()
    results = []

    # 创建分析器实例
    analyzer = StrategyAnalyzer(DB_CONFIG)

    try:
        # 创建MySQL存储过程
        try:
            # 首先创建存储过程
            analyzer.create_batch_strategy_procedure()

            # 准备参数
            import socket
            client_id = socket.gethostname()
            created_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 调用存储过程执行批量插入
            with analyzer.get_db_connection() as conn:
                cursor = conn.cursor()

                # 调用存储过程
                # MySQL中调用此存储过程的语法:
                # CALL create_batch_strategy_tasks(
                #   '[[\"2023-01-01T00:00:00\", \"2023-01-31T23:59:59\"]]',
                #   '[0.002, 0.005, 0.007, 0.01, 0.015, 0.025]',
                #   '[0.002, 0.005, 0.007, 0.01, 0.015, 0.025]',
                #   '[0.002, 0.005, 0.007, 0.01, 0.015, 0.025]',
                #   '[0.002, 0.005, 0.007, 0.01, 0.015, 0.025]',
                #   '[10, 15, 30, 40]',
                #   '[0]',
                #   '[2, 3, 4, 5, 6]',
                #   '[2, 3, 4, 5, 6]',
                #   'BTC',
                #   'hostname',
                #   '2023-06-01 12:00:00',
                #   @total_tasks
                # );
                # SELECT @total_tasks;
                cursor.callproc('create_batch_strategy_tasks', [
                    json.dumps([(t[0].isoformat(), t[1].isoformat()) for t in time_ranges]),
                    json.dumps(buy_rates),
                    json.dumps(sell_rates),
                    json.dumps(min_trigger_rests),
                    json.dumps(rest_minutes),
                    json.dumps(lookback_minutes_buy_list),
                    json.dumps(lookback_minutes_sell_list),
                    args.currency,
                    client_id,
                    created_at,
                    args.reverse_buy,
                    0  # 添加输出参数占位符
                ])

                # 获取结果 - 使用正确的方式获取输出参数
                cursor.execute("SELECT @_create_batch_strategy_tasks_10")  # 获取输出参数
                result = cursor.fetchone()
                task_count = int(result['@_create_batch_strategy_tasks_10']) if result and result['@_create_batch_strategy_tasks_10'] else 0

                conn.commit()

            print_log(f"成功通过存储过程创建 {task_count} 个策略分析任务")

            # 计算总任务数
            total_tasks = (len(time_ranges) * len(buy_rates) * len(sell_rates) *
                          len(min_trigger_rests) * len(rest_minutes) * len(lookback_minutes_buy_list) * len(lookback_minutes_sell_list))
            print_log(f"任务创建完成: {task_count}/{total_tasks} ({(int(task_count)/int(total_tasks)*100):.1f}%)")


            if total_tasks < 100:
                process_tasks_with_client("main", 50, 2)
                sys.exit()


        except Exception as e:
            print_log(f"调用存储过程创建策略分析任务出错: {str(e)}\n{traceback.format_exc()}")


    except KeyboardInterrupt:
        print_log("程序被用户中断")
        _stop_event = True
    finally:
        print_log("程序已安全退出")

    total_end_time = time.time()
    total_duration = total_end_time - total_start_time

    print_log("" + "=" * 50)
    print_log("策略分析完成")
    print_log(f"总耗时: {total_duration:.4f}秒")
    print_log(f"成功完成任务数: {len(results)}")
    print_log("=" * 50)

