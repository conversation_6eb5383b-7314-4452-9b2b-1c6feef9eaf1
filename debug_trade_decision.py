#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试交易决策过程
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def debug_trade_decision():
    """调试交易决策过程"""
    
    print("=== 调试交易决策过程 ===")
    print()
    
    # 创建策略分析器实例
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 策略参数
    params = {
        'sell_rate': 0.01,
        'buy_rate': 1.0,
        'min_trigger_rest': 5,
        'rest_minutes': 0,
        'lookback_minutes_buy': 5,
        'lookback_minutes_sell': 2,
        'currency': 'DOGE',
        'is_live_trading': False,
        'reverse_buy': 0
    }
    
    # 测试时间：只测试00:40:00到00:45:00
    test_date = datetime(2025, 6, 6)
    start_time = test_date.replace(hour=0, minute=40)
    end_time = test_date.replace(hour=0, minute=45)
    
    print(f"测试时间: {start_time} 到 {end_time}")
    print()
    
    # 在StrategyAnalyzer类中添加调试信息
    original_trade_condition_new = analyzer.trade_condition_new
    
    def debug_trade_condition_new(row, params):
        print(f"🔍 调用 trade_condition_new - 时间: {row['timestamp']}")
        print(f"   当前持仓: {params.get('current_position', 0)}")
        print(f"   历史K线数量: {len(params.get('minute_candles', []))}")
        
        result = original_trade_condition_new(row, params)
        
        print(f"   决策结果: {result['action']} - {result['reason']}")
        print()
        
        return result
    
    # 替换方法
    analyzer.trade_condition_new = debug_trade_condition_new
    
    try:
        print("=== 开始分析 ===")
        result = analyzer.analyze_rebound_strategy(
            start_time=start_time,
            end_time=end_time,
            use_new_condition=True,  # 使用新方法
            **params
        )
        
        if result:
            print(f"\n=== 分析结果 ===")
            print(f"交易次数: {len(result['trades'])}")
            print(f"收益率: {result['profit_percentage']:.2f}%")
            
            # 显示所有交易记录
            if result['trades']:
                print(f"\n所有交易记录:")
                for i, trade in enumerate(result['trades']):
                    print(f"  {i+1}. {trade['timestamp']} - {trade['action']} - 价格: {trade['price']:.4f}")
                    print(f"     原因: {trade.get('trigger_reason', trade.get('reason', 'N/A'))}")
        else:
            print("分析失败")
            
    except Exception as e:
        print(f"运行时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_trade_decision()
