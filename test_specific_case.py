#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试特定的边界检查问题
验证实际触发价0.24218是否还会超出K线范围[0.24012, 0.24067]
"""

import subprocess
import sys
import os

def test_boundary_issue():
    """测试边界检查问题"""
    
    print("=== 测试边界检查修复 ===")
    print()
    
    # 运行一个更长时间的策略分析来获取更多交易数据
    cmd = [
        "python", "strategy_analyzer.py",
        "--start", "2025-03-01 00:00:00",
        "--end", "2025-03-01 12:00:00",  # 12小时的数据
        "--sell-rate", "1",
        "--buy-rate", "1", 
        "--rest", "0",
        "--min-trigger-rest", "0",
        "--lookback-minutes-buy", "2",
        "--lookback-minutes-sell", "5",
        "--price-adjust-rate", "0.001"
    ]
    
    print("🚀 运行策略分析（12小时数据）...")
    result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
    
    if result.returncode != 0:
        print(f"❌ 策略分析失败: {result.stderr}")
        return False
    
    print("✅ 策略分析完成")
    print()
    
    # 查找最新的策略日志文件
    import glob
    import pandas as pd
    
    pattern = 'log/strategy/strategy_line_*.log'
    files = glob.glob(pattern)
    if not files:
        print("❌ 没有找到策略日志文件")
        return False
    
    latest = max(files, key=os.path.getmtime)
    print(f"📁 分析文件: {latest}")
    
    try:
        # 读取日志文件
        df = pd.read_csv(latest, sep='\t', encoding='utf-8', low_memory=False)
        
        # 转换数据类型
        df['实际触发价'] = pd.to_numeric(df['实际触发价'], errors='coerce')
        df['最高价'] = pd.to_numeric(df['最高价'], errors='coerce')
        df['最低价'] = pd.to_numeric(df['最低价'], errors='coerce')
        df['保护价'] = pd.to_numeric(df['保护价'], errors='coerce')
        
        # 过滤有交易的记录
        trades = df[df['实际触发价'] > 0].copy()
        
        if len(trades) == 0:
            print("⚠️  没有找到交易记录")
            return True
        
        print(f"📊 找到 {len(trades)} 笔交易记录")
        print()
        
        # 检查边界违规
        violations = []
        
        for idx, trade in trades.iterrows():
            timestamp = trade['时间']
            low_price = trade['最低价']
            high_price = trade['最高价']
            actual_trigger_price = trade['实际触发价']
            protection_price = trade['保护价']
            
            # 检查实际触发价是否在范围内
            actual_in_range = low_price <= actual_trigger_price <= high_price
            protection_in_range = low_price <= protection_price <= high_price
            
            if not actual_in_range:
                violations.append({
                    'timestamp': timestamp,
                    'type': '实际触发价',
                    'price': actual_trigger_price,
                    'low': low_price,
                    'high': high_price
                })
            
            if not protection_in_range:
                violations.append({
                    'timestamp': timestamp,
                    'type': '保护价',
                    'price': protection_price,
                    'low': low_price,
                    'high': high_price
                })
        
        # 报告结果
        if len(violations) == 0:
            print("🎉 所有交易价格都在K线范围内！边界检查修复成功！")
            print()
            
            # 显示一些示例交易
            print("📋 示例交易记录（最后5笔）:")
            for idx, trade in trades.tail(5).iterrows():
                timestamp = trade['时间']
                low_price = trade['最低价']
                high_price = trade['最高价']
                actual_trigger_price = trade['实际触发价']
                protection_price = trade['保护价']
                
                print(f"  {timestamp}")
                print(f"    K线范围: [{low_price:.5f}, {high_price:.5f}]")
                print(f"    实际触发价: {actual_trigger_price:.5f} ✅")
                print(f"    保护价: {protection_price:.5f} ✅")
                print()
            
            return True
        else:
            print(f"❌ 发现 {len(violations)} 个边界违规问题:")
            print()
            
            for violation in violations[:10]:  # 只显示前10个
                print(f"  🚨 {violation['timestamp']}")
                print(f"     {violation['type']}: {violation['price']:.5f}")
                print(f"     K线范围: [{violation['low']:.5f}, {violation['high']:.5f}]")
                print()
            
            if len(violations) > 10:
                print(f"  ... 还有 {len(violations) - 10} 个违规")
            
            return False
            
    except Exception as e:
        print(f"❌ 分析日志文件时出错: {e}")
        return False

if __name__ == "__main__":
    success = test_boundary_issue()
    if success:
        print("✅ 边界检查测试通过")
    else:
        print("❌ 边界检查测试失败")
    
    sys.exit(0 if success else 1)
