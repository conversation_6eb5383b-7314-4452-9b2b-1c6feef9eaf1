#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试方法调用
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def debug_method_call():
    """调试方法调用"""
    
    print("=== 调试方法调用 ===")
    print()
    
    # 创建策略分析器实例
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 获取最新的数据时间范围
    try:
        with analyzer.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT MAX(timestamp) as max_time 
                FROM crypto_prices 
                WHERE currency = 'DOGE'
            """)
            result = cursor.fetchone()
            
            if not result or not result['max_time']:
                print("没有找到DOGE的价格数据")
                return
                
            max_time = result['max_time']
            # 使用最新数据的前一天，但只测试一小时
            test_date = max_time.date() - timedelta(days=1)
            start_time = datetime.combine(test_date, datetime.min.time())
            end_time = start_time + timedelta(hours=1)  # 只测试1小时
            
    except Exception as e:
        print(f"获取数据时间范围时出错: {str(e)}")
        return
    
    # 策略参数
    params = {
        'sell_rate': 0.01,
        'buy_rate': 1.0,
        'min_trigger_rest': 5,
        'rest_minutes': 0,
        'lookback_minutes_buy': 5,
        'lookback_minutes_sell': 2,
        'currency': 'DOGE',
        'is_live_trading': False,
        'reverse_buy': 0
    }
    
    print(f"测试时间: {start_time} 到 {end_time}")
    print(f"测试币种: {params['currency']}")
    print()
    
    # 在StrategyAnalyzer类中添加调试信息
    original_trade_condition_new = analyzer.trade_condition_new
    original_trade_condition_analyze = analyzer.trade_condition_analyze
    
    def debug_trade_condition_new(row, params):
        print(f"🟢 调用了 trade_condition_new 方法 - 时间: {row['timestamp']}")
        return original_trade_condition_new(row, params)
    
    def debug_trade_condition_analyze(row, params):
        print(f"🔴 调用了 trade_condition_analyze 方法 - 时间: {row['timestamp']}")
        return original_trade_condition_analyze(row, params)
    
    # 替换方法
    analyzer.trade_condition_new = debug_trade_condition_new
    analyzer.trade_condition_analyze = debug_trade_condition_analyze
    
    try:
        print("=== 测试新方法 (use_new_condition=True) ===")
        result = analyzer.analyze_rebound_strategy(
            start_time=start_time,
            end_time=end_time,
            use_new_condition=True,  # 使用新方法
            **params
        )
        
        if result:
            print(f"\n新方法结果:")
            print(f"  交易次数: {len(result['trades'])}")
            print(f"  收益率: {result['profit_percentage']:.2f}%")
            
            # 显示前3条交易记录
            if result['trades']:
                print(f"\n前3条交易记录:")
                for i, trade in enumerate(result['trades'][:3]):
                    print(f"  {i+1}. {trade['timestamp']} - {trade['action']} - 原因: {trade.get('trigger_reason', 'N/A')}")
        else:
            print("新方法分析失败")
            
    except Exception as e:
        print(f"运行新方法时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_method_call()
