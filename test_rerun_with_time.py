#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试重新运行策略并指定新时间范围的功能
"""

import sys
import os
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def test_rerun_with_new_time():
    """测试使用新时间范围重新运行策略"""
    
    print("=== 测试重新运行策略并指定新时间范围 ===")
    
    # 创建策略分析器
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 示例策略ID（请根据实际情况修改）
    strategy_ids = [1, 2]  # 替换为你想要重新运行的策略ID
    
    # 定义新的时间范围
    new_start_time = "2024-01-01 00:00:00"
    new_end_time = "2024-01-31 23:59:59"
    
    print(f"原策略ID: {strategy_ids}")
    print(f"新开始时间: {new_start_time}")
    print(f"新结束时间: {new_end_time}")
    
    try:
        # 重新运行策略，使用新的时间范围
        new_strategy_ids = analyzer.rerun_strategies(
            strategy_ids=strategy_ids,
            new_start_time=new_start_time,
            new_end_time=new_end_time
        )
        
        print(f"✅ 重新运行成功！")
        print(f"新策略ID: {new_strategy_ids}")
        
        return new_strategy_ids
        
    except Exception as e:
        print(f"❌ 重新运行失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_rerun_only_start_time():
    """测试只修改开始时间"""
    
    print("\n=== 测试只修改开始时间 ===")
    
    analyzer = StrategyAnalyzer(DB_CONFIG)
    strategy_ids = [1]  # 替换为实际的策略ID
    new_start_time = "2024-02-01 00:00:00"
    
    print(f"策略ID: {strategy_ids}")
    print(f"新开始时间: {new_start_time}")
    print("结束时间: 保持原有")
    
    try:
        new_strategy_ids = analyzer.rerun_strategies(
            strategy_ids=strategy_ids,
            new_start_time=new_start_time,
            new_end_time=None  # 保持原有结束时间
        )
        
        print(f"✅ 重新运行成功！新策略ID: {new_strategy_ids}")
        return new_strategy_ids
        
    except Exception as e:
        print(f"❌ 重新运行失败: {str(e)}")
        return None

def test_rerun_only_end_time():
    """测试只修改结束时间"""
    
    print("\n=== 测试只修改结束时间 ===")
    
    analyzer = StrategyAnalyzer(DB_CONFIG)
    strategy_ids = [1]  # 替换为实际的策略ID
    new_end_time = "2024-03-31 23:59:59"
    
    print(f"策略ID: {strategy_ids}")
    print("开始时间: 保持原有")
    print(f"新结束时间: {new_end_time}")
    
    try:
        new_strategy_ids = analyzer.rerun_strategies(
            strategy_ids=strategy_ids,
            new_start_time=None,  # 保持原有开始时间
            new_end_time=new_end_time
        )
        
        print(f"✅ 重新运行成功！新策略ID: {new_strategy_ids}")
        return new_strategy_ids
        
    except Exception as e:
        print(f"❌ 重新运行失败: {str(e)}")
        return None

def show_usage_examples():
    """显示命令行使用示例"""
    
    print("\n=== 命令行使用示例 ===")
    print()
    print("1. 重新运行策略并指定新的时间范围:")
    print("   python strategy_analyzer.py --rerun 1,2,3 --rerun-start '2024-01-01 00:00:00' --rerun-end '2024-01-31 23:59:59'")
    print()
    print("2. 只修改开始时间:")
    print("   python strategy_analyzer.py --rerun 1 --rerun-start '2024-02-01 00:00:00'")
    print()
    print("3. 只修改结束时间:")
    print("   python strategy_analyzer.py --rerun 1 --rerun-end '2024-03-31 23:59:59'")
    print()
    print("4. 使用原有时间范围重新运行:")
    print("   python strategy_analyzer.py --rerun 1,2,3")
    print()
    print("参数说明:")
    print("  --rerun: 要重新运行的策略ID，多个ID用逗号分隔")
    print("  --rerun-start: 新的开始时间（可选）")
    print("  --rerun-end: 新的结束时间（可选）")
    print()

if __name__ == "__main__":
    print("策略重新运行功能测试")
    print("=" * 50)
    
    # 显示使用示例
    show_usage_examples()
    
    # 注意：以下测试需要实际的策略ID，请根据你的数据库情况修改
    print("注意：以下测试需要实际的策略ID，请根据你的数据库情况修改strategy_ids变量")
    print()
    
    # 如果你想运行实际测试，请取消注释以下代码并修改策略ID
    """
    # 测试完整的时间范围修改
    test_rerun_with_new_time()
    
    # 测试只修改开始时间
    test_rerun_only_start_time()
    
    # 测试只修改结束时间
    test_rerun_only_end_time()
    """
    
    print("测试脚本准备完成！")
    print("请修改策略ID后运行相应的测试函数。")
