#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
命令行测试新策略方法
"""

import sys
import os
import argparse
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def main():
    parser = argparse.ArgumentParser(description='测试新的交易策略')
    
    # 时间参数
    parser.add_argument('--start', type=str, required=True, 
                       help='开始时间，格式: "2024-11-24 00:00:00"')
    parser.add_argument('--end', type=str, required=True,
                       help='结束时间，格式: "2024-11-24 23:59:59"')
    
    # 策略参数
    parser.add_argument('--buy_rate', type=float, default=1.0,
                       help='买入倍率 (默认: 1.0)')
    parser.add_argument('--sell_rate', type=float, default=0.02,
                       help='卖出倍率 (默认: 0.02)')
    parser.add_argument('--rest', type=int, default=0,
                       help='休息时间(分钟) (默认: 0)')
    parser.add_argument('--lookback_buy', type=int, default=5,
                       help='连续下跌分钟数 (默认: 5)')
    parser.add_argument('--lookback_sell', type=int, default=2,
                       help='连续上涨分钟数 (默认: 2)')
    
    # 其他参数
    parser.add_argument('--currency', type=str, default='DOGE',
                       help='交易币种 (默认: DOGE)')
    parser.add_argument('--clear', action='store_true',
                       help='清除之前的分析结果')
    parser.add_argument('--threads', type=int, default=1,
                       help='线程数 (默认: 1)')
    parser.add_argument('--split-by-day', action='store_true',
                       help='按天分割分析')
    
    args = parser.parse_args()
    
    print("=== 新策略命令行测试 ===")
    print(f"测试时间: {args.start} 到 {args.end}")
    print(f"币种: {args.currency}")
    print(f"策略参数:")
    print(f"  - lookback_buy: {args.lookback_buy} (连续下跌分钟)")
    print(f"  - lookback_sell: {args.lookback_sell} (连续上涨分钟)")
    print(f"  - buy_rate: {args.buy_rate}")
    print(f"  - sell_rate: {args.sell_rate}")
    print(f"  - rest: {args.rest} 分钟")
    print()
    
    # 创建策略分析器
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    try:
        # 解析时间
        start_time = datetime.fromisoformat(args.start)
        end_time = datetime.fromisoformat(args.end)
        
        # 运行新策略分析
        result = analyzer.analyze_new_strategy(
            start_time=start_time,
            end_time=end_time,
            currency=args.currency,
            buy_rate=args.buy_rate,
            sell_rate=args.sell_rate,
            rest_minutes=args.rest,
            lookback_minutes_buy=args.lookback_buy,
            lookback_minutes_sell=args.lookback_sell
        )
        
        print("=== 分析结果 ===")
        print(f"初始资金: ${result['initial_capital']:.4f}")
        print(f"最终资金: ${result['final_capital']:.4f}")
        print(f"总收益: ${result['total_profit']:.4f}")
        print(f"收益率: {result['return_rate']:.2f}%")
        print(f"交易次数: {result['total_trades']}")
        print(f"总手续费: ${result['total_fees']:.4f}")
        print()
        
        if result['trades']:
            print("=== 交易记录 ===")
            for i, trade in enumerate(result['trades'][:10], 1):  # 只显示前10条
                action = "BUY " if trade['action'] == '开多' else "SELL" if trade['action'] == '平多' else trade['action']
                print(f"{i:2d}. {trade['timestamp']} | {action:4s} | $ {trade['price']:8.4f} | 收益: ${trade['profit']:8.4f} | {trade['reason']}")
            
            if len(result['trades']) > 10:
                print(f"... 还有 {len(result['trades']) - 10} 条交易记录")
        
        print()
        print("=== 策略说明 ===")
        print("新方法的优势:")
        print("1. 更精确的趋势识别：基于连续分钟K线的涨跌模式")
        print("2. 减少假信号：需要满足连续涨跌条件才触发")
        print("3. 更好的风控：使用关键价位进行止损")
        print("4. 适应性强：可以通过调整lookback参数适应不同市场")
        
    except Exception as e:
        print(f"分析过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
