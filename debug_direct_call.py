#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
直接调用trade_condition_new方法进行调试
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def debug_direct_call():
    """直接调用trade_condition_new方法进行调试"""
    
    print("=== 直接调用trade_condition_new方法进行调试 ===")
    print()
    
    # 创建策略分析器实例
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 获取00:00:00到00:45:00的数据
    try:
        with analyzer.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT timestamp, open_price as open, close_price as close, 
                       high_price as high, low_price as low
                FROM crypto_prices 
                WHERE currency = 'DOGE' 
                AND timestamp >= '2025-06-06 00:00:00' 
                AND timestamp <= '2025-06-06 00:45:00'
                ORDER BY timestamp
            """)
            results = cursor.fetchall()
            
            if not results:
                print("没有找到数据")
                return
                
            print(f"找到 {len(results)} 条数据")
            print()
            
            # 模拟策略分析器的参数设置
            minute_candles = []
            
            # 策略参数
            params = {
                'sell_rate': 0.01,
                'buy_rate': 1.0,
                'min_trigger_rest': 5,
                'rest_minutes': 0,
                'lookback_minutes_buy': 5,
                'lookback_minutes_sell': 2,
                'currency': 'DOGE',
                'is_live_trading': False,
                'reverse_buy': 0,
                'current_position': 0,
                'last_position_price': 0,
                'last_high': 0,
                'last_low': 999999,
                'last_close_time': None,
                'last_entry_time': None,
                'minute_candles': minute_candles,
                'use_new_condition': True
            }
            
            for i, row in enumerate(results):
                timestamp = row['timestamp']
                open_price = float(row['open'])
                close_price = float(row['close'])
                high_price = float(row['high'])
                low_price = float(row['low'])
                
                # 更新最高最低价
                if high_price > params['last_high']:
                    params['last_high'] = high_price
                if low_price < params['last_low']:
                    params['last_low'] = low_price
                
                # 模拟策略分析器中的涨跌判断逻辑
                if len(minute_candles) > 0:
                    prev_candle = minute_candles[-1]
                    # 上涨：当前最高值和最低值都比上一分钟高
                    if high_price > prev_candle['high'] and low_price > prev_candle['low']:
                        is_up = True
                    # 下跌：当前最高值和最低值都比上一分钟低
                    elif high_price < prev_candle['high'] and low_price < prev_candle['low']:
                        is_up = False
                    else:
                        # 横盘或混合情况，暂时按收盘价判断
                        is_up = close_price > open_price
                else:
                    # 第一条数据，按收盘价判断
                    is_up = close_price > open_price
                
                # 构造row数据
                row_data = {
                    'timestamp': str(timestamp),
                    'open': open_price,
                    'close': close_price,
                    'high': high_price,
                    'low': low_price
                }
                
                # 如果是00:42:00，直接调用trade_condition_new方法
                if '00:42:00' in str(timestamp):
                    print(f"\n=== 直接调用trade_condition_new - 00:42:00 ===")
                    print(f"当前时间: {timestamp}")
                    print(f"历史K线数量: {len(minute_candles)}")
                    print(f"当前K线: 开盘{open_price:.6f}, 收盘{close_price:.6f}, 最高{high_price:.6f}, 最低{low_price:.6f}")
                    print(f"当前持仓: {params['current_position']}")
                    print(f"最高价: {params['last_high']:.6f}, 最低价: {params['last_low']:.6f}")
                    
                    # 直接调用trade_condition_new方法
                    result = analyzer.trade_condition_new(row_data, params)
                    
                    print(f"\ntrade_condition_new返回结果:")
                    print(f"  action: {result['action']}")
                    print(f"  reason: {result['reason']}")
                    print(f"  trigger_price: {result['trigger_price']}")
                    print(f"  min_trigger_price: {result['min_trigger_price']}")
                    
                    # 如果触发了开仓，更新持仓状态
                    if result['action'] == '开多':
                        params['current_position'] = 1  # 模拟持仓
                        params['last_position_price'] = result['trigger_price']
                        params['last_entry_time'] = str(timestamp)
                        print(f"\n✅ 触发开多，更新持仓状态")
                        print(f"  持仓: {params['current_position']}")
                        print(f"  入场价: {params['last_position_price']}")
                
                # 如果是00:43:00，直接调用trade_condition_new方法
                if '00:43:00' in str(timestamp):
                    print(f"\n=== 直接调用trade_condition_new - 00:43:00 ===")
                    print(f"当前时间: {timestamp}")
                    print(f"历史K线数量: {len(minute_candles)}")
                    print(f"当前K线: 开盘{open_price:.6f}, 收盘{close_price:.6f}, 最高{high_price:.6f}, 最低{low_price:.6f}")
                    print(f"当前持仓: {params['current_position']}")
                    print(f"最高价: {params['last_high']:.6f}, 最低价: {params['last_low']:.6f}")
                    
                    # 直接调用trade_condition_new方法
                    result = analyzer.trade_condition_new(row_data, params)
                    
                    print(f"\ntrade_condition_new返回结果:")
                    print(f"  action: {result['action']}")
                    print(f"  reason: {result['reason']}")
                    print(f"  trigger_price: {result['trigger_price']}")
                    print(f"  min_trigger_price: {result['min_trigger_price']}")
                    
                    # 如果触发了平仓，更新持仓状态
                    if result['action'] == '平多':
                        params['current_position'] = 0  # 模拟平仓
                        params['last_position_price'] = 0
                        params['last_close_time'] = str(timestamp)
                        params['last_entry_time'] = None
                        print(f"\n✅ 触发平多，更新持仓状态")
                        print(f"  持仓: {params['current_position']}")
                
                # 添加到历史数据（模拟策略分析器的逻辑：在trade_condition_new之后添加）
                candle_data = {
                    'timestamp': timestamp,
                    'open': open_price,
                    'close': close_price,
                    'high': high_price,
                    'low': low_price,
                    'is_up': is_up
                }
                minute_candles.append(candle_data)
                
                # 更新params中的minute_candles
                params['minute_candles'] = minute_candles
                    
    except Exception as e:
        print(f"调试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_direct_call()
