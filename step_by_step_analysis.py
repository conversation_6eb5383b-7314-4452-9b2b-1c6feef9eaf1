#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
逐分钟分析策略逻辑
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def step_by_step_analysis():
    """逐分钟分析策略逻辑"""
    
    print("=== 逐分钟分析策略逻辑 ===")
    print()
    
    # 创建策略分析器实例
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 获取00:35:00到00:50:00的数据进行详细分析
    try:
        with analyzer.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT timestamp, open_price as open, close_price as close, 
                       high_price as high, low_price as low
                FROM crypto_prices 
                WHERE currency = 'DOGE' 
                AND timestamp >= '2025-06-06 00:35:00' 
                AND timestamp <= '2025-06-06 00:50:00'
                ORDER BY timestamp
            """)
            results = cursor.fetchall()
            
            if not results:
                print("没有找到数据")
                return
                
            print(f"找到 {len(results)} 条数据")
            print()
            
            # 模拟策略分析器的完整逻辑
            minute_candles = []
            current_position = 0  # 0=无仓, 1=多仓, -1=空仓
            
            # 策略参数
            lookback_minutes_buy = 5
            lookback_minutes_sell = 2
            buy_rate = 1.0
            
            for i, row in enumerate(results):
                timestamp = row['timestamp']
                open_price = float(row['open'])
                close_price = float(row['close'])
                high_price = float(row['high'])
                low_price = float(row['low'])
                
                print(f"=== {timestamp} ===")
                print(f"开盘: {open_price:.6f}, 收盘: {close_price:.6f}")
                print(f"最高: {high_price:.6f}, 最低: {low_price:.6f}")
                print(f"当前持仓: {current_position} ({'无仓' if current_position == 0 else '多仓' if current_position > 0 else '空仓'})")
                
                # 计算涨跌（模拟策略分析器的逻辑）
                if len(minute_candles) > 0:
                    prev_candle = minute_candles[-1]
                    print(f"上一分钟: 最高{prev_candle['high']:.6f}, 最低{prev_candle['low']:.6f}")
                    
                    # 上涨：当前最高值和最低值都比上一分钟高
                    if high_price > prev_candle['high'] and low_price > prev_candle['low']:
                        is_up = True
                        reason = "最高值和最低值都比上一分钟高"
                    # 下跌：当前最高值和最低值都比上一分钟低
                    elif high_price < prev_candle['high'] and low_price < prev_candle['low']:
                        is_up = False
                        reason = "最高值和最低值都比上一分钟低"
                    else:
                        # 横盘或混合情况，按收盘价判断
                        is_up = close_price > open_price
                        reason = f"横盘或混合情况，按收盘价判断: {close_price:.6f} {'>' if is_up else '<='} {open_price:.6f}"
                    
                    print(f"涨跌判断: {'涨' if is_up else '跌'} ({reason})")
                else:
                    # 第一条数据，按收盘价判断
                    is_up = close_price > open_price
                    print(f"涨跌判断: {'涨' if is_up else '跌'} (第一条数据，按收盘价判断)")
                
                # 添加到历史数据（模拟策略分析器的逻辑：先添加再判断）
                candle_data = {
                    'timestamp': timestamp,
                    'open': open_price,
                    'close': close_price,
                    'high': high_price,
                    'low': low_price,
                    'is_up': is_up
                }
                minute_candles.append(candle_data)
                
                # 保持最近20条数据（模拟策略分析器的逻辑）
                if len(minute_candles) > 20:
                    minute_candles = minute_candles[-20:]
                
                print(f"历史K线数量: {len(minute_candles)}")
                
                # 模拟trade_condition_new的逻辑
                action_taken = None
                
                if current_position == 0:  # 无仓
                    print(f"\n--- 检查开仓条件 ---")
                    
                    # 需要足够的历史数据
                    total_needed = lookback_minutes_buy + lookback_minutes_sell - 1  # 6分钟
                    print(f"需要历史数据: {total_needed}条, 实际: {len(minute_candles)}条")
                    
                    if len(minute_candles) >= total_needed:
                        # 获取最后total_needed条数据
                        all_candles = minute_candles[-total_needed:]
                        
                        print(f"分析的{total_needed}分钟数据:")
                        for j, candle in enumerate(all_candles):
                            trend = "涨" if candle['is_up'] else "跌"
                            print(f"  {j+1}. {candle['timestamp']} - {trend}")
                        
                        # 检查做多条件：先跌5分钟，再涨2分钟
                        down_period = all_candles[:lookback_minutes_buy]  # 前5分钟
                        up_period = all_candles[lookback_minutes_buy:]    # 后2分钟
                        
                        print(f"\n前{lookback_minutes_buy}分钟（下跌期）:")
                        for j, candle in enumerate(down_period):
                            trend = "涨" if candle['is_up'] else "跌"
                            status = "✅" if not candle['is_up'] else "❌"
                            print(f"  {status} {j+1}. {candle['timestamp']} - {trend}")
                        
                        print(f"\n后{lookback_minutes_sell}分钟（上涨期）:")
                        for j, candle in enumerate(up_period):
                            trend = "涨" if candle['is_up'] else "跌"
                            status = "✅" if candle['is_up'] else "❌"
                            print(f"  {status} {j+1}. {candle['timestamp']} - {trend}")

                        # 🔧 关键修正：确保后期有足够的数据
                        if len(up_period) < lookback_minutes_sell:
                            print(f"\n❌ 后期数据不足: 需要{lookback_minutes_sell}分钟，实际只有{len(up_period)}分钟")
                            print(f"❌ 不满足做多条件")
                        else:
                            # 检查条件
                            all_down_in_down_period = all(not candle['is_up'] for candle in down_period)
                            all_up_in_up_period = all(candle['is_up'] for candle in up_period)

                            print(f"\n前{lookback_minutes_buy}分钟连续下跌: {all_down_in_down_period}")
                            print(f"后{lookback_minutes_sell}分钟连续上涨: {all_up_in_up_period}")

                            if all_down_in_down_period and all_up_in_up_period:
                                # 找到最低点并计算触发价格
                                lowest_candle = min(down_period, key=lambda x: x['low'])
                                if buy_rate == 1:
                                    trigger_price = lowest_candle['high']
                                else:
                                    price_range = lowest_candle['high'] - lowest_candle['low']
                                    trigger_price = lowest_candle['low'] + (price_range * buy_rate)

                                print(f"最低点: {lowest_candle['timestamp']} - 最低价: {lowest_candle['low']:.6f}, 最高价: {lowest_candle['high']:.6f}")
                                print(f"触发价格: {trigger_price:.6f}")
                                print(f"当前最高价: {high_price:.6f}")

                                if high_price >= trigger_price:
                                    print(f"✅ 满足做多条件，开仓！")
                                    current_position = 1
                                    action_taken = "开多"
                                else:
                                    print(f"❌ 价格未达到触发条件")
                            else:
                                print(f"❌ 不满足做多条件")
                        
                        # 检查做空条件：先涨5分钟，再跌2分钟
                        up_period_first = all_candles[:lookback_minutes_buy]   # 前5分钟
                        down_period_second = all_candles[lookback_minutes_buy:] # 后2分钟
                        
                        all_up_in_up_period_first = all(candle['is_up'] for candle in up_period_first)
                        all_down_in_down_period_second = all(not candle['is_up'] for candle in down_period_second)
                        
                        if all_up_in_up_period_first and all_down_in_down_period_second:
                            # 找到最高点并计算触发价格
                            highest_candle = max(up_period_first, key=lambda x: x['high'])
                            if buy_rate == 1:
                                trigger_price = highest_candle['low']
                            else:
                                price_range = highest_candle['high'] - highest_candle['low']
                                trigger_price = highest_candle['high'] - (price_range * buy_rate)
                            
                            print(f"\n检查做空条件:")
                            print(f"前{lookback_minutes_buy}分钟连续上涨: {all_up_in_up_period_first}")
                            print(f"后{lookback_minutes_sell}分钟连续下跌: {all_down_in_down_period_second}")
                            print(f"最高点: {highest_candle['timestamp']} - 最低价: {highest_candle['low']:.6f}, 最高价: {highest_candle['high']:.6f}")
                            print(f"触发价格: {trigger_price:.6f}")
                            print(f"当前最低价: {low_price:.6f}")
                            
                            if low_price <= trigger_price and action_taken is None:
                                print(f"✅ 满足做空条件，开仓！")
                                current_position = -1
                                action_taken = "开空"
                            else:
                                print(f"❌ 价格未达到触发条件或已有其他操作")
                    else:
                        print(f"❌ 历史数据不足")
                
                elif current_position > 0:  # 多仓
                    print(f"\n--- 检查平多条件 ---")
                    print(f"需要数据: {lookback_minutes_sell}条, 实际: {len(minute_candles)}条")
                    
                    if len(minute_candles) >= lookback_minutes_sell:
                        recent_moves = minute_candles[-lookback_minutes_sell:]
                        
                        print(f"最近{lookback_minutes_sell}分钟数据:")
                        for j, candle in enumerate(recent_moves):
                            trend = "涨" if candle['is_up'] else "跌"
                            status = "✅" if not candle['is_up'] else "❌"
                            print(f"  {status} {j+1}. {candle['timestamp']} - {trend}")
                        
                        all_down = all(not candle['is_up'] for candle in recent_moves)
                        print(f"连续{lookback_minutes_sell}分钟下跌: {all_down}")
                        
                        if all_down:
                            print(f"✅ 满足平多条件，平仓！")
                            current_position = 0
                            action_taken = "平多"
                        else:
                            print(f"❌ 不满足平多条件")
                    else:
                        print(f"❌ 历史数据不足")
                
                elif current_position < 0:  # 空仓
                    print(f"\n--- 检查平空条件 ---")
                    print(f"需要数据: {lookback_minutes_sell}条, 实际: {len(minute_candles)}条")
                    
                    if len(minute_candles) >= lookback_minutes_sell:
                        recent_moves = minute_candles[-lookback_minutes_sell:]
                        
                        print(f"最近{lookback_minutes_sell}分钟数据:")
                        for j, candle in enumerate(recent_moves):
                            trend = "涨" if candle['is_up'] else "跌"
                            status = "✅" if candle['is_up'] else "❌"
                            print(f"  {status} {j+1}. {candle['timestamp']} - {trend}")
                        
                        all_up = all(candle['is_up'] for candle in recent_moves)
                        print(f"连续{lookback_minutes_sell}分钟上涨: {all_up}")
                        
                        if all_up:
                            print(f"✅ 满足平空条件，平仓！")
                            current_position = 0
                            action_taken = "平空"
                        else:
                            print(f"❌ 不满足平空条件")
                    else:
                        print(f"❌ 历史数据不足")
                
                if action_taken:
                    print(f"\n🎯 执行操作: {action_taken}")
                else:
                    print(f"\n⏸️  无操作")
                
                print("-" * 80)
                print()
                    
    except Exception as e:
        print(f"分析过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    step_by_step_analysis()
