@echo off
setlocal enabledelayedexpansion
title 策略分析工具

:menu
cls
echo ===================================
echo        策略分析工具菜单
echo ===================================
echo  1. 执行默认策略分析
echo  2. 执行自定义策略分析
echo  3. 启动客户端处理模式
echo  4. 重新运行指定策略ID
echo  5. 退出
echo ===================================
echo.

set /p CHOICE="请选择操作 (1-5): "

if "%CHOICE%"=="1" goto :default_strategy
if "%CHOICE%"=="2" goto :custom_strategy
if "%CHOICE%"=="3" goto :start_client
if "%CHOICE%"=="4" goto :rerun_strategy
if "%CHOICE%"=="5" goto :end
goto :menu

:default_strategy
cls
echo 执行默认策略分析...
echo.
set PYTHONUNBUFFERED=1
python strategy_analyzer.py --start "2025-04-22 00:00:00" --end "2025-04-22 23:59:59" --buy-rate 0.006 --sell-rate 0.004 --currency DOGE --lookback-minutes-buy 5 --lookback-minutes-sell 3 --min-trigger-rest 40 --rest 0
echo.
echo 策略分析完成！
pause
goto :menu

:custom_strategy
cls
echo 自定义策略分析
echo ==============

set /p START_DATE="输入开始日期 (格式: YYYY-MM-DD, 默认: 2025-04-22): "
if "%START_DATE%"=="" set START_DATE=2025-04-22

set /p END_DATE="输入结束日期 (格式: YYYY-MM-DD, 默认: 2025-04-22): "
if "%END_DATE%"=="" set END_DATE=2025-04-22

set /p START_TIME="输入开始时间 (格式: HH:MM:SS, 默认: 00:00:00): "
if "%START_TIME%"=="" set START_TIME=00:00:00

set /p END_TIME="输入结束时间 (格式: HH:MM:SS, 默认: 23:59:59): "
if "%END_TIME%"=="" set END_TIME=23:59:59

set /p BUY_RATE="输入买入阈值 (默认: 0.006): "
if "%BUY_RATE%"=="" set BUY_RATE=0.006

set /p SELL_RATE="输入卖出阈值 (默认: 0.004): "
if "%SELL_RATE%"=="" set SELL_RATE=0.004

set /p CURRENCY="输入币种 (默认: DOGE): "
if "%CURRENCY%"=="" set CURRENCY=DOGE

set /p LOOKBACK_BUY="输入买入回看分钟数 (默认: 5): "
if "%LOOKBACK_BUY%"=="" set LOOKBACK_BUY=5

set /p LOOKBACK_SELL="输入卖出回看分钟数 (默认: 3): "
if "%LOOKBACK_SELL%"=="" set LOOKBACK_SELL=3

set /p MIN_TRIGGER_REST="输入最小触发休息时间 (默认: 40): "
if "%MIN_TRIGGER_REST%"=="" set MIN_TRIGGER_REST=40

set /p REST="输入休息时间 (默认: 0): "
if "%REST%"=="" set REST=0

echo.
echo 将使用以下参数执行策略分析:
echo 时间范围: %START_DATE% %START_TIME% 至 %END_DATE% %END_TIME%
echo 买入阈值: %BUY_RATE%
echo 卖出阈值: %SELL_RATE%
echo 币种: %CURRENCY%
echo 买入回看分钟数: %LOOKBACK_BUY%
echo 卖出回看分钟数: %LOOKBACK_SELL%
echo 最小触发休息时间: %MIN_TRIGGER_REST%
echo 休息时间: %REST%
echo.

set /p CONFIRM="确认执行? (Y/N): "
if /i not "%CONFIRM%"=="Y" goto :menu

echo.
echo 开始执行策略分析...
set PYTHONUNBUFFERED=1
python strategy_analyzer.py --start "%START_DATE% %START_TIME%" --end "%END_DATE% %END_TIME%" --buy-rate %BUY_RATE% --sell-rate %SELL_RATE% --currency %CURRENCY% --lookback-minutes-buy %LOOKBACK_BUY% --lookback-minutes-sell %LOOKBACK_SELL% --min-trigger-rest %MIN_TRIGGER_REST% --rest %REST%
echo 策略分析完成！
pause
goto :menu

:start_client
cls
echo 启动客户端处理模式...
echo.
set /p CLIENT_ID="输入客户端ID (默认: a1): "
if "%CLIENT_ID%"=="" set CLIENT_ID=a1

set /p SLEEP_TIME="输入休眠时间 (秒, 默认: 5): "
if "%SLEEP_TIME%"=="" set SLEEP_TIME=5

echo 启动客户端: %CLIENT_ID%, 休眠时间: %SLEEP_TIME%秒
echo 按Ctrl+C可以终止程序
echo.
set PYTHONUNBUFFERED=1
python strategy_analyzer.py --client-id %CLIENT_ID% --sleep-time %SLEEP_TIME%
pause
goto :menu

:rerun_strategy
cls
echo 重新运行指定策略
echo ==============
echo.
set /p STRATEGY_ID="输入要重新运行的策略ID: "
if "%STRATEGY_ID%"=="" goto :menu

echo 将重新运行策略ID: %STRATEGY_ID%
set /p CONFIRM="确认执行? (Y/N): "
if /i not "%CONFIRM%"=="Y" goto :menu

echo.
echo 开始重新运行策略...
set PYTHONUNBUFFERED=1
python strategy_analyzer.py --rerun %STRATEGY_ID%
echo 策略重新运行完成！
pause
goto :menu

:end
echo 感谢使用策略分析工具！
exit /b 0
