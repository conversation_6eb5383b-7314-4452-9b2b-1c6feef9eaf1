import os
from okx_api_handler import okx_api
import logging
from typing import Dict, Optional
from decimal import Decimal
import time
import asyncio
from datetime import datetime
import traceback
import copy
from config import *




class TradingExecutor:
    def __init__(self, price_collector):
        self.logger = logging.getLogger(__name__)
        self.price_collector = price_collector
        self.pending_orders = {}  # 订单跟踪字典

        # send_email("系统启动", "系统启动")
        # 格式: {order_id: {'retries': 0, 'params': {}, 'created_at': timestamp}}


    def get_account_balance(self, currency: str = 'USDT') -> Dict:
        """
        获取账户余额

        Args:
            currency: 货币类型，默认USDT

        Returns:
            Dict: {
                'total_equity': 总权益,
                'available_balance': 可用余额,
                'margin_frozen': 保证金冻结,
                'currency': 货币类型,
                'updated_time': 更新时间
            }
        """
        try:
            result = okx_api.get_balance(ccy=currency)

            if result.get('code') == '0':
                data = result.get('data', [{}])[0]
                details = data.get('details', [])

                if not details:
                    return {
                        'error': f'未找到{currency}的余额信息',
                        'code': '404'
                    }

                balance_info = details[0]

                return {
                    'total_equity': Decimal(balance_info.get('eq', '0')),
                    'available_balance': Decimal(balance_info.get('availEq', '0')),
                    'margin_frozen': Decimal(balance_info.get('frozenBal', '0')),
                    'currency': balance_info.get('ccy', currency),
                    'updated_time': balance_info.get('uTime', ''),
                    'isolated_margin': Decimal(balance_info.get('isoEq', '0')),
                    'margin_ratio': Decimal(balance_info.get('mgnRatio', '0')),
                    'maintenance_margin_ratio': Decimal(balance_info.get('mmr', '0'))
                }
            else:
                print_log(f"获取账户余额失败: {result}")
                return {
                    'error': result.get('msg', '获取账户余额失败'),
                    'code': result.get('code')
                }

        except Exception as e:
            print_log(f"获取账户余额时发生错误: {str(e)}")
            return {'error': str(e)}

    def execute_perpetual_trade(self,
                              instId: str,           # 产品ID，如 BTC-USDT
                              tdMode: str,           # 交易模式
                              side: str,             # 订单方向 buy/sell
                              ordType: str,          # 订单类型 market/limit
                              sz: str,               # 委托数量
                              px: Optional[str] = None,     # 委托价格，市价单不需要
                              clOrdId: Optional[str] = None,  # 客户自定义订单ID
                              **kwargs               # 其他参数
                              ) -> Dict:
        """
        执行交易

        Args:
            instId: 产品ID，如 BTC-USDT
            tdMode: 交易模式 cash/isolated/cross
            side: 订单方向 buy/sell
            ordType: 订单类型 market/limit/post_only/fok/ioc
            sz: 委托数量
            px: 委托价格（限价单必需）
            clOrdId: 客户自定义订单ID
            **kwargs: 其他参数

        Returns:
            Dict: 订单结果
        """
        try:
            # 准备下单参数
            order_data = {
                "instId": instId,
                "tdMode": tdMode,
                "side": side,
                "ordType": ordType,
                "sz": sz
            }


            # 添加可选参数
            if px is not None:
                order_data["px"] = px
            if clOrdId is not None:
                order_data["clOrdId"] = clOrdId

            # 添加其他可选参数
            order_data.update(kwargs)
            print(order_data)
            # 执行下单
            result = okx_api.place_order(**order_data)

            if result.get('code') == '0':
                print_log(f"下单成功: {result}")
                return {
                    'success': True,
                    'order_id': result.get('data', [{}])[0].get('ordId'),
                    'client_order_id': result.get('data', [{}])[0].get('clOrdId'),
                    'status': 'success',
                    'raw_response': result
                }
            else:

                if result.get('msg') == '[Errno 11004] getaddrinfo failed':
                    print_log("网络异常，请检查网络连接")

                    exit();

                return {
                    'success': False,
                    'error': result.get('msg', '下单失败'),
                    'code': result.get('code'),
                    'raw_response': result
                }

        except Exception as e:
            error_msg = f"执行交易时发生错误: {str(e)}"
            print_log(error_msg)
            return {
                'success': False,
                'error': error_msg
            }

    def get_position_info(self, instId: str) -> Dict:
        """
        获取持仓信息

        Args:
            instId: 产品ID，如 BTC-USDT

        Returns:
            Dict: 持仓信息
        """
        try:
            result = okx_api.get_positions(instId=instId)

            if result.get('code') == '0':
                position_data = result.get('data', [{}])[0]

                def safe_decimal(value, default='0'):
                    try:
                        return Decimal(str(value)) if value not in ['', None] else Decimal(default)
                    except (ValueError, TypeError):
                        return Decimal(default)

                return {
                    'instId': instId,
                    'position_size': safe_decimal(position_data.get('pos')),
                    'entry_price': safe_decimal(position_data.get('avgPx')),
                    'unrealized_pnl': safe_decimal(position_data.get('upl')),
                    'margin_ratio': safe_decimal(position_data.get('mgnRatio')),
                    'leverage': safe_decimal(position_data.get('lever')),
                    'liquidation_price': safe_decimal(position_data.get('liqPx')),
                    'margin_mode': position_data.get('mgnMode', 'cross'),
                    'position_side': position_data.get('posSide', 'net')
                }
            else:
                print_log(f"获取持仓信息失败: {result}")
                return {
                    'error': result.get('msg', '获取持仓信息失败'),
                    'code': result.get('code')
                }

        except Exception as e:
            print_log(f"获取持仓信息时发生错误: {str(e)}")
            return {'error': str(e)}

    def get_history_orders(self,
                          instId: Optional[str] = None,
                          ordType: Optional[str] = None,
                          state: Optional[str] = None,
                          days: int = 7,
                          instType: str = 'SWAP') -> Dict:
        """
        获取历史订单记录

        Args:
            instId: 产品ID，如 BTC-USDT
            ordType: 订单类型 market/limit/post_only/fok/ioc
            state: 订单状态 live/filled/canceled
            days: 获取最近几天的订单记录，默认7天
            instType: 产品类型，如 SPOT/MARGIN/SWAP/FUTURES/OPTION，默认SWAP

        Returns:
            Dict: 包含历史订单列表的字典
        """
        try:
            # 计算时间范围
            end_time = int(time.time() * 1000)  # 当前时间
            start_time = end_time - (days * 24 * 60 * 60 * 1000)  # days天前

            # 调用API获取历史订单
            result = okx_api.get_history_orders(
                instId=instId,
                ordType=ordType,
                state=state,
                begin=str(start_time),
                end=str(end_time),
                limit='100',  # 最大限制
                instType=instType
            )

            if result.get('code') == '0':
                print_log(f"成功获取历史订单记录")
                return result
            else:
                print_log(f"获取历史订单记录失败: {result}")
                return result

        except Exception as e:
            error_msg = f"获取历史订单记录时发生错误: {str(e)}"
            print_log(error_msg)
            return {
                'error': error_msg
            }

    def calculate_contract_size(self,instrument_id: str,usdt_amount: float):
        """计算合约张数"""
        print("\n=== USDT金额换算合约张数 ===")
        print(f"计划投入USDT金额: {usdt_amount}")

        # 获取DOGE当前价格和合约信息
        result = okx_api.get_tickers(instType="SWAP")

        if result['code'] == 0:
            doge_ticker = None
            for ticker in result['data']:
                if ticker['instrument_id'] == instrument_id:
                    doge_ticker = ticker
                    break

            if doge_ticker:
                current_price = float(doge_ticker['last_price'])
                print(f"\n{instrument_id}当前价格: {current_price} USDT")

                # 获取合约具体信息
                contract_info = okx_api.get_instruments(instType="SWAP")
                contract_detail = None

                if contract_info['code'] == 0:
                    for instrument in contract_info['data']:
                        if instrument['instrument_id'] == instrument_id:
                            contract_detail = instrument
                            break

                    if contract_detail:
                        # 每张合约价值1000 DOGE
                        contract_value = 1000  # 每张合约代表的DOGE数量
                        min_size = float(contract_detail['min_size'])  # 最小下单数量

                        # 计算每张合约的USDT价值
                        contract_usdt_value = contract_value * current_price
                        print(f"每张合约价值: {contract_usdt_value:.4f} USDT")

                        # 计算能买多少张合约
                        contract_num = usdt_amount / contract_usdt_value
                        # 向下取整到最小下单单位
                        contract_num = (int(contract_num / min_size) * min_size)

                        # 计算实际能买到的DOGE数量
                        size = contract_num * contract_value
                        actual_usdt = contract_num * contract_usdt_value

                        print(f"\n换算结果:")
                        print(f"当前DOGE价格: {current_price} USDT")
                        print(f"每张合约面值: {contract_value} DOGE")
                        print(f"每张合约价值: {contract_usdt_value:.4f} USDT")
                        print(f"最小下单单位: {min_size} 张")
                        print(f"可以购买的合约张数: {contract_num} 张")
                        print(f"对应DOGE数量: {size:.4f} DOGE")
                        print(f"实际投入USDT: {actual_usdt:.4f} USDT")

                        return {
                            'contract_num': contract_num,
                            'size': size,
                            'current_price': current_price,
                            'actual_usdt': actual_usdt
                        }

        print("获取合约信息失败")
        return None

    def get_contract_spec(self, currency: str) -> dict:
        """
        增强版合约规格配置（增加错误处理）
        """
        specs = {
            'DOGE': {'coins_per_contract': 1000, 'min_size': 0.01},
            'BTC': {'coins_per_contract': 0.001, 'min_size': 0.0001},
            'ETH': {'coins_per_contract': 10, 'min_size': 0.01},
        }
        # 添加日志记录和默认值
        if currency.upper() not in specs:
            print_log(f"未找到{currency}的合约配置，使用默认值")
        return specs.get(currency.upper(), {'coins_per_contract': 1, 'min_size': 0.01})

    def convert_contract_size(self, currency: str, amount: float, is_usdt: bool = False, price: float = None) -> dict:
        """
        增强版合约转换方法，返回完整交易信息
        返回格式:
        {
            'contract_num': 实际可买张数,
            'usdt_value': 合约总价值(USDT),
            'actual_amount': 实际交易币数量,
            'min_size': 最小交易单位,
            'coins_per_contract': 单张合约币数量,
            'status': 'success/error',
            'message': 附加信息
        }
        """
        try:
            spec = self.get_contract_spec(currency.upper())

            if is_usdt and not price:
                raise ValueError("USDT转换需要当前价格")

            # 计算基础值
            contract_value = spec['coins_per_contract'] * (price if is_usdt else 1)
            raw_contract_num = amount / contract_value

            # 处理精度
            decimal_places = len(str(spec['min_size']).split('.')[-1]) if '.' in str(spec['min_size']) else 0
            contract_num = round((raw_contract_num // spec['min_size']) * spec['min_size'], decimal_places)
            contract_num = max(contract_num, spec['min_size'])

            return {
                'contract_num': contract_num,
                'size': contract_num * spec['coins_per_contract'],
                'usdt_value': contract_num * contract_value if is_usdt else contract_num * price,
                'actual_amount': contract_num * spec['coins_per_contract'],
                'min_size': spec['min_size'],
                'coins_per_contract': spec['coins_per_contract'],
                'status': 'success',
                'message': f"最大可买{raw_contract_num:.2f}张，实际下单{contract_num}张"
            }

        except Exception as e:
            print_log(f"合约转换失败: {str(e)}")
            return {
                'contract_num': 0,
                'usdt_value': 0,
                'actual_amount': 0,
                'status': 'error',
                'message': str(e)
            }

    def get_instrument_info(self, instId: str) -> dict:
        """获取合约信息"""
        try:
            result = okx_api.get_instruments(instType="SWAP", instId=instId)
            if result.get('code') == '0' and len(result.get('data', [])) > 0:
                return result['data'][0]
            return {}
        except Exception as e:
            print_log(f"获取合约信息失败: {str(e)}")
            return {}
    def execute_live_trade(self,
                               side: str,            # 交易方向，'buy' 或 'sell'
                               pos_side: str,        # 仓位方向，'long' 或 'short'
                               contract_num: float,          # 合约数量
                               price: float,
                               coins_per_contract: float) -> str: # 单张合约币数量
        """执行实盘交易，根据给定的交易方向、仓位方向、合约数量和价格下单。

        Args:
            side (str): 交易方向，'buy' 或 'sell'。
            pos_side (str): 仓位方向，'long' 或 'short'。
            contract_num (float): 合约数量。
            price (float): 交易价格。

        Returns:
            str: 交易结果，'success' 或 'error'。
        """
        try:

            self.price_collector.pre_params = self.price_collector.params.copy()

            # 设置lock_time到params字典中
            self.price_collector.params['lock_time'] = time.time()+6

            # 获取最新订单簿并检查时效性
            order_book = self.price_collector.params['order_book']
            current_time = time.time()

            # 检查订单簿是否存在且是否在5秒内更新
            is_orderbook_valid = (
                order_book and
                order_book.get('timestamp') and
                (current_time - datetime.fromisoformat(order_book['timestamp']).timestamp()) <= 5
            )

            if not is_orderbook_valid:
                print_log("订单簿数据已过期或无效，使用市场价并重新订阅")
                # 获取市场价
                market_price = float(price)

                # 使用price_collector的统一重连方法重新订阅订单簿
                asyncio.create_task(self.price_collector.establish_connection('public'))

                # 根据交易方向调整价格
                if side == 'buy':
                    price = round(market_price * 1.0001, 5)
                else:
                    price = round(market_price * 0.9999, 5)
            else:
                # 使用有效的订单簿数据
                bids = order_book['bids'][:4]
                asks = order_book['asks'][:4]

                if side == 'buy':
                    price = bids[0][0]
                else:
                    price = asks[0][0]

            if self.price_collector.tradding_retry_count>0:
                print_log(f"重试超过一次,直接市价操作 目前{self.price_collector.tradding_retry_count}")
                ordType="market"
            else:
                ordType="post_only"
                ordType="market" # 都按照市价测试

            # 调用API下单
            order_result = okx_api.place_order(
                instId=self.price_collector.instrument,
                tdMode="cross",
                side=side,
                posSide=pos_side,
                ordType=ordType,
                px=str(price),
                sz=str(contract_num),  # 使用实际张数
                clOrdId=f"algo{int(time.time() * 1000)}"
            )

            if order_result.get('code') != '0' :
                send_email("系统出错2", "系统出错2")
                print_log(f"{order_result}");
                if(order_result.get('sCode') == '51008' or order_result.get('code') == '500'):
                    return {
                            'status': 'fail',
                            'msg':order_result.get('sMsg')
                        }
                else:

                    sys.exit()


            order_data = order_result['data'][0]
            order_id = str(order_data['ordId'])

            # 记录交易结果
            print_log(f"""
            成功下单
            ID: {order_id}
            方向: {side} {pos_side}
            价格: {price}
            数量: {contract_num}张
            状态: {order_data['sMsg']}
            """)

            amount=contract_num*coins_per_contract*price

            # 启动监控任务
            asyncio.create_task(self.monitor_order_status(order_id))

            # 更新pending_orders
            self.pending_orders[order_id] = {
                'retries': 0,
                'params': {
                    'side': side,
                    'pos_side': pos_side,
                    'price': price,
                    'size': contract_num*coins_per_contract,
                    'contract_num': contract_num,
                    'amount': amount,
                    'coins_per_contract': coins_per_contract
                },
                'state': 'live',
                'created_at': time.time()
            }

            # 更新策略参数（添加详细交易信息）
            self.price_collector.params.update({
                'last_order_detail': order_data,
                'last_order_time': datetime.now().isoformat()
            })

            # 其他逻辑保持不变...
            return {
                'order_id': order_id,
                'amount': amount,
                'price': price,
                'contract_num': contract_num,
                'size': contract_num*coins_per_contract,
                'status': 'success',
                'coins_per_contract': coins_per_contract
            }

        except Exception as e:
            print_log(f"执行交易时发生错误: {str(e)}\n{traceback.format_exc()}")
            return None

    async def _resubscribe_orderbook(self):
        """重新订阅订单簿"""
        try:
            # 获取公共连接的WebSocket对象
            for ws in self.price_collector.connections.values():
                if hasattr(ws, 'conn_type') and ws.conn_type == 'public':
                    # 使用price_collector的统一重连方法
                    await self.price_collector.establish_connection('public')
                    return

            print_log("未找到公共频道WebSocket连接")

        except Exception as e:
            print_log(f"重新订阅订单簿失败: {str(e)}\n{traceback.format_exc()}")

    async def monitor_order_status(self, order_id):
        """订单状态监控任务"""
        retry_count = 0
        while retry_count < 5:
            await asyncio.sleep(5)

            # 获取最新状态
            order_data = self.pending_orders.get(order_id)
            if not order_data:
                return


            if order_data['state'] == 'live':
                print_log(f"订单{order_id}仍待成交，尝试撤单重试（第{retry_count+1}次）")

                # 调用撤单API - 确保传递instId参数
                cancel_result = okx_api.cancel_order(
                    ordId=order_id,
                    instId=self.price_collector.instrument  # 必须提供instId
                )

                if cancel_result.get('code') == '0':

                    #只撤销订单不再下单
                    self.price_collector.tradding_retry_count+=1
                    self.revert_trade_status(order_id)
                    return

                    # 重新下单 - 注意execute_live_trade现在返回字典
                    new_order_result = self.execute_live_trade(
                        order_data['params']['side'],
                        order_data['params']['pos_side'],
                        order_data['params']['contract_num'],
                        order_data['params']['price'],
                        order_data['params']['coins_per_contract']
                    )

                    # 检查返回结果并提取订单ID
                    if new_order_result and isinstance(new_order_result, dict) and new_order_result.get('order_id'):
                        new_order_id = new_order_result['order_id']
                        self.pending_orders[new_order_id] = {
                            **order_data,
                            'retries': order_data['retries'] + 1
                        }
                        del self.pending_orders[order_id]
                        print_log(f"已重新下单: {new_order_id} 重试次数: {retry_count+1} 时间: {datetime.now().isoformat()}")
                        return
                    else:
                        print_log(f"重新下单失败4: {new_order_result}")
                else:
                    print_log(f"撤单失败5: {cancel_result}")


            retry_count += 1

        # 超过重试次数仍失败
        print_log(f"订单{order_id}超过最大重试次数 重试次数: {retry_count} 时间: {datetime.now().isoformat()}")

        # 重置参数时考虑交易类型，恢复到上一次状态
        self.revert_trade_status(order_id)

    def revert_trade_status(self, order_id):

                # 重置参数时考虑交易类型，恢复到上一次状态
        order_data = self.pending_orders.get(order_id, {})


        print_log(f"上次持仓时间: {self.price_collector.params['last_entry_time']}")

        self.price_collector.params=self.price_collector.pre_params.copy()

        print_log(f"上次持仓时间: {self.price_collector.params['last_entry_time']}")
        # 设置lock_time到params字典中
        self.price_collector.params['lock_time'] = time.time()+6
        if order_id in self.pending_orders:
            del self.pending_orders[order_id]

        self.price_collector.pre_params={}


    async def handle_canceled_order(self, order_id):
        await asyncio.sleep(3)
        """处理已撤销订单"""
        if order_id in self.pending_orders:
            order_data = self.pending_orders[order_id]
            if order_data['retries'] < 5:
                try:
                    # 重新下单 - 注意execute_live_trade现在返回字典
                    new_order_result = self.execute_live_trade(
                        order_data['params']['side'],
                        order_data['params']['pos_side'],
                        order_data['params']['contract_num'],
                        order_data['params']['price'],
                        order_data['params']['coins_per_contract']
                    )

                    # 检查返回结果并提取订单ID
                    if new_order_result and isinstance(new_order_result, dict) and new_order_result.get('order_id'):
                        new_order_id = new_order_result['order_id']
                        self.pending_orders[new_order_id] = {
                            **order_data,
                            'retries': order_data['retries'] + 1,
                            'state': 'live'
                        }
                        print_log(f"已重新下单7: {new_order_id} 重试次数: {order_data['retries']+1} 时间: {datetime.now().isoformat()}")
                    else:
                        print_log(f"重新下单失败6: {new_order_result} 时间: {datetime.now().isoformat()}")
                except Exception as e:
                    print_log(f"处理已撤销订单时出错: {str(e)} 时间: {datetime.now().isoformat()}")
            else:
                print_log(f"订单{order_id}已达到最大重试次数 重试次数: {order_data['retries']} 时间: {datetime.now().isoformat()}")
                # 恢复到上一次状态
                self.revert_trade_status(order_id)

            # 无论如何都删除原订单
            if order_id in self.pending_orders:
                del self.pending_orders[order_id]

    async def handle_filled_order(self, order_id, filled_data=None):

        print(f"处理已完全成交的订单: {order_id}")
        """处理已完全成交的订单"""
        try:
            await asyncio.sleep(1)  # 等待一下确保数据同步

            if order_id not in self.pending_orders:
                print_log(f"找不到订单ID: {order_id}")
                return

            order_data = self.pending_orders[order_id]
            print(f"处理已完全成交的订单: {order_id} 3 ")
            # 更新交易记录中的实际成交信息
            if len(self.price_collector.params['trades']) > 0:
                latest_trade = self.price_collector.params['trades'][-1]

                print(f"处理已完全成交的订单: {order_id} 4 ")
                # 确保这是同一笔交易
                if latest_trade.get('live_order_no') == order_id:

                    print(f"处理已完全成交的订单: {order_id} 5 ")
                    # 先上次更新的费用. 重新更新
                    self.price_collector.params['total_fees'] -= latest_trade.get('fee',0)
                    self.price_collector.params['account_value'] -= latest_trade.get('fee',0)
                    self.price_collector.params['account_value'] -= latest_trade.get('profit',0)
                    self.price_collector.params['available_capital'] -= latest_trade.get('fee',0)
                    self.price_collector.params['available_capital'] -= latest_trade.get('profit',0)


                    ##latest_trade['pre_info'] = copy.deepcopy(latest_trade)

                    # 更新交易记录
                    latest_trade.update({
                        'entry_price': float(filled_data['avgPx']),
                        'position_size': float(filled_data['sz']) * order_data['params']['coins_per_contract'],
                        'size': float(filled_data['sz']) * order_data['params']['coins_per_contract'],
                        'amount': float(filled_data['avgPx']) * float(filled_data['sz']) * order_data['params']['coins_per_contract'],
                        'fee': float(filled_data['fillFee']),
                        'timestamp': datetime.fromtimestamp(int(filled_data['uTime'])/1000).isoformat(),
                        'profit': float(filled_data['fillPnl']),
                        'capital_after': latest_trade.get('capital_before',0) + float(filled_data['fillPnl'])+float(filled_data['fillFee']),
                        'live_available_balance': float(self.price_collector.params['live_available_balance']),
                        'live_total_equity': float(self.price_collector.params['live_total_equity']),
                        'live_position_value': float(self.price_collector.params['live_position_value']),
                        'live_unrealized_pnl': float(self.price_collector.params['live_unrealized_pnl'])
                    })

                    # 更新账户价值
                    # 先上次更新的费用. 重新更新
                    self.price_collector.params['total_fees'] += latest_trade.get('fee',0)
                    self.price_collector.params['account_value'] += latest_trade.get('fee',0)
                    self.price_collector.params['account_value'] += latest_trade.get('profit',0)
                    self.price_collector.params['available_capital'] += latest_trade.get('fee',0)
                    self.price_collector.params['available_capital'] += latest_trade.get('profit',0)

                    self.price_collector.params['last_order_time'] = datetime.fromtimestamp(int(filled_data['uTime'])/1000).isoformat()

                    print_log(f"""
                    订单完全成交:
                    ID: {order_id}
                    均价: {filled_data['avgPx']}
                    数量: {filled_data['sz']}
                    方向: {filled_data['side']} {filled_data['posSide']}
                    费用: {filled_data['fillFee']}
                    利润: {filled_data['fillPnl']}
                    时间: {datetime.fromtimestamp(int(filled_data['uTime'])/1000).isoformat()}
                    """)

            # 从待处理订单中移除
            del self.pending_orders[order_id]

        except Exception as e:
            print_log(f"处理已成交订单时出错: {str(e)}\n{traceback.format_exc()}")

# 创建全局实例（带默认参数）
trading_executor = TradingExecutor(price_collector=None)