#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
寻找满足新交易条件的机会
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def find_trading_opportunities():
    """寻找满足新交易条件的机会"""
    
    print("=== 寻找满足新交易条件的机会 ===")
    print()
    
    # 创建策略分析器实例
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 获取更多数据进行分析
    try:
        with analyzer.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT timestamp, open_price as open, close_price as close, 
                       high_price as high, low_price as low
                FROM crypto_prices 
                WHERE currency = 'DOGE' 
                AND timestamp >= '2025-06-02 00:00:00' 
                AND timestamp <= '2025-06-02 06:00:00'
                ORDER BY timestamp 
                LIMIT 200
            """)
            results = cursor.fetchall()
            
            if not results:
                print("没有找到数据")
                return
                
            print(f"找到 {len(results)} 条数据")
            print()
            
            # 模拟新策略的逻辑（简化版本）
            minute_candles = []
            lookback_minutes_buy = 1
            buy_rate = 0.5
            
            long_opportunities = []
            short_opportunities = []

            # 统计涨跌情况
            up_count = 0
            down_count = 0
            
            for i, row in enumerate(results):
                # 计算涨跌
                open_price = float(row['open'])
                close_price = float(row['close'])
                high_price = float(row['high'])
                low_price = float(row['low'])

                # 新的涨跌判断标准：对比上一分钟的最高值和最低值
                is_up_final = None
                if len(minute_candles) > 0:
                    prev_candle = minute_candles[-1]
                    # 上涨：当前最高值和最低值都比上一分钟高
                    if high_price > prev_candle['high'] and low_price > prev_candle['low']:
                        is_up_final = True
                    # 下跌：当前最高值和最低值都比上一分钟低
                    elif high_price < prev_candle['high'] and low_price < prev_candle['low']:
                        is_up_final = False
                    else:
                        # 横盘或混合情况，暂时按收盘价判断
                        is_up_final = close_price > open_price
                else:
                    # 第一条数据，按收盘价判断
                    is_up_final = close_price > open_price

                # 统计涨跌
                if is_up_final:
                    up_count += 1
                else:
                    down_count += 1

                # 添加到历史数据
                candle_data = {
                    'timestamp': row['timestamp'],
                    'open': open_price,
                    'close': close_price,
                    'high': high_price,
                    'low': low_price,
                    'is_up': is_up_final
                }
                minute_candles.append(candle_data)
                
                # 保持最近20条数据
                if len(minute_candles) > 20:
                    minute_candles = minute_candles[-20:]
                
                # 如果有足够的历史数据，进行连续涨跌分析
                if len(minute_candles) > lookback_minutes_buy:  # 注意这里改为 > 而不是 >=
                    # 获取历史数据（不包括当前刚添加的数据）
                    historical_candles = minute_candles[-(lookback_minutes_buy+1):-1]  # 取历史lookback_minutes_buy次

                    # 检查历史是否连续下跌
                    all_down = all(not candle['is_up'] for candle in historical_candles)
                    # 检查历史是否连续上涨
                    all_up = all(candle['is_up'] for candle in historical_candles)

                    # 调试：显示一些基本模式
                    if i < 10:  # 只显示前10个
                        print(f"第{i+1}条数据 {row['timestamp']}: 当前{'涨' if is_up_final else '跌'}")
                        print(f"  历史{lookback_minutes_buy}分钟: {['涨' if c['is_up'] else '跌' for c in historical_candles]}")
                        print(f"  连续下跌: {all_down}, 连续上涨: {all_up}")
                        print(f"  下跌后上涨: {all_down and is_up_final}")
                        print(f"  上涨后下跌: {all_up and not is_up_final}")
                        print()
                    
                    # 如果历史连续下跌，且当前K线上涨，检查做多条件
                    if all_down and is_up_final:
                        print(f"找到潜在做多机会: {row['timestamp']}")
                        print(f"历史连续下跌: {[candle['timestamp'] for candle in historical_candles]}")
                        print(f"当前上涨: 开盘{open_price:.6f}, 收盘{close_price:.6f}")

                        # 找到最低点
                        lowest_candle = min(historical_candles, key=lambda x: x['low'])
                        print(f"最低点: {lowest_candle['timestamp']}, 最低价{lowest_candle['low']:.6f}, 最高价{lowest_candle['high']:.6f}")

                        # 计算触发价格
                        if buy_rate == 1:
                            trigger_price = lowest_candle['high']
                        else:
                            price_range = lowest_candle['high'] - lowest_candle['low']
                            trigger_price = lowest_candle['low'] + (price_range * buy_rate)

                        print(f"触发价格: {trigger_price:.6f}, 当前最高价: {high_price:.6f}")
                        print(f"价格达到: {high_price >= trigger_price}")

                        # 检查当前价格是否达到触发条件
                        if high_price >= trigger_price:
                            long_opportunities.append({
                                'timestamp': row['timestamp'],
                                'open': open_price,
                                'close': close_price,
                                'high': high_price,
                                'low': low_price,
                                'trigger_price': trigger_price,
                                'lowest_candle': lowest_candle,
                                'recent_candles': historical_candles.copy()
                            })
                            print("✅ 添加到做多机会列表")
                        else:
                            print("❌ 价格未达到触发条件")
                        print()

                    # 如果历史连续上涨，且当前K线下跌，检查做空条件
                    elif all_up and not is_up_final:
                        # 找到最高点
                        highest_candle = max(historical_candles, key=lambda x: x['high'])

                        # 计算触发价格
                        if buy_rate == 1:
                            trigger_price = highest_candle['low']
                        else:
                            price_range = highest_candle['high'] - highest_candle['low']
                            trigger_price = highest_candle['high'] - (price_range * buy_rate)

                        # 检查当前价格是否达到触发条件
                        if low_price <= trigger_price:
                            short_opportunities.append({
                                'timestamp': row['timestamp'],
                                'open': open_price,
                                'close': close_price,
                                'high': high_price,
                                'low': low_price,
                                'trigger_price': trigger_price,
                                'highest_candle': highest_candle,
                                'recent_candles': historical_candles.copy()
                            })
            
            # 显示统计信息
            print(f"涨跌统计: 上涨{up_count}次, 下跌{down_count}次")
            print()

            # 显示结果
            print(f"找到 {len(long_opportunities)} 个做多机会:")
            for i, opp in enumerate(long_opportunities):
                print(f"\n=== 做多机会 {i+1} ===")
                print(f"时间: {opp['timestamp']}")
                print(f"当前K线: 开盘{opp['open']:.6f}, 收盘{opp['close']:.6f}, 最高{opp['high']:.6f}, 最低{opp['low']:.6f}")
                print(f"触发价格: {opp['trigger_price']:.6f}")
                print(f"最低点K线: {opp['lowest_candle']['timestamp']}, 最低价{opp['lowest_candle']['low']:.6f}")
                print("历史1分钟:")
                for j, candle in enumerate(opp['recent_candles']):
                    trend = "涨" if candle['is_up'] else "跌"
                    print(f"  {j+1}. {candle['timestamp']} - {trend} (开盘:{candle['open']:.6f}, 收盘:{candle['close']:.6f})")

            print(f"\n找到 {len(short_opportunities)} 个做空机会:")
            for i, opp in enumerate(short_opportunities):
                print(f"\n=== 做空机会 {i+1} ===")
                print(f"时间: {opp['timestamp']}")
                print(f"当前K线: 开盘{opp['open']:.6f}, 收盘{opp['close']:.6f}, 最高{opp['high']:.6f}, 最低{opp['low']:.6f}")
                print(f"触发价格: {opp['trigger_price']:.6f}")
                print(f"最高点K线: {opp['highest_candle']['timestamp']}, 最高价{opp['highest_candle']['high']:.6f}")
                print("历史1分钟:")
                for j, candle in enumerate(opp['recent_candles']):
                    trend = "涨" if candle['is_up'] else "跌"
                    print(f"  {j+1}. {candle['timestamp']} - {trend} (开盘:{candle['open']:.6f}, 收盘:{candle['close']:.6f})")
                
    except Exception as e:
        print(f"查找过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    find_trading_opportunities()
