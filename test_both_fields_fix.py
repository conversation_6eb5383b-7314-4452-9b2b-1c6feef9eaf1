#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 reason 和 trigger_reason 字段截断的脚本
"""

def test_both_fields_truncation():
    """测试两个字段的截断处理"""
    
    print("🔍 测试 reason 和 trigger_reason 字段截断")
    print("="*60)
    
    # 模拟 row_reson_list
    row_reson_list = {}
    
    # 模拟包含长reason和长trigger_reason的交易
    trade = {
        'timestamp': '2025-07-25 21:48:48',
        'action': '平多',
        'reason': """平多触发
条件：连续2次下跌
倍率：sell_rate=0.2
参考K线：01:11:00(开:0.14828,高:0.14865,低:0.14824)
价格范围：最高价-最低价=0.14865-0.14824=0.00041
计算公式：最高价-价格范围×sell_rate
计算结果：0.14865-0.00041×0.2=0.14857
价格保护：触发价格0.14857>开盘价0.14828
实际触发：直接以开盘价0.14828触发
详细分析：当前K线开盘价为0.14828，最高价为0.14865，最低价为0.14824
价格范围计算：0.14865 - 0.14824 = 0.00041
触发价格计算：0.14865 - 0.00041 × 0.2 = 0.14857
由于触发价格0.14857大于开盘价0.14828，超出了K线范围
因此采用价格保护机制，直接以开盘价0.14828作为触发价格""",
        'trigger_reason': """触发条件详细分析：
1. 检测到连续2次下跌趋势
2. 当前K线时间：2025-07-25 21:48:48
3. 参考上涨K线：2025-07-25 21:47:00
4. 上涨K线数据：开盘0.14828，最高0.14865，最低0.14824，收盘0.14855
5. 价格范围计算：最高价-最低价=0.14865-0.14824=0.00041
6. 触发价格计算：最高价-价格范围×sell_rate=0.14865-0.00041×0.2=0.14857
7. 当前K线开盘价：0.14828
8. 价格保护检查：触发价格0.14857>开盘价0.14828，超出K线范围
9. 保护机制启动：直接使用开盘价0.14828作为触发价格
10. 最终触发：价格跌破0.14828，执行平多操作""",
        'price': 0.14828
    }
    
    # 模拟修复后的逻辑
    print("📊 原始数据:")
    print(f"reason 长度: {len(trade['reason'])} 字符")
    print(f"trigger_reason 长度: {len(trade['trigger_reason'])} 字符")
    
    # 处理 reason 字段
    original_reason = trade.get('reason', '')
    reason = original_reason
    
    if len(original_reason) > 250:
        reason = original_reason[:247] + '...'
        key = f"{trade['timestamp']}_{trade['action']}_reason_truncated"
        row_reson_list[key] = {
            'timestamp': trade['timestamp'],
            'action': trade['action'],
            'field': 'reason',
            'full_content': original_reason,
            'truncated_content': reason,
            'original_length': len(original_reason),
            'truncated_length': len(reason)
        }
        print(f"⚠️ reason 过长已截断: {len(original_reason)} → {len(reason)} 字符")
    else:
        print(f"✅ reason 长度正常: {len(original_reason)} 字符")
    
    # 处理 trigger_reason 字段
    original_trigger_reason = trade.get('trigger_reason', '')
    trigger_reason = original_trigger_reason
    
    if len(original_trigger_reason) > 250:
        trigger_reason = original_trigger_reason[:247] + '...'
        key = f"{trade['timestamp']}_{trade['action']}_trigger_reason_truncated"
        row_reson_list[key] = {
            'timestamp': trade['timestamp'],
            'action': trade['action'],
            'field': 'trigger_reason',
            'full_content': original_trigger_reason,
            'truncated_content': trigger_reason,
            'original_length': len(original_trigger_reason),
            'truncated_length': len(trigger_reason)
        }
        print(f"⚠️ trigger_reason 过长已截断: {len(original_trigger_reason)} → {len(trigger_reason)} 字符")
    else:
        print(f"✅ trigger_reason 长度正常: {len(original_trigger_reason)} 字符")
    
    print(f"\n📝 截断记录数: {len(row_reson_list)}")
    
    # 显示截断后的内容
    print("\n📄 截断后内容预览:")
    print(f"reason (前100字符): {reason[:100]}...")
    print(f"trigger_reason (前100字符): {trigger_reason[:100]}...")
    
    return row_reson_list, reason, trigger_reason

def test_database_compatibility():
    """测试数据库兼容性"""
    
    print("\n💾 数据库兼容性测试")
    print("="*60)
    
    row_reson_list, reason, trigger_reason = test_both_fields_truncation()
    
    # 模拟数据库插入的数据
    db_data = {
        'reason': reason,
        'trigger_reason': trigger_reason
    }
    
    print("\n📊 数据库字段长度检查:")
    for field, value in db_data.items():
        length = len(value)
        status = "✅ 通过" if length <= 255 else "❌ 超长"
        print(f"{field}: {length} 字符 - {status}")
    
    # 验证所有字段都在限制范围内
    all_valid = all(len(value) <= 255 for value in db_data.values())
    print(f"\n🎯 总体结果: {'✅ 所有字段都符合数据库限制' if all_valid else '❌ 仍有字段超长'}")
    
    return all_valid

def test_json_export_structure():
    """测试JSON导出结构"""
    
    print("\n📋 JSON导出结构测试")
    print("="*60)
    
    row_reson_list, _, _ = test_both_fields_truncation()
    
    if row_reson_list:
        import json
        
        print("JSON结构:")
        for key, value in row_reson_list.items():
            print(f"\n键: {key}")
            print(f"  字段: {value['field']}")
            print(f"  时间: {value['timestamp']}")
            print(f"  动作: {value['action']}")
            print(f"  原长度: {value['original_length']}")
            print(f"  截断长度: {value['truncated_length']}")
        
        # 测试JSON序列化
        try:
            json_str = json.dumps(row_reson_list, ensure_ascii=False, indent=2)
            print(f"\n✅ JSON序列化成功，长度: {len(json_str)} 字符")
        except Exception as e:
            print(f"\n❌ JSON序列化失败: {e}")
    else:
        print("没有截断数据需要导出")

def test_edge_cases():
    """测试边界情况"""
    
    print("\n🧪 边界情况测试")
    print("="*60)
    
    test_cases = [
        {
            'name': '正常长度',
            'reason': '做多触发：价格突破',
            'trigger_reason': '触发条件满足'
        },
        {
            'name': '刚好250字符',
            'reason': 'a' * 250,
            'trigger_reason': 'b' * 250
        },
        {
            'name': '刚好255字符',
            'reason': 'a' * 255,
            'trigger_reason': 'b' * 255
        },
        {
            'name': '超长字符',
            'reason': 'a' * 400,
            'trigger_reason': 'b' * 500
        }
    ]
    
    for case in test_cases:
        print(f"\n测试: {case['name']}")
        
        # 模拟截断逻辑
        reason = case['reason']
        trigger_reason = case['trigger_reason']
        
        if len(reason) > 250:
            reason = reason[:247] + '...'
            print(f"  reason: {len(case['reason'])} → {len(reason)} 字符")
        else:
            print(f"  reason: {len(reason)} 字符 (无需截断)")
        
        if len(trigger_reason) > 250:
            trigger_reason = trigger_reason[:247] + '...'
            print(f"  trigger_reason: {len(case['trigger_reason'])} → {len(trigger_reason)} 字符")
        else:
            print(f"  trigger_reason: {len(trigger_reason)} 字符 (无需截断)")

if __name__ == "__main__":
    print("🧪 reason 和 trigger_reason 字段截断测试")
    print("="*70)
    
    # 测试基本截断功能
    test_both_fields_truncation()
    
    # 测试数据库兼容性
    is_compatible = test_database_compatibility()
    
    # 测试JSON导出
    test_json_export_structure()
    
    # 测试边界情况
    test_edge_cases()
    
    print("\n" + "="*70)
    print("✅ 所有测试完成！")
    
    if is_compatible:
        print("\n🎉 修复成功！现在可以正常保存到数据库了")
    else:
        print("\n⚠️ 仍有问题需要解决")
    
    print("\n💡 修复要点:")
    print("1. reason 字段截断到250字符")
    print("2. trigger_reason 字段截断到250字符") 
    print("3. 完整内容保存到JSON文件")
    print("4. 使用字典结构保存截断信息")
