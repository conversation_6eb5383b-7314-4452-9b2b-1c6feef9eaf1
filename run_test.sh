#!/bin/bash

echo "=== 新策略命令行测试 ==="
echo

echo "测试1: 基本参数测试 (2025-06-06)"
python test_new_strategy_cli.py --start "2025-06-06 00:00:00" --end "2025-06-06 23:59:59" --buy_rate 1.0 --lookback_buy 5 --lookback_sell 2

echo
echo "测试2: 不同参数测试 (lookback_buy=3, lookback_sell=3)"
python test_new_strategy_cli.py --start "2025-06-06 00:00:00" --end "2025-06-06 23:59:59" --buy_rate 1.0 --lookback_buy 3 --lookback_sell 3

echo
echo "测试3: 不同buy_rate测试 (buy_rate=0.5)"
python test_new_strategy_cli.py --start "2025-06-06 00:00:00" --end "2025-06-06 23:59:59" --buy_rate 0.5 --lookback_buy 5 --lookback_sell 2

echo
echo "测试完成！"
