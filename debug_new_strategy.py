#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试新策略的详细逻辑
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def debug_new_strategy():
    """调试新策略的详细逻辑"""
    
    print("=== 调试新策略的详细逻辑 ===")
    print()
    
    # 创建策略分析器实例
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 获取一些真实数据进行分析
    try:
        with analyzer.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT timestamp, open_price as open, close_price as close, 
                       high_price as high, low_price as low
                FROM crypto_prices 
                WHERE currency = 'DOGE' 
                AND timestamp >= '2025-06-02 00:00:00' 
                AND timestamp <= '2025-06-02 02:00:00'
                ORDER BY timestamp
                LIMIT 100
            """)
            results = cursor.fetchall()
            
            if not results:
                print("没有找到数据")
                return
                
            print(f"找到 {len(results)} 条数据")
            print()
            
            # 模拟新策略的逻辑（简化版本）
            minute_candles = []
            lookback_minutes_buy = 3
            lookback_minutes_sell = 1
            buy_rate = 0.5
            
            for i, row in enumerate(results):
                print(f"=== 第 {i+1} 条数据 ===")
                print(f"时间: {row['timestamp']}")
                print(f"开盘: {row['open']}, 收盘: {row['close']}, 最高: {row['high']}, 最低: {row['low']}")
                
                # 计算涨跌区间
                open_price = float(row['open'])
                close_price = float(row['close'])
                high_price = float(row['high'])
                low_price = float(row['low'])
                
                up_range = high_price - open_price      # 上涨区间
                down_range = open_price - low_price     # 下跌区间
                is_up_final = close_price > open_price  # 最终涨跌
                
                print(f"上涨区间: {up_range:.6f} (开盘到最高)")
                print(f"下跌区间: {down_range:.6f} (开盘到最低)")
                print(f"最终涨跌: {'涨' if is_up_final else '跌'}")
                print(f"主要趋势: {'上涨' if up_range > down_range else '下跌'}")
                
                # 添加到历史数据（模拟完整K线）
                candle_data = {
                    'timestamp': row['timestamp'],
                    'open': open_price,
                    'close': close_price,
                    'high': high_price,
                    'low': low_price,
                    'is_up': is_up_final,
                    'up_range': up_range,
                    'down_range': down_range
                }
                minute_candles.append(candle_data)
                
                # 保持最近20条数据
                if len(minute_candles) > 20:
                    minute_candles = minute_candles[-20:]
                
                # 如果有足够的历史数据，进行简化的连续涨跌分析
                if len(minute_candles) >= lookback_minutes_buy:
                    print(f"\n--- 简化连续涨跌分析 (lookback_buy={lookback_minutes_buy}) ---")

                    # 获取最近的lookback_minutes_buy次K线
                    recent_candles = minute_candles[-lookback_minutes_buy:]

                    # 检查历史是否连续下跌（基于收盘价相对开盘价）
                    all_down = all(not candle['is_up'] for candle in recent_candles)
                    # 检查历史是否连续上涨（基于收盘价相对开盘价）
                    all_up = all(candle['is_up'] for candle in recent_candles)

                    print(f"最近{lookback_minutes_buy}分钟涨跌:")
                    for j, candle in enumerate(recent_candles):
                        trend = "涨" if candle['is_up'] else "跌"
                        print(f"  {j+1}. {candle['timestamp']} - {trend} (开盘:{candle['open']:.6f}, 收盘:{candle['close']:.6f})")

                    print(f"连续下跌: {all_down}")
                    print(f"连续上涨: {all_up}")
                    print(f"当前K线: {'涨' if is_up_final else '跌'}")

                    # 如果历史连续下跌，且当前K线上涨，检查做多条件
                    if all_down and is_up_final:
                        print("✅ 满足做多条件（历史连续下跌 + 当前上涨）")
                        # 找到最低点
                        lowest_candle = min(recent_candles, key=lambda x: x['low'])
                        print(f"最低点: {lowest_candle['timestamp']}, 最低价: {lowest_candle['low']}, 最高价: {lowest_candle['high']}")

                        # 计算触发价格
                        if buy_rate == 1:
                            trigger_price = lowest_candle['high']
                        else:
                            price_range = lowest_candle['high'] - lowest_candle['low']
                            trigger_price = lowest_candle['low'] + (price_range * buy_rate)

                        print(f"做多触发价格: {trigger_price}")
                        print(f"当前最高价: {high_price}, 触发价格: {trigger_price}")
                        print(f"价格达到: {high_price >= trigger_price}")

                        if high_price >= trigger_price:
                            print("🚀 可以触发做多！")
                            print(f"开仓价格: {close_price}")

                            # 模拟持仓后的平仓逻辑
                            print(f"\n--- 模拟持仓后的平仓检查 ---")
                            # 检查后续几分钟是否会触发平仓
                            for k in range(i+1, min(i+5, len(results))):
                                next_row = results[k]
                                next_open = float(next_row['open'])
                                next_close = float(next_row['close'])
                                next_is_up = next_close > next_open

                                print(f"  {k-i}分钟后: {next_row['timestamp']} - {'涨' if next_is_up else '跌'}")

                                # 检查是否满足平仓条件（下跌）
                                if not next_is_up:
                                    print(f"  ✅ 触发平多条件，平仓价格: {next_close}")
                                    break
                        else:
                            print("❌ 不能触发做多")
                            print(f"  原因: 价格未达到触发点 ({high_price} < {trigger_price})")

                    # 如果历史连续上涨，且当前K线下跌，检查做空条件
                    elif all_up and not is_up_final:
                        print("✅ 满足做空条件（历史连续上涨 + 当前下跌）")
                        # 找到最高点
                        highest_candle = max(recent_candles, key=lambda x: x['high'])
                        print(f"最高点: {highest_candle['timestamp']}, 最高价: {highest_candle['high']}, 最低价: {highest_candle['low']}")

                        # 计算触发价格
                        if buy_rate == 1:
                            trigger_price = highest_candle['low']
                        else:
                            price_range = highest_candle['high'] - highest_candle['low']
                            trigger_price = highest_candle['high'] - (price_range * buy_rate)

                        print(f"做空触发价格: {trigger_price}")
                        print(f"当前最低价: {low_price}, 触发价格: {trigger_price}")
                        print(f"价格达到: {low_price <= trigger_price}")

                        if low_price <= trigger_price:
                            print("🚀 可以触发做空！")
                            print(f"开仓价格: {close_price}")
                        else:
                            print("❌ 不能触发做空")
                            print(f"  原因: 价格未达到触发点 ({low_price} > {trigger_price})")
                    else:
                        print("❌ 不满足连续涨跌条件")
                        if not all_down and not all_up:
                            print("  原因: 历史不是连续涨跌")
                        elif all_down and not is_up_final:
                            print("  原因: 历史连续下跌但当前也下跌")
                        elif all_up and is_up_final:
                            print("  原因: 历史连续上涨但当前也上涨")
                
                print()
                
                # 只分析前50条数据，避免输出过多
                if i >= 49:
                    break
                
    except Exception as e:
        print(f"调试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_new_strategy()
