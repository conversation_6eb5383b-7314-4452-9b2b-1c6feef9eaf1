#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用新的交易条件判断方法的示例

新方法的特点：
1. 使用分钟级K线数据进行连续涨跌分析
2. 只处理完整的分钟K线数据 (is_complete == 1)
3. 维护最近20条分钟K线数据的历史记录
4. 使用lookback-minutes-buy和lookback-minutes-sell参数进行反弹判断
5. 使用buy-rate参数判断是否跌穿/涨穿关键价位

策略逻辑：
开仓条件（需要同时满足）：
1. 连续涨跌模式：先跌lookback-minutes-buy次，再涨lookback-minutes-sell次
2. 价格突破条件：当前价格需要突破基于buy-rate计算的阈值

平仓条件：
- 连续lookback-minutes-sell次反向运动即可平仓（包含当前未完成的K线）

重要特性：
- 历史数据：只使用完整的分钟K线（is_complete == 1）
- 当前数据：即使未完成的K线（is_complete != 1）也参与连续涨跌判断

例如：lookback-minutes-buy=5, lookback-minutes-sell=2, buy-rate=1.0
- 做多：先跌5分钟，再涨2分钟，且价格涨到最低K线的最高值 → 触发做多
- 平多：连续跌2分钟（包含当前未完成K线） → 平多
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def run_new_condition_example():
    """运行新交易条件判断方法的示例"""
    
    print("=== 新交易条件判断方法示例 ===")
    print()
    print("策略说明：")
    print("- lookback-minutes-buy=5: 需要先连续下跌5分钟")
    print("- lookback-minutes-sell=2: 然后连续上涨2分钟")
    print("- buy-rate=1.0: 价格需要涨到最低K线的最高值才开仓")
    print("- 平仓条件：连续下跌2分钟即可平仓")
    print()
    
    # 创建策略分析器实例
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 获取最新的数据时间范围
    try:
        with analyzer.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT MAX(timestamp) as max_time 
                FROM crypto_prices 
                WHERE currency = 'DOGE'
            """)
            result = cursor.fetchone()
            
            if not result or not result['max_time']:
                print("没有找到DOGE的价格数据")
                return
                
            max_time = result['max_time']
            # 使用最新数据的前一天
            test_date = max_time.date() - timedelta(days=1)
            start_time = datetime.combine(test_date, datetime.min.time())
            end_time = datetime.combine(test_date, datetime.max.time())
            
    except Exception as e:
        print(f"获取数据时间范围时出错: {str(e)}")
        return
    
    # 策略参数 - 恢复到原始的严格条件
    params = {
        'sell_rate': 0.01,           # 卖出阈值1%（暂时未使用）
        'buy_rate': 1.0,             # 买入阈值：1.0表示涨到最低K线的最高值
        'min_trigger_rest': 5,       # 最小触发间隔5分钟
        'rest_minutes': 0,           # 交易间隔休息时间0分钟
        'lookback_minutes_buy': 5,   # 买入回看5分钟（先连续下跌5分钟）
        'lookback_minutes_sell': 2,  # 卖出回看2分钟（再连续上涨2分钟）
        'currency': 'DOGE',
        'is_live_trading': False,
        'reverse_buy': 0
    }
    
    print(f"测试时间: {start_time} 到 {end_time}")
    print(f"测试币种: {params['currency']}")
    print()
    
    try:
        # 使用新的交易条件判断方法
        result = analyzer.analyze_rebound_strategy(
            start_time=start_time,
            end_time=end_time,
            use_new_condition=True,  # 使用新方法
            **params
        )
        
        if result:
            print("=== 分析结果 ===")
            print(f"初始资金: ${result['initial_capital']:.4f}")
            print(f"最终资金: ${result['final_capital']:.4f}")
            print(f"总收益: ${result['profit_loss']:.4f}")
            print(f"收益率: {result['profit_percentage']:.2f}%")
            print(f"交易次数: {len(result['trades'])}")
            print(f"总手续费: ${result['total_fees']:.4f}")
            print()
            
            # 显示交易记录
            if result['trades']:
                print("=== 交易记录 ===")
                for i, trade in enumerate(result['trades']):
                    action = trade['action']
                    timestamp = trade['timestamp']
                    price = trade['price']
                    reason = trade.get('trigger_reason', trade.get('reason', 'N/A'))
                    profit = trade.get('profit', 0)
                    
                    print(f"{i+1:2d}. {timestamp} | {action:4s} | ${price:8.4f} | 收益: ${profit:7.4f} | {reason}")
                print()
            else:
                print("没有产生任何交易")
                print("可能原因：")
                print("1. 市场没有出现符合条件的连续涨跌模式")
                print("2. 价格变化幅度未达到buy_rate阈值")
                print("3. 触发条件过于严格")
                print()
                
            print("=== 策略说明 ===")
            print("新方法的优势：")
            print("1. 更精确的趋势识别：基于连续分钟K线的涨跌模式")
            print("2. 减少假信号：需要满足连续涨跌条件才触发")
            print("3. 更好的风控：使用关键价位进行止损")
            print("4. 适应性强：可以通过调整lookback参数适应不同市场")
            
        else:
            print("策略分析失败")
            
    except Exception as e:
        print(f"运行示例时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_new_condition_example()
