#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试精简后的原因长度
"""

def test_concise_reason_lengths():
    """测试精简后的原因长度"""
    
    print("🔍 测试精简后的原因长度")
    print("="*50)
    
    # 模拟精简后的原因格式
    test_cases = [
        {
            'name': '回调触发',
            'reason': '多开:回调15.5%,价0.14857,连2跌',
            'trigger_reason': '多开:回调15.5%,价0.14857,连2跌'
        },
        {
            'name': '反弹触发', 
            'reason': '空开:反弹12.3%,价0.14835,连3涨',
            'trigger_reason': '空开:反弹12.3%,价0.14835,连3涨'
        },
        {
            'name': '平多触发',
            'reason': '多平:连2次,率0.2,价0.14857,参考21:48:48(0.14828-0.14865-0.14824)',
            'trigger_reason': '多平:连2次,率0.2,价0.14857,参考21:48:48(0.14828-0.14865-0.14824)'
        },
        {
            'name': '平空触发',
            'reason': '空平:连2次,率0.2,价0.14835,参考21:47:00(0.14820-0.14860-0.14815)',
            'trigger_reason': '空平:连2次,率0.2,价0.14835,参考21:47:00(0.14820-0.14860-0.14815)'
        },
        {
            'name': '做多触发',
            'reason': '多开:连2跌2涨次,率0.2,价0.14824,参考21:46:00(0.14815-0.14860-0.14810)',
            'trigger_reason': '多开:连2跌2涨次,率0.2,价0.14824,参考21:46:00(0.14815-0.14860-0.14810)'
        },
        {
            'name': '做空触发',
            'reason': '空开:连2涨2跌次,率0.2,价0.14850,参考21:45:00(0.14825-0.14870-0.14820)',
            'trigger_reason': '空开:连2涨2跌次,率0.2,价0.14850,参考21:45:00(0.14825-0.14870-0.14820)'
        }
    ]
    
    print("📊 精简后的原因长度检查:")
    print("-"*50)
    
    all_valid = True
    
    for case in test_cases:
        reason_len = len(case['reason'])
        trigger_reason_len = len(case['trigger_reason'])
        
        reason_status = "✅" if reason_len <= 255 else "❌"
        trigger_status = "✅" if trigger_reason_len <= 255 else "❌"
        
        if reason_len > 255 or trigger_reason_len > 255:
            all_valid = False
        
        print(f"{case['name']}:")
        print(f"  reason: {reason_len} 字符 {reason_status}")
        print(f"  trigger_reason: {trigger_reason_len} 字符 {trigger_status}")
        print(f"  内容: {case['reason']}")
        print()
    
    print(f"🎯 总体结果: {'✅ 所有原因都符合数据库限制' if all_valid else '❌ 仍有超长原因'}")
    
    return all_valid

def test_original_vs_concise():
    """对比原始和精简格式"""
    
    print("📊 原始 vs 精简格式对比")
    print("="*50)
    
    comparisons = [
        {
            'name': '回调触发',
            'original': '15.5% 价格回调至触发价格0.14857且连续2分钟下跌, 反向买入为1, 开多',
            'concise': '多开:回调15.5%,价0.14857,连2跌'
        },
        {
            'name': '平多触发',
            'original': """平多触发
条件：连续2次下跌
倍率：sell_rate=0.2
参考K线：21:48:48(开:0.14828,高:0.14865,低:0.14824)
价格范围：最高价-最低价=0.14865-0.14824=0.00041
计算公式：最高价-价格范围×sell_rate
计算结果：0.14865-0.00041×0.2=0.14857
价格保护：触发价格0.14857>开盘价0.14828
实际触发：直接以开盘价0.14828触发""",
            'concise': '多平:连2次,率0.2,价0.14857,参考21:48:48(0.14828-0.14865-0.14824)'
        }
    ]
    
    total_original = 0
    total_concise = 0
    
    for comp in comparisons:
        original_len = len(comp['original'])
        concise_len = len(comp['concise'])
        compression_ratio = concise_len / original_len * 100
        
        total_original += original_len
        total_concise += concise_len
        
        print(f"{comp['name']}:")
        print(f"  原始: {original_len} 字符")
        print(f"  精简: {concise_len} 字符")
        print(f"  压缩比: {compression_ratio:.1f}%")
        print(f"  节省: {original_len - concise_len} 字符")
        print(f"  数据库兼容: {'✅' if concise_len <= 255 else '❌'}")
        print()
    
    overall_compression = total_concise / total_original * 100
    print(f"📈 总体压缩比: {overall_compression:.1f}%")
    print(f"📉 总共节省: {total_original - total_concise} 字符")

def simulate_database_save():
    """模拟数据库保存过程"""
    
    print("💾 模拟数据库保存过程")
    print("="*50)
    
    # 模拟交易记录
    trades = [
        {
            'timestamp': '2025-03-29T01:15:00',
            'action': 'BUY',
            'reason': '多开:回调15.5%,价0.14857,连2跌',
            'trigger_reason': '多开:回调15.5%,价0.14857,连2跌'
        },
        {
            'timestamp': '2025-03-29T01:17:00', 
            'action': 'SELL',
            'reason': '多平:连2次,率0.2,价0.14857,参考01:16:00(0.14828-0.14865-0.14824)',
            'trigger_reason': '多平:连2次,率0.2,价0.14857,参考01:16:00(0.14828-0.14865-0.14824)'
        },
        {
            'timestamp': '2025-03-29T01:18:00',
            'action': 'BUY', 
            'reason': '空开:反弹12.3%,价0.14835,连3涨',
            'trigger_reason': '空开:反弹12.3%,价0.14835,连3涨'
        }
    ]
    
    print("🔍 检查每条交易记录:")
    
    all_valid = True
    truncated_count = 0
    
    for i, trade in enumerate(trades, 1):
        print(f"\n交易 {i}: {trade['timestamp']} {trade['action']}")
        
        # 模拟截断逻辑
        original_reason = trade['reason']
        original_trigger_reason = trade['trigger_reason']
        
        reason = original_reason
        trigger_reason = original_trigger_reason
        
        # 检查reason字段
        if len(original_reason) > 250:
            reason = original_reason[:247] + '...'
            truncated_count += 1
            print(f"  ⚠️ reason过长已截断: {len(original_reason)} → {len(reason)}")
            all_valid = False
        else:
            print(f"  ✅ reason长度正常: {len(original_reason)} 字符")
        
        # 检查trigger_reason字段
        if len(original_trigger_reason) > 250:
            trigger_reason = original_trigger_reason[:247] + '...'
            truncated_count += 1
            print(f"  ⚠️ trigger_reason过长已截断: {len(original_trigger_reason)} → {len(trigger_reason)}")
            all_valid = False
        else:
            print(f"  ✅ trigger_reason长度正常: {len(original_trigger_reason)} 字符")
        
        print(f"  reason: {reason}")
        print(f"  trigger_reason: {trigger_reason}")
    
    print(f"\n🎯 模拟结果:")
    print(f"  总交易数: {len(trades)}")
    print(f"  截断次数: {truncated_count}")
    print(f"  成功率: {'✅ 100%' if all_valid else f'❌ {truncated_count}/{len(trades)*2} 字段需要截断'}")
    
    return all_valid

if __name__ == "__main__":
    print("🧪 精简原因长度测试")
    print("="*60)
    
    # 测试精简后的长度
    is_valid = test_concise_reason_lengths()
    
    print("\n" + "="*60 + "\n")
    
    # 对比原始和精简格式
    test_original_vs_concise()
    
    print("\n" + "="*60 + "\n")
    
    # 模拟数据库保存
    save_success = simulate_database_save()
    
    print("\n" + "="*60)
    print("✅ 测试完成！")
    
    if is_valid and save_success:
        print("\n🎉 精简成功！所有原因都符合数据库限制")
        print("💡 建议:")
        print("1. 重新运行策略分析")
        print("2. 应该不再出现字段过长错误")
        print("3. 原因保持简洁但包含关键信息")
    else:
        print("\n⚠️ 仍需要进一步优化")
        print("💡 可能的问题:")
        print("1. 某些原因格式仍然太长")
        print("2. 需要进一步精简词汇")
        print("3. 检查是否有遗漏的长原因生成代码")
