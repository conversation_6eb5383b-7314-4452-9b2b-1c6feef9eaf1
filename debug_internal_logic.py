#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
逐步调试analyze_correct_consecutive_moves函数的内部逻辑
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def debug_internal_logic():
    """逐步调试analyze_correct_consecutive_moves函数的内部逻辑"""
    
    print("=== 逐步调试analyze_correct_consecutive_moves函数的内部逻辑 ===")
    print()
    
    # 创建策略分析器实例
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 获取00:00:00到00:45:00的数据
    try:
        with analyzer.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT timestamp, open_price as open, close_price as close, 
                       high_price as high, low_price as low
                FROM crypto_prices 
                WHERE currency = 'DOGE' 
                AND timestamp >= '2025-06-06 00:00:00' 
                AND timestamp <= '2025-06-06 00:45:00'
                ORDER BY timestamp
            """)
            results = cursor.fetchall()
            
            if not results:
                print("没有找到数据")
                return
                
            print(f"找到 {len(results)} 条数据")
            print()
            
            # 模拟策略分析器的逻辑，构建minute_candles
            minute_candles = []
            
            for i, row in enumerate(results):
                timestamp = row['timestamp']
                open_price = float(row['open'])
                close_price = float(row['close'])
                high_price = float(row['high'])
                low_price = float(row['low'])
                
                # 模拟策略分析器中的涨跌判断逻辑
                if len(minute_candles) > 0:
                    prev_candle = minute_candles[-1]
                    # 上涨：当前最高值和最低值都比上一分钟高
                    if high_price > prev_candle['high'] and low_price > prev_candle['low']:
                        is_up = True
                    # 下跌：当前最高值和最低值都比上一分钟低
                    elif high_price < prev_candle['high'] and low_price < prev_candle['low']:
                        is_up = False
                    else:
                        # 横盘或混合情况，暂时按收盘价判断
                        is_up = close_price > open_price
                else:
                    # 第一条数据，按收盘价判断
                    is_up = close_price > open_price
                
                # 如果是00:42:00，手动执行analyze_correct_consecutive_moves函数的逻辑
                if str(timestamp) == '2025-06-06 00:42:00':
                    print(f"=== 手动执行analyze_correct_consecutive_moves函数逻辑 ===")
                    print(f"当前时间: {timestamp}")
                    print(f"历史K线数量: {len(minute_candles)}")
                    
                    # 构造current_row参数
                    current_row = {
                        'timestamp': str(timestamp),
                        'open': open_price,
                        'close': close_price,
                        'high': high_price,
                        'low': low_price
                    }
                    
                    # 参数设置
                    lookback_buy = 5
                    lookback_sell = 2
                    buy_rate = 1.0
                    
                    # 模拟analyze_correct_consecutive_moves函数的逻辑
                    total_needed = lookback_buy + lookback_sell - 1
                    print(f"需要的历史数据: {total_needed}条")
                    print(f"实际历史数据: {len(minute_candles)}条")
                    
                    if len(minute_candles) >= total_needed:
                        # 获取当前K线数据
                        current_open = float(current_row['open'])
                        current_close = float(current_row['close'])
                        current_high = float(current_row['high'])
                        current_low = float(current_row['low'])
                        
                        print(f"\n当前K线数据:")
                        print(f"  开盘: {current_open:.6f}")
                        print(f"  收盘: {current_close:.6f}")
                        print(f"  最高: {current_high:.6f}")
                        print(f"  最低: {current_low:.6f}")
                        
                        # 使用新的涨跌判断标准：基于最高值和最低值的比较
                        if len(minute_candles) > 0:
                            prev_candle = minute_candles[-1]
                            print(f"\n上一分钟K线:")
                            print(f"  最高: {prev_candle['high']:.6f}")
                            print(f"  最低: {prev_candle['low']:.6f}")
                            
                            # 上涨：当前最高值和最低值都比上一分钟高
                            if current_high > prev_candle['high'] and current_low > prev_candle['low']:
                                current_is_up = True
                                print(f"  涨跌判断: 涨 (最高值和最低值都比上一分钟高)")
                            # 下跌：当前最高值和最低值都比上一分钟低
                            elif current_high < prev_candle['high'] and current_low < prev_candle['low']:
                                current_is_up = False
                                print(f"  涨跌判断: 跌 (最高值和最低值都比上一分钟低)")
                            else:
                                # 横盘或混合情况，暂时按收盘价判断
                                current_is_up = current_close > current_open
                                print(f"  涨跌判断: {'涨' if current_is_up else '跌'} (横盘或混合情况，按收盘价判断)")
                        else:
                            # 第一条数据，按收盘价判断
                            current_is_up = current_close > current_open
                            print(f"  涨跌判断: {'涨' if current_is_up else '跌'} (第一条数据，按收盘价判断)")
                        
                        # 创建包含当前K线的完整序列
                        current_candle = {
                            'timestamp': current_row['timestamp'],
                            'open': current_open,
                            'close': current_close,
                            'high': current_high,
                            'low': current_low,
                            'is_up': current_is_up
                        }
                        
                        # 获取历史数据 + 当前数据
                        all_candles = minute_candles[-total_needed:] + [current_candle]
                        
                        print(f"\n完整序列（{len(all_candles)}条）:")
                        for j, candle in enumerate(all_candles):
                            trend = "涨" if candle['is_up'] else "跌"
                            print(f"  {j+1}. {candle['timestamp']} - {trend}")
                        
                        # 检查做多条件：先跌lookback_buy分钟，再涨lookback_sell分钟
                        down_period = all_candles[:lookback_buy]  # 前lookback_buy分钟
                        up_period = all_candles[lookback_buy:]    # 后lookback_sell分钟（包含当前）
                        
                        print(f"\n前{lookback_buy}分钟（下跌期）:")
                        for j, candle in enumerate(down_period):
                            trend = "涨" if candle['is_up'] else "跌"
                            status = "✅" if not candle['is_up'] else "❌"
                            print(f"  {status} {j+1}. {candle['timestamp']} - {trend}")
                        
                        print(f"\n后{lookback_sell}分钟（上涨期）:")
                        for j, candle in enumerate(up_period):
                            trend = "涨" if candle['is_up'] else "跌"
                            status = "✅" if candle['is_up'] else "❌"
                            print(f"  {status} {j+1}. {candle['timestamp']} - {trend}")
                        
                        # 检查前期是否连续下跌
                        all_down_in_down_period = all(not candle['is_up'] for candle in down_period)
                        # 检查后期是否连续上涨
                        all_up_in_up_period = all(candle['is_up'] for candle in up_period)
                        
                        print(f"\n前{lookback_buy}分钟连续下跌: {all_down_in_down_period}")
                        print(f"后{lookback_sell}分钟连续上涨: {all_up_in_up_period}")
                        print(f"满足做多条件: {all_down_in_down_period and all_up_in_up_period}")
                        
                        # 检查做空条件：先涨lookback_buy分钟，再跌lookback_sell分钟
                        up_period_first = all_candles[:lookback_buy]   # 前lookback_buy分钟
                        down_period_second = all_candles[lookback_buy:] # 后lookback_sell分钟（包含当前）
                        
                        print(f"\n=== 检查做空条件 ===")
                        print(f"前{lookback_buy}分钟（上涨期）:")
                        for j, candle in enumerate(up_period_first):
                            trend = "涨" if candle['is_up'] else "跌"
                            status = "✅" if candle['is_up'] else "❌"
                            print(f"  {status} {j+1}. {candle['timestamp']} - {trend}")
                        
                        print(f"\n后{lookback_sell}分钟（下跌期）:")
                        for j, candle in enumerate(down_period_second):
                            trend = "涨" if candle['is_up'] else "跌"
                            status = "✅" if not candle['is_up'] else "❌"
                            print(f"  {status} {j+1}. {candle['timestamp']} - {trend}")
                        
                        # 检查前期是否连续上涨
                        all_up_in_up_period_first = all(candle['is_up'] for candle in up_period_first)
                        # 检查后期是否连续下跌
                        all_down_in_down_period_second = all(not candle['is_up'] for candle in down_period_second)
                        
                        print(f"\n前{lookback_buy}分钟连续上涨: {all_up_in_up_period_first}")
                        print(f"后{lookback_sell}分钟连续下跌: {all_down_in_down_period_second}")
                        print(f"满足做空条件: {all_up_in_up_period_first and all_down_in_down_period_second}")
                        
                        # 最终结论
                        can_trigger_long = all_down_in_down_period and all_up_in_up_period
                        can_trigger_short = all_up_in_up_period_first and all_down_in_down_period_second
                        
                        print(f"\n=== 最终结论 ===")
                        print(f"可以做多: {can_trigger_long}")
                        print(f"可以做空: {can_trigger_short}")
                        
                        if can_trigger_long or can_trigger_short:
                            print(f"✅ 满足交易条件")
                        else:
                            print(f"❌ 不满足交易条件")
                    else:
                        print(f"历史数据不足，需要{total_needed}条，实际只有{len(minute_candles)}条")
                    
                    break  # 只分析00:42:00，然后退出
                
                # 添加到历史数据
                candle_data = {
                    'timestamp': timestamp,
                    'open': open_price,
                    'close': close_price,
                    'high': high_price,
                    'low': low_price,
                    'is_up': is_up
                }
                minute_candles.append(candle_data)
                    
    except Exception as e:
        print(f"调试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_internal_logic()
