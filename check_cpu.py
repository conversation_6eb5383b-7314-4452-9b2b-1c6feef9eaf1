#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import psutil
import time

def check_python_processes():
    """检查Python进程的CPU占用"""
    print("=== Python进程CPU占用检查 ===")
    
    python_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_info', 'cmdline']):
        try:
            if 'python' in proc.info['name'].lower():
                python_processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if not python_processes:
        print("没有找到Python进程")
        return
    
    # 等待一秒钟来获取准确的CPU使用率
    time.sleep(1)
    
    print(f"{'PID':<8} {'进程名':<15} {'CPU%':<8} {'内存(MB)':<10} {'命令行'}")
    print("-" * 80)
    
    for proc in python_processes:
        try:
            cpu_percent = proc.cpu_percent()
            memory_mb = proc.memory_info().rss / 1024 / 1024
            cmdline = ' '.join(proc.cmdline()[:3]) if proc.cmdline() else 'N/A'
            
            print(f"{proc.pid:<8} {proc.name():<15} {cpu_percent:<8.1f} {memory_mb:<10.1f} {cmdline}")
            
            # 如果CPU占用超过5%，显示详细信息
            if cpu_percent > 5:
                print(f"  ⚠️  高CPU占用进程详情:")
                print(f"     完整命令行: {' '.join(proc.cmdline()) if proc.cmdline() else 'N/A'}")
                print(f"     线程数: {proc.num_threads()}")
                print(f"     状态: {proc.status()}")
                
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            print(f"{proc.pid:<8} {'ERROR':<15} {'N/A':<8} {'N/A':<10} {str(e)}")

def check_system_cpu():
    """检查系统整体CPU占用"""
    print("\n=== 系统CPU占用情况 ===")
    cpu_percent = psutil.cpu_percent(interval=1)
    cpu_count = psutil.cpu_count()
    
    print(f"总CPU占用率: {cpu_percent}%")
    print(f"CPU核心数: {cpu_count}")
    
    # 检查各个CPU核心的占用率
    cpu_per_core = psutil.cpu_percent(interval=1, percpu=True)
    for i, core_percent in enumerate(cpu_per_core):
        print(f"CPU核心{i}: {core_percent}%")

if __name__ == "__main__":
    check_system_cpu()
    check_python_processes()
    
    print("\n=== 建议 ===")
    print("如果发现高CPU占用的Python进程:")
    print("1. 检查是否有无限循环")
    print("2. 检查是否有频繁的文件I/O操作")
    print("3. 检查是否有大量的数据库查询")
    print("4. 考虑添加适当的sleep()来降低CPU占用")
