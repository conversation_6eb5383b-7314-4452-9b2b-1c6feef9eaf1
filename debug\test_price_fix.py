#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试价格修复效果
验证交易价格不再超出K线范围
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + '/..')

from strategy_analyzer import StrategyAnalyzer

def test_price_validation():
    """测试价格验证函数"""

    print("🧪 测试价格验证函数")
    print("=" * 50)

    # 创建一个简单的数据库配置用于测试
    db_config = {
        'host': 'localhost',
        'user': 'test',
        'password': 'test',
        'database': 'test'
    }

    analyzer = StrategyAnalyzer(db_config)
    
    # 测试用例1：价格在范围内
    test_cases = [
        {
            'name': '价格在范围内',
            'price': 0.19800,
            'low': 0.19760,
            'high': 0.19846,
            'expected': 0.19800
        },
        {
            'name': '价格超出最高价',
            'price': 0.19996,  # 问题价格
            'low': 0.19760,
            'high': 0.19846,
            'expected': 0.19846  # 应该被调整为最高价
        },
        {
            'name': '价格低于最低价',
            'price': 0.19700,
            'low': 0.19760,
            'high': 0.19846,
            'expected': 0.19760  # 应该被调整为最低价
        },
        {
            'name': '价格等于最高价',
            'price': 0.19846,
            'low': 0.19760,
            'high': 0.19846,
            'expected': 0.19846
        },
        {
            'name': '价格等于最低价',
            'price': 0.19760,
            'low': 0.19760,
            'high': 0.19846,
            'expected': 0.19760
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {case['name']}")
        print(f"  输入价格: {case['price']}")
        print(f"  K线范围: [{case['low']}, {case['high']}]")
        
        result = analyzer.ensure_price_in_range(
            case['price'], case['low'], case['high'], f"测试价格{i}"
        )
        
        print(f"  输出价格: {result}")
        print(f"  预期价格: {case['expected']}")
        
        if abs(result - case['expected']) < 0.00001:
            print(f"  结果: ✅ 通过")
        else:
            print(f"  结果: ❌ 失败")
    
    return True

def test_problem_scenario():
    """测试问题场景：2025-03-01 02:19:00"""
    
    print(f"\n🎯 测试问题场景")
    print("=" * 50)
    
    # 模拟问题场景的数据
    problem_data = {
        'timestamp': '2025-03-01 02:19:00',
        'kline': {
            'open': 0.19844,
            'close': 0.19777,
            'high': 0.19846,
            'low': 0.19760
        },
        'original_trigger_price': 0.19996,  # 原始超出范围的价格
        'trade_type': 'close_long'
    }
    
    print(f"问题时间: {problem_data['timestamp']}")
    print(f"K线数据:")
    print(f"  开盘: {problem_data['kline']['open']}")
    print(f"  收盘: {problem_data['kline']['close']}")
    print(f"  最高: {problem_data['kline']['high']}")
    print(f"  最低: {problem_data['kline']['low']}")
    print(f"  范围: [{problem_data['kline']['low']}, {problem_data['kline']['high']}]")
    
    print(f"\n原始问题:")
    print(f"  触发价格: {problem_data['original_trigger_price']}")
    print(f"  超出最高价: {problem_data['original_trigger_price'] - problem_data['kline']['high']:.5f}")
    print(f"  超出百分比: {((problem_data['original_trigger_price'] - problem_data['kline']['high']) / problem_data['kline']['high']) * 100:.3f}%")
    
    # 使用修复后的函数
    db_config = {
        'host': 'localhost',
        'user': 'test',
        'password': 'test',
        'database': 'test'
    }
    analyzer = StrategyAnalyzer(db_config)
    fixed_price = analyzer.ensure_price_in_range(
        problem_data['original_trigger_price'],
        problem_data['kline']['low'],
        problem_data['kline']['high'],
        "平多触发价"
    )
    
    print(f"\n修复后结果:")
    print(f"  调整后价格: {fixed_price}")
    print(f"  是否在范围内: {'✅' if problem_data['kline']['low'] <= fixed_price <= problem_data['kline']['high'] else '❌'}")
    print(f"  价格调整: {problem_data['original_trigger_price']:.5f} → {fixed_price:.5f}")
    print(f"  调整幅度: {fixed_price - problem_data['original_trigger_price']:.5f}")
    
    return fixed_price

def simulate_strategy_execution():
    """模拟策略执行，验证所有返回的价格都在范围内"""
    
    print(f"\n🔄 模拟策略执行")
    print("=" * 50)
    
    # 模拟不同的交易场景
    scenarios = [
        {
            'name': '平多交易',
            'action': '平多',
            'original_price': 0.19996,
            'kline_range': [0.19760, 0.19846]
        },
        {
            'name': '平空交易',
            'action': '平空',
            'original_price': 0.18500,
            'kline_range': [0.18600, 0.18800]
        },
        {
            'name': '开多交易',
            'action': '开多',
            'original_price': 0.20100,
            'kline_range': [0.19900, 0.20000]
        },
        {
            'name': '开空交易',
            'action': '开空',
            'original_price': 0.19500,
            'kline_range': [0.19600, 0.19800]
        }
    ]
    
    db_config = {
        'host': 'localhost',
        'user': 'test',
        'password': 'test',
        'database': 'test'
    }
    analyzer = StrategyAnalyzer(db_config)

    for scenario in scenarios:
        print(f"\n场景: {scenario['name']}")
        print(f"  原始价格: {scenario['original_price']}")
        print(f"  K线范围: {scenario['kline_range']}")
        
        fixed_price = analyzer.ensure_price_in_range(
            scenario['original_price'],
            scenario['kline_range'][0],
            scenario['kline_range'][1],
            scenario['action'] + "触发价"
        )
        
        in_range = scenario['kline_range'][0] <= fixed_price <= scenario['kline_range'][1]
        print(f"  修复后价格: {fixed_price}")
        print(f"  范围检查: {'✅ 通过' if in_range else '❌ 失败'}")
        
        if not in_range:
            print(f"  ⚠️ 警告：修复后价格仍然超出范围！")

def generate_test_report():
    """生成测试报告"""
    
    print(f"\n📊 测试报告")
    print("=" * 50)
    
    print("✅ 已完成的修复:")
    print("1. 添加了 ensure_price_in_range 函数")
    print("2. 修改了 _check_close_long_position 返回语句")
    print("3. 修改了 _check_close_short_position 返回语句")
    print("4. 修改了 _check_long_condition 返回语句")
    print("5. 添加了详细的调试日志")
    
    print(f"\n🎯 预期效果:")
    print("- 所有交易价格都会被限制在当前K线范围内")
    print("- 超出范围的价格会被自动调整到边界值")
    print("- 调试日志会记录所有价格调整过程")
    print("- 不再出现交易价格超出K线范围的问题")
    
    print(f"\n🧪 建议测试:")
    print("1. 重新运行策略分析器")
    print("2. 检查问题时间点 2025-03-01 02:19:00")
    print("3. 验证所有交易价格都在K线范围内")
    print("4. 查看调试日志确认价格调整过程")

if __name__ == "__main__":
    print("🔧 策略价格修复测试")
    print("=" * 60)
    
    try:
        # 执行测试
        test_price_validation()
        test_problem_scenario()
        simulate_strategy_execution()
        generate_test_report()
        
        print(f"\n✅ 所有测试完成")
        print("策略价格修复已实施，交易价格将被限制在K线范围内")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
