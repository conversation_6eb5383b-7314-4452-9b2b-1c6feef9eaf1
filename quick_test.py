import pandas as pd
import os

# 找到最新的策略日志文件
log_dir = 'log/strategy'
log_files = []
for file in os.listdir(log_dir):
    if file.startswith('strategy_line_') and file.endswith('.log'):
        log_files.append(os.path.join(log_dir, file))

latest_log = max(log_files, key=os.path.getmtime)
print(f'最新日志文件: {latest_log}')

# 读取文件
df = pd.read_csv(latest_log, sep='\t', encoding='utf-8', low_memory=False)
print(f'总行数: {len(df)}')

# 查看有交易的记录
# 先转换数据类型
df['实际触发价'] = pd.to_numeric(df['实际触发价'], errors='coerce')
trades = df[df['实际触发价'] > 0]
print(f'交易记录数: {len(trades)}')

if len(trades) > 0:
    print()
    print('=== 边界检查测试 ===')
    
    for idx, trade in trades.tail(5).iterrows():  # 只看最后5笔交易
        timestamp = trade['时间']
        low_price = float(trade['最低价'])
        high_price = float(trade['最高价'])
        trigger_price = float(trade['触发价'])
        actual_trigger_price = float(trade['实际触发价'])
        protection_price = float(trade['保护价'])
        
        print(f'交易: {timestamp}')
        print(f'  K线范围: [{low_price:.5f}, {high_price:.5f}]')
        print(f'  触发价: {trigger_price:.5f}')
        print(f'  实际触发价: {actual_trigger_price:.5f}')
        print(f'  保护价: {protection_price:.5f}')
        
        # 边界检查
        actual_ok = low_price <= actual_trigger_price <= high_price
        protection_ok = low_price <= protection_price <= high_price
        
        actual_status = '✅' if actual_ok else '❌'
        protection_status = '✅' if protection_ok else '❌'
        
        print(f'  实际触发价在范围内: {actual_ok} {actual_status}')
        print(f'  保护价在范围内: {protection_ok} {protection_status}')
        print()
