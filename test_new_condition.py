#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新的交易条件判断方法
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def test_new_condition():
    """测试新的交易条件判断方法"""

    print("开始测试新的交易条件判断方法...")

    # 创建策略分析器实例
    analyzer = StrategyAnalyzer(DB_CONFIG)

    # 先检查数据库中有什么数据
    print("检查数据库中的数据...")
    try:
        with analyzer.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT currency,
                       MIN(timestamp) as min_time,
                       MAX(timestamp) as max_time,
                       COUNT(*) as count
                FROM crypto_prices
                GROUP BY currency
                ORDER BY max_time DESC
                LIMIT 5
            """)
            results = cursor.fetchall()

            if results:
                print("数据库中的数据:")
                for row in results:
                    print(f"  {row['currency']}: {row['min_time']} 到 {row['max_time']} ({row['count']} 条记录)")

                # 使用最新的数据
                latest_data = results[0]
                currency = latest_data['currency']
                max_time = latest_data['max_time']

                # 使用最新数据的前一天作为测试时间
                test_date = max_time.date() - timedelta(days=1)
                start_time = datetime.combine(test_date, datetime.min.time())
                end_time = datetime.combine(test_date, datetime.max.time())

                print(f"使用 {currency} 的数据，测试时间: {start_time} 到 {end_time}")
            else:
                print("数据库中没有找到任何价格数据")
                return

    except Exception as e:
        print(f"检查数据库时出错: {str(e)}")
        return
    
    # 测试参数
    test_params = {
        'sell_rate': 0.01,
        'buy_rate': 0.01,
        'min_trigger_rest': 5,
        'rest_minutes': 0,
        'lookback_minutes_buy': 5,
        'lookback_minutes_sell': 2,
        'currency': currency,
        'is_live_trading': False,
        'reverse_buy': 0
    }
    
    print(f"测试时间范围: {start_time} 到 {end_time}")
    print(f"测试参数: {test_params}")
    
    try:
        # 测试旧方法
        print("\n=== 测试旧的交易条件判断方法 ===")
        result_old = analyzer.analyze_rebound_strategy(
            start_time=start_time,
            end_time=end_time,
            use_new_condition=False,
            **test_params
        )
        
        if result_old:
            print(f"旧方法结果:")
            print(f"  初始资金: ${result_old['initial_capital']:.4f}")
            print(f"  最终资金: ${result_old['final_capital']:.4f}")
            print(f"  总收益: ${result_old['profit_loss']:.4f}")
            print(f"  收益率: {result_old['profit_percentage']:.2f}%")
            print(f"  交易次数: {len(result_old['trades'])}")
            print(f"  总手续费: ${result_old['total_fees']:.4f}")
        else:
            print("旧方法分析失败")
        
        # 测试新方法
        print("\n=== 测试新的交易条件判断方法 ===")
        result_new = analyzer.analyze_rebound_strategy(
            start_time=start_time,
            end_time=end_time,
            use_new_condition=True,
            **test_params
        )
        
        if result_new:
            print(f"新方法结果:")
            print(f"  初始资金: ${result_new['initial_capital']:.4f}")
            print(f"  最终资金: ${result_new['final_capital']:.4f}")
            print(f"  总收益: ${result_new['profit_loss']:.4f}")
            print(f"  收益率: {result_new['profit_percentage']:.2f}%")
            print(f"  交易次数: {len(result_new['trades'])}")
            print(f"  总手续费: ${result_new['total_fees']:.4f}")
            
            # 显示一些交易记录
            if result_new['trades']:
                print(f"\n前5条交易记录:")
                for i, trade in enumerate(result_new['trades'][:5]):
                    print(f"  {i+1}. {trade['timestamp']} - {trade['action']} - 价格: ${trade['price']:.4f} - 原因: {trade.get('trigger_reason', 'N/A')}")
            else:
                print("  新方法没有产生任何交易")
        else:
            print("新方法分析失败")
        
        # 比较结果
        if result_old and result_new:
            print("\n=== 结果比较 ===")
            print(f"收益差异: ${result_new['profit_loss'] - result_old['profit_loss']:.4f}")
            print(f"收益率差异: {result_new['profit_percentage'] - result_old['profit_percentage']:.2f}%")
            print(f"交易次数差异: {len(result_new['trades']) - len(result_old['trades'])}")
            
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_new_condition()
