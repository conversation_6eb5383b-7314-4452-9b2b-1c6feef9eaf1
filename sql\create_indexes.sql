-- 为strategy_results表创建索引
-- 优化按final_capital排序的查询
CREATE INDEX idx_strategy_results_final_capital ON strategy_results (final_capital DESC);

-- 为了优化其他常用查询，添加组合索引
CREATE INDEX idx_strategy_results_combined ON strategy_results (
    start_time, 
    end_time, 
    rebound_buy, 
    profit_take, 
    rest_minutes
);

-- 为crypto_prices表创建索引
-- 优化按currency和timestamp查询
CREATE INDEX idx_crypto_prices_currency_timestamp ON crypto_prices (currency, timestamp);

-- 为strategy_trades表创建索引
-- 优化按timestamp查询
CREATE INDEX idx_strategy_trades_timestamp ON strategy_trades (timestamp);

-- 添加id索引（如果还没有主键的话）
ALTER TABLE strategy_trades ADD PRIMARY KEY (id);
