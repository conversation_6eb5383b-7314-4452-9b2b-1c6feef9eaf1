import pandas as pd
from datetime import datetime, timedelta

# 检查特定时间段的交易记录
log_file = 'log/strategy/strategy_line_892810_live_0.log'

print('=== 调试 2025-06-01 22:02:00 前后一小时的交易记录 ===')
print()

try:
    # 读取文件
    df = pd.read_csv(log_file, sep='\t', encoding='utf-8', low_memory=False)

    # 跳过标题行
    if df.iloc[0]['时间'] == 'timestamp':
        df = df.iloc[1:].reset_index(drop=True)

    # 转换时间列
    df['时间'] = pd.to_datetime(df['时间'])
    
    # 设置目标时间
    target_time = datetime(2025, 6, 1, 22, 2, 0)
    start_time = target_time - timedelta(hours=1)  # 21:02:00
    end_time = target_time + timedelta(hours=1)    # 23:02:00
    
    print(f'分析时间范围: {start_time} 到 {end_time}')
    print()
    
    # 筛选时间范围内的数据
    time_filtered = df[(df['时间'] >= start_time) & (df['时间'] <= end_time)].copy()
    print(f'时间范围内总K线数: {len(time_filtered)}')
    
    # 转换数据类型
    numeric_columns = ['开盘价', '收盘价', '最高价', '最低价', '触发价', '实际触发价', '保护价', '当前仓位']
    for col in numeric_columns:
        time_filtered[col] = pd.to_numeric(time_filtered[col], errors='coerce')
    
    # 筛选有交易的记录
    trades = time_filtered[time_filtered['实际触发价'] > 0].copy()
    print(f'时间范围内交易记录数: {len(trades)}')
    print()
    
    if len(trades) == 0:
        print('该时间段内没有交易记录')
        
        # 显示目标时间前后的K线数据
        print('显示目标时间前后的K线数据:')
        target_area = time_filtered[(time_filtered['时间'] >= target_time - timedelta(minutes=10)) & 
                                   (time_filtered['时间'] <= target_time + timedelta(minutes=10))]
        
        for idx, row in target_area.iterrows():
            timestamp = row['时间']
            open_price = row['开盘价']
            close_price = row['收盘价']
            high_price = row['最高价']
            low_price = row['最低价']
            position = row['当前仓位']
            
            print(f'  {timestamp}: 开{open_price:.5f} 收{close_price:.5f} 高{high_price:.5f} 低{low_price:.5f} 仓位{position:.2f}')
    else:
        print('=== 交易记录详细分析 ===')
        print()
        
        for i, (idx, trade) in enumerate(trades.iterrows(), 1):
            timestamp = trade['时间']
            open_price = trade['开盘价']
            close_price = trade['收盘价']
            high_price = trade['最高价']
            low_price = trade['最低价']
            trigger_price = trade['触发价']
            actual_trigger_price = trade['实际触发价']
            protection_price = trade['保护价']
            position = trade['当前仓位']
            
            print(f'📊 交易 {i}: {timestamp}')
            print(f'   K线数据:')
            print(f'     开盘价: {open_price:.5f}')
            print(f'     收盘价: {close_price:.5f}')
            print(f'     最高价: {high_price:.5f}')
            print(f'     最低价: {low_price:.5f}')
            print(f'     K线范围: [{low_price:.5f}, {high_price:.5f}]')
            print(f'   交易数据:')
            print(f'     触发价: {trigger_price:.5f}')
            print(f'     实际触发价: {actual_trigger_price:.5f}')
            print(f'     保护价: {protection_price:.5f}')
            print(f'     当前仓位: {position:.2f}')
            
            # 边界检查
            actual_in_range = low_price <= actual_trigger_price <= high_price
            protection_in_range = low_price <= protection_price <= high_price
            trigger_in_range = low_price <= trigger_price <= high_price
            
            print(f'   边界检查:')
            print(f'     实际触发价在范围内: {actual_in_range} {"✅" if actual_in_range else "❌"}')
            print(f'     保护价在范围内: {protection_in_range} {"✅" if protection_in_range else "❌"}')
            print(f'     触发价在范围内: {trigger_in_range} {"✅" if trigger_in_range else "⚠️ (历史价格)"}')
            
            # 分析交易类型
            if position > 0:
                trade_type = "做多开仓" if i == 1 or trades.iloc[i-2]['当前仓位'] == 0 else "加仓"
            elif position < 0:
                trade_type = "做空开仓" if i == 1 or trades.iloc[i-2]['当前仓位'] == 0 else "加仓"
            else:
                trade_type = "平仓"
            
            print(f'   交易类型: {trade_type}')
            
            # 价格分析
            if actual_trigger_price == protection_price:
                print(f'   价格分析: 使用保护价成交')
            elif actual_trigger_price == trigger_price:
                print(f'   价格分析: 使用原始触发价成交')
            else:
                print(f'   价格分析: 使用调整后价格成交')
            
            print()
        
        # 显示交易前后的K线上下文
        print('=== 交易上下文分析 ===')
        print()
        
        for i, (idx, trade) in enumerate(trades.iterrows(), 1):
            timestamp = trade['时间']
            print(f'🔍 交易 {i} ({timestamp}) 前后K线:')
            
            # 获取前后5分钟的K线
            context_start = timestamp - timedelta(minutes=5)
            context_end = timestamp + timedelta(minutes=5)
            context_data = time_filtered[(time_filtered['时间'] >= context_start) & 
                                       (time_filtered['时间'] <= context_end)]
            
            for _, row in context_data.iterrows():
                row_time = row['时间']
                open_price = row['开盘价']
                close_price = row['收盘价']
                high_price = row['最高价']
                low_price = row['最低价']
                position = row['当前仓位']
                
                # 标记交易时间
                marker = " 🎯" if row_time == timestamp else ""
                
                # 判断涨跌
                trend = "↑" if close_price > open_price else "↓" if close_price < open_price else "→"
                
                print(f'     {row_time}: 开{open_price:.5f} 收{close_price:.5f} 高{high_price:.5f} 低{low_price:.5f} {trend} 仓位{position:.2f}{marker}')
            
            print()

except Exception as e:
    print(f'错误: {e}')
    import traceback
    traceback.print_exc()
