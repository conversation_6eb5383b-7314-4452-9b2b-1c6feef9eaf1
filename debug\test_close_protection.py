#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试平仓保护逻辑修正
验证平多和平空的保护机制是否正确体现趋势方向
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def test_close_protection():
    """测试平仓保护逻辑"""
    print("🧪 测试平仓保护逻辑修正")
    print("=" * 50)
    
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 测试参数
    test_params = {
        'current_position': -404.72,  # 空仓，测试平空
        'last_close_time': None,
        'rest_minutes': 0,
        'lookback_minutes_buy': 2,
        'lookback_minutes_sell': 2,
        'buy_rate': 0.2,
        'sell_rate': 0.2,
        'price_adjust_rate': 0.001,  # 0.1%
        'minute_candles': []
    }
    
    # 模拟K线数据 - 平空场景：先跌后涨，确保连续上涨
    candles_data = [
        # 下跌期
        {'timestamp': '2025-03-03T07:56:00+00:00', 'open': 0.23950, 'close': 0.23930, 'high': 0.23960, 'low': 0.23920, 'is_up': False},
        {'timestamp': '2025-03-03T07:57:00+00:00', 'open': 0.23930, 'close': 0.23910, 'high': 0.23940, 'low': 0.23900, 'is_up': False},
        # 上涨期 - 确保连续上涨
        {'timestamp': '2025-03-03T07:58:00+00:00', 'open': 0.23910, 'close': 0.23930, 'high': 0.23940, 'low': 0.23905, 'is_up': True},
        {'timestamp': '2025-03-03T07:59:00+00:00', 'open': 0.23930, 'close': 0.23950, 'high': 0.23960, 'low': 0.23925, 'is_up': True},
        # 当前K线 - 继续上涨，触发平空
        {'timestamp': '2025-03-03T08:00:00+00:00', 'open': 0.23931, 'close': 0.23950, 'high': 0.24060, 'low': 0.23908, 'is_up': True}
    ]
    
    test_params['minute_candles'] = candles_data
    
    # 构造当前K线数据
    current_row = {
        'timestamp': '2025-03-03T08:00:00+00:00',
        'open': 0.23931,
        'close': 0.23950,
        'high': 0.24060,
        'low': 0.23908,
        'is_complete': 1
    }
    
    print("📊 测试场景：平空保护机制")
    print(f"当前仓位：{test_params['current_position']:.2f} (空仓)")
    print(f"当前K线：开盘{current_row['open']:.5f} 最高{current_row['high']:.5f} 最低{current_row['low']:.5f}")
    print(f"价格调整比例：{test_params['price_adjust_rate']*100:.1f}%")
    print()
    
    # 测试交易条件
    result = analyzer.trade_condition_new(current_row, test_params)
    
    if result and result['action']:
        print(f"✅ 交易信号：{result['action']}")
        print(f"📝 交易原因：{result['reason']}")
        print(f"💰 触发价格：{result['trigger_price']:.5f}")
        
        if 'actual_trigger_price' in result:
            print(f"💰 实际触发价：{result['actual_trigger_price']:.5f}")
            
            # 验证保护逻辑
            expected_protection_price = current_row['open'] * (1 + test_params['price_adjust_rate'])
            actual_protection_price = result['actual_trigger_price']
            
            print(f"🔍 保护价格验证：")
            print(f"   期望保护价：{expected_protection_price:.5f} (开盘价+{test_params['price_adjust_rate']*100:.1f}%)")
            print(f"   实际保护价：{actual_protection_price:.5f}")
            
            if abs(actual_protection_price - expected_protection_price) < 0.00001:
                print("   ✅ 保护价格正确：体现上涨趋势")
            else:
                print("   ❌ 保护价格错误")
                
            # 验证价格在K线范围内
            if current_row['low'] <= actual_protection_price <= current_row['high']:
                print("   ✅ 保护价格在K线范围内")
            else:
                print("   ❌ 保护价格超出K线范围")
        
    else:
        print("❌ 未产生交易信号")
        if result:
            print(f"📝 原因：{result['reason']}")
    
    print()
    print("=" * 50)
    
    # 测试平多场景
    print("📊 测试场景：平多保护机制")
    
    # 修改为多仓
    test_params['current_position'] = 404.72  # 多仓，测试平多
    
    # 模拟K线数据 - 平多场景：先涨后跌
    candles_data_long = [
        # 上涨期
        {'timestamp': '2025-03-03T07:56:00+00:00', 'open': 0.23900, 'close': 0.23920, 'high': 0.23930, 'low': 0.23890, 'is_up': True},
        {'timestamp': '2025-03-03T07:57:00+00:00', 'open': 0.23920, 'close': 0.23940, 'high': 0.23950, 'low': 0.23910, 'is_up': True},
        # 下跌期
        {'timestamp': '2025-03-03T07:58:00+00:00', 'open': 0.23940, 'close': 0.23920, 'high': 0.23950, 'low': 0.23910, 'is_up': False},
        {'timestamp': '2025-03-03T07:59:00+00:00', 'open': 0.23920, 'close': 0.23900, 'high': 0.23930, 'low': 0.23890, 'is_up': False},
        # 当前K线 - 继续下跌，触发平多
        {'timestamp': '2025-03-03T08:00:00+00:00', 'open': 0.23931, 'close': 0.23880, 'high': 0.23940, 'low': 0.23850, 'is_up': False}
    ]
    
    test_params['minute_candles'] = candles_data_long
    
    # 构造当前K线数据（平多场景）
    current_row_long = {
        'timestamp': '2025-03-03T08:00:00+00:00',
        'open': 0.23931,
        'close': 0.23880,
        'high': 0.23940,
        'low': 0.23850,
        'is_complete': 1
    }
    
    print(f"当前仓位：{test_params['current_position']:.2f} (多仓)")
    print(f"当前K线：开盘{current_row_long['open']:.5f} 最高{current_row_long['high']:.5f} 最低{current_row_long['low']:.5f}")
    print()
    
    # 测试交易条件
    result_long = analyzer.trade_condition_new(current_row_long, test_params)
    
    if result_long and result_long['action']:
        print(f"✅ 交易信号：{result_long['action']}")
        print(f"📝 交易原因：{result_long['reason']}")
        print(f"💰 触发价格：{result_long['trigger_price']:.5f}")
        
        if 'actual_trigger_price' in result_long:
            print(f"💰 实际触发价：{result_long['actual_trigger_price']:.5f}")
            
            # 验证保护逻辑
            expected_protection_price = current_row_long['open'] * (1 - test_params['price_adjust_rate'])
            actual_protection_price = result_long['actual_trigger_price']
            
            print(f"🔍 保护价格验证：")
            print(f"   期望保护价：{expected_protection_price:.5f} (开盘价-{test_params['price_adjust_rate']*100:.1f}%)")
            print(f"   实际保护价：{actual_protection_price:.5f}")
            
            if abs(actual_protection_price - expected_protection_price) < 0.00001:
                print("   ✅ 保护价格正确：体现下跌趋势")
            else:
                print("   ❌ 保护价格错误")
                
            # 验证价格在K线范围内
            if current_row_long['low'] <= actual_protection_price <= current_row_long['high']:
                print("   ✅ 保护价格在K线范围内")
            else:
                print("   ❌ 保护价格超出K线范围")
        
    else:
        print("❌ 未产生交易信号")
        if result_long:
            print(f"📝 原因：{result_long['reason']}")
    
    print()
    print("🎯 测试总结：")
    print("- 平空保护：开盘价 + 0.1% (体现上涨趋势)")
    print("- 平多保护：开盘价 - 0.1% (体现下跌趋势)")
    print("- 确保保护价格在当前K线范围内")
    print("✅ 平仓保护逻辑修正完成")

if __name__ == "__main__":
    test_close_protection()
