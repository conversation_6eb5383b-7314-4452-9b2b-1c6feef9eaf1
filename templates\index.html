<!DOCTYPE html>
<html>
<head>
    <title>标签</title>
    <script src="/static/echarts.min.js"></script>
    <script src="/static/plotly.min.js"></script>
    <script src="/static/js/strategy_log_parser.js"></script>
    <link href="/static/bootstrap.min.css" rel="stylesheet">
    <style>
        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100vh;
            overflow: hidden;
        }

        body {
            background-color: #f8f9fa;
        }

        .container {
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            width: 100%;
            height: 100vh;
            max-width: 100% !important;
            padding: 0 !important;
            margin: 0 !important;
            display: flex;
            flex-direction: column;
            overflow-x: auto;
            overflow-y: hidden;
        }

        .controls {
            width: 100%;
            min-width: 400px;
            padding: 10px;
            background: #f5f5f5;
            border-bottom: 1px solid #ddd;
            flex-shrink: 0;
            z-index: 1;
        }

        #chart {
            position: relative;
            flex: 1;
            width: 100%;
            min-width: 400px;
            height: calc(100vh - 60px);
            min-height: 0;
            margin: 0 !important;
            padding: 0 !important;
        }

        .control-group {
            display: inline-block;
            margin-right: 20px;
            vertical-align: middle;
        }

        .control-group label {
            margin-right: 5px;
        }

        .interval-buttons {
            display: inline-block;
            vertical-align: middle;
        }

        .interval-btn {
            padding: 5px 10px;
            margin: 0 2px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .interval-btn.active {
            background: #007bff;
            color: white;
            border-color: #0056b3;
        }

        /* 图表控制按钮样式 */
        .control-group button {
            padding: 5px 8px;
            margin: 0 2px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            font-size: 12px;
            min-width: 30px;
        }

        .control-group button:hover {
            background: #f0f0f0;
            border-color: #007bff;
        }

        .control-group button:active {
            background: #007bff;
            color: white;
        }

        .control-group button[title] {
            position: relative;
        }

        select, input {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 5px;
        }

        .strategy-selector {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .strategy-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .form-control {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
        }

        .row {
            margin-bottom: 15px;
        }

        .strategy-info ul {
            padding-left: 20px;
        }

        .strategy-info li {
            margin-bottom: 5px;
        }

        #toastContainer {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            display: flex;
            flex-direction: column;
            gap: 10px;  /* 消息之间的间距 */
        }

        .toast-message {
            padding: 10px 20px;
            background-color: #333;
            color: white;
            border-radius: 4px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease-in-out;
            min-width: 200px;
            max-width: 400px;
            word-wrap: break-word;
        }

        .toast-message.show {
            opacity: 1;
            transform: translateX(0);
        }

        .toast-message.success {
            background-color: #4caf50;
        }

        .toast-message.error {
            background-color: #f44336;
        }

        .toast-message.info {
            background-color: #2196F3;
        }
    </style>
    <script>
        // 文件变化监听已禁用以减少服务器负载
        console.log('文件变化监听已禁用');
    </script>
</head>
<body>
    <div class="container">
        <div class="controls">

            <div class="control-group">
                <label for="strategySelect" style="display: inline-block;">策略选择:</label>
                <select id="strategySelect" class="form-control" style="display: inline-block;">
                    <!-- 动态填充策略选项 -->
                </select>
            </div>
            <div class="control-group">
                <label for="strategyIdInput" style="display: inline-block;">策略ID:</label>
                <input type="text" id="strategyIdInput" placeholder="请输入策略ID" style="display: inline-block;" onkeypress="if(event.key === 'Enter') { fetchData(); }">
            </div>

            <div class="control-group">
                <label>
                    <input type="checkbox" id="loadTradeDataToggle" checked> 智能显示交易箭头
                </label>
            </div>
            <div class="control-group">
                <label>
                    <input type="checkbox" id="loadOrderDataToggle" checked> 加载委托数据
                </label>
            </div>

            <div class="control-group">
                <span id="tradeDataStatus" style="font-size: 12px; color: #666; margin-left: 10px;">
                    📊 交易箭头: 自动控制
                </span>
            </div>

            <button onclick="fetchData()">刷新数据</button>

            <!-- 图表控制按钮 -->
            <div class="control-group">
                <label>图表控制:</label>
                <button onclick="zoomIn()" title="放大">🔍+</button>
                <button onclick="zoomOut()" title="缩小">🔍-</button>
                <button onclick="panLeft()" title="向左移动">⬅️</button>
                <button onclick="panRight()" title="向右移动">➡️</button>
                <button onclick="resetZoom()" title="重置视图">🏠</button>
                <button onclick="fitToData()" title="适应数据">📊</button>
                <button onclick="jumpToPrevTrade()" title="跳到上个交易">⏮️</button>
                <button onclick="jumpToNextTrade()" title="跳到下个交易">⏭️</button>
                <button onclick="jumpToStart()" title="跳到开始">⏪</button>
                <button onclick="jumpToEnd()" title="跳到结尾">⏩</button>
                <button onclick="showTimeSelector()" title="选择时间">📅</button>
            </div>

            <!-- 时间范围快捷按钮 -->
            <div class="control-group">
                <label>时间范围:</label>
                <button onclick="showTimeRange('15m')" title="显示15分钟">15M</button>
                <button onclick="showTimeRange('30m')" title="显示30分钟">30M</button>
                <button onclick="showTimeRange('1h')" title="显示1小时 (Ctrl+1)">1H</button>
                <button onclick="showTimeRange('4h')" title="显示4小时 (Ctrl+4)">4H</button>
                <button onclick="showTimeRange('12h')" title="显示12小时">12H</button>
                <button onclick="showTimeRange('1d')" title="显示1天">1D</button>
                <button onclick="showTimeRange('3d')" title="显示3天">3D</button>
                <button onclick="showTimeRange('all')" title="显示全部 (Ctrl+0)">全部</button>
            </div>

            <!-- 快捷键帮助 -->
            <div class="control-group">
                <button onclick="toggleKeyboardHelp()" title="显示/隐藏快捷键帮助">❓</button>
                <button onclick="toggleBrushHelp()" title="显示/隐藏矩形选择帮助">📐</button>
                <div id="keyboardHelp" style="display: none; position: absolute; background: white; border: 1px solid #ccc; padding: 10px; border-radius: 4px; z-index: 1000; font-size: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <strong>键盘快捷键:</strong><br>
                    ← → : 按当前范围移动（如1小时视图则移动1小时）<br>
                    + - : 放大缩小<br>
                    Home: 重置视图<br>
                    End: 适应数据<br>
                    <strong>时间范围:</strong><br>
                    15M/30M: 15/30分钟视图<br>
                    Ctrl+1: 1小时视图<br>
                    Ctrl+4: 4小时视图<br>
                    Ctrl+0: 全部数据<br>
                    鼠标滚轮: 缩放<br>
                    拖拽: 平移
                </div>
                <div id="brushHelp" style="display: none; position: absolute; background: white; border: 1px solid #ccc; padding: 10px; border-radius: 4px; z-index: 1000; font-size: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); margin-top: 30px;">
                    <strong>矩形选择分析:</strong><br>
                    1. 点击右侧工具栏的矩形选择工具<br>
                    2. 在图表上拖拽选择矩形区域<br>
                    3. 自动显示区域内的数据分析<br>
                    <br>
                    <strong>分析内容:</strong><br>
                    • K线统计: 最高/最低价、涨跌幅<br>
                    • 时间跨度: 持续时间分析<br>
                    • 矩形区域: Y轴价格范围分析<br>
                    • 成交量: 区域内总成交量
                </div>
            </div>
        </div>
        <div id="chart" class="chart-container" style="position: relative; width: 100%; height: 100%; padding: 0px; margin: 0px; border-width: 0px; cursor: pointer; pointer-events: auto;"></div>

        <!-- 策略信息显示 -->
        <div class="strategy-info" id="strategyInfo" style="display: none; padding: 10px; margin-bottom: 10px;">
            <div class="row" style="display: flex; flex-wrap: wrap; gap: 10px;">
                <div style="flex: 1; min-width: 200px;">
                    <p><strong>策略ID:</strong> <span id="strategyId"></span></p>
                    <p><strong>策略名称:</strong> <span id="strategyName"></span></p>
                    <p><strong>策略类型:</strong> <span id="strategyType"></span></p>
                    <p><strong>交易对:</strong> <span id="currency"></span></p>
                    <p><strong>收益率:</strong> <span id="roi"></span>%</p>
                </div>
                <div style="flex: 1; min-width: 200px;">
                    <p><strong>成功率:</strong> <span id="successRate"></span>%</p>
                    <p><strong>总交易次数:</strong> <span id="totalTrades"></span></p>
                    <p><strong>成功交易次数:</strong> <span id="successfulTrades"></span></p>
                    <p><strong>初始资金:</strong> <span id="initialCapital"></span> USDT</p>
                    <p><strong>最终资金:</strong> <span id="finalCapital"></span> USDT</p>
                </div>
                <div style="flex: 1; min-width: 200px;">
                    <p><strong>总盈利:</strong> <span id="totalProfit"></span> USDT</p>
                    <p><strong>总手续费:</strong> <span id="totalFees"></span> USDT</p>
                    <p><strong>买入阈值:</strong> <span id="buyRate"></span>%</p>
                    <p><strong>卖出阈值:</strong> <span id="sellRate"></span>%</p>
                </div>
                <div style="flex: 1; min-width: 200px;">
                    <p><strong>休息时间:</strong> <span id="restMinutes"></span>分钟</p>
                    <p><strong>最小触发休息:</strong> <span id="minTriggerRest"></span>分钟</p>
                    <p><strong>买入回看时间:</strong> <span id="lookbackMinutesBuy"></span>分钟</p>
                    <p><strong>卖出回看时间:</strong> <span id="lookbackMinutesSell"></span>分钟</p>
                    <p><strong>是否实盘:</strong> <span id="isLiveTrading"></span></p>
                </div>
                <div style="flex: 1; min-width: 200px;">
                    <p><strong>最高账户价值:</strong> <span id="highestAccountValue"></span> USDT</p>
                    <p><strong>最低账户价值:</strong> <span id="lowestAccountValue"></span> USDT</p>
                    <p><strong>开始时间:</strong> <span id="startTime"></span></p>
                    <p><strong>结束时间:</strong> <span id="endTime"></span></p>
                </div>
            </div>
        </div>

        <!-- 添加消息提示容器 -->
        <div id="toastContainer"></div>

        <!-- 添加详情弹窗 -->
        <div id="detailModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.4);"></div>

        <!-- 添加弹窗样式 -->
        <style>
            .modal-content {
                background-color: #fefefe;
                margin: 10% auto;
                padding: 20px;
                border: 1px solid #888;
                width: 100%;
                max-width: 600px;
                border-radius: 5px;
                box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
            }

            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 1px solid #eee;
                padding-bottom: 10px;
                margin-bottom: 15px;
            }

            .modal-header h2 {
                margin: 0;
                font-size: 1.5em;
            }

            .close {
                color: #aaa;
                font-size: 28px;
                font-weight: bold;
                cursor: pointer;
            }

            .close:hover,
            .close:focus {
                color: black;
                text-decoration: none;
            }

            .modal-section {
                margin-bottom: 20px;
                padding: 10px;
                background-color: #f9f9f9;
                border-radius: 4px;
            }

            .section-title {
                font-weight: bold;
                margin-bottom: 10px;
                padding-bottom: 5px;
                border-bottom: 1px solid #eee;
            }

            .data-row {
                display: flex;
                margin-bottom: 5px;
            }

            .data-label {
                flex: 0 0 120px;
                font-weight: bold;
            }

            .data-value {
                flex: 1;
            }

            .profit-positive {
                color: #52c41a;
            }

            .profit-negative {
                color: #f5222d;
            }

            /* 矩形选择分析模态框样式 */
            .brush-analysis-modal {
                position: fixed;
                top: 50px;
                right: 20px;
                z-index: 10000;
                background: white;
                border: 2px solid #1890ff;
                border-radius: 8px;
                box-shadow: 0 8px 24px rgba(0,0,0,0.3);
                width: 400px;
                max-height: 90%;
                overflow-y: auto;
                font-size: 12px;
                cursor: move;
            }

            .brush-analysis-modal .modal-content {
                padding: 0;
                margin: 0;
                border: none;
                box-shadow: none;
                background: transparent;
            }

            .brush-analysis-modal .modal-header {
                background: #1890ff;
                color: white;
                padding: 10px 15px;
                margin: 0;
                border-radius: 6px 6px 0 0;
                cursor: move;
                user-select: none;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .brush-analysis-modal .modal-header h2 {
                margin: 0;
                font-size: 14px;
                font-weight: bold;
            }

            .brush-analysis-modal .close {
                color: white;
                font-size: 18px;
                font-weight: bold;
                cursor: pointer;
                background: none;
                border: none;
                padding: 0;
                margin: 0;
            }

            .brush-analysis-modal .close:hover {
                color: #ffccc7;
            }

            .brush-analysis-modal .modal-body {
                padding: 15px;
                max-height: 60vh;
                overflow-y: auto;
                overflow-x: hidden;
            }

            /* 确保滚动条可见 */
            .brush-analysis-modal .modal-body::-webkit-scrollbar {
                width: 8px;
            }

            .brush-analysis-modal .modal-body::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 4px;
            }

            .brush-analysis-modal .modal-body::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 4px;
            }

            .brush-analysis-modal .modal-body::-webkit-scrollbar-thumb:hover {
                background: #a8a8a8;
            }

            .brush-analysis-modal .section-title {
                font-size: 13px;
                color: #1890ff;
                border-left: 3px solid #1890ff;
                padding-left: 8px;
                margin-bottom: 10px;
                font-weight: bold;
            }

            .brush-analysis-modal .data-row {
                padding: 4px 0;
                border-bottom: 1px solid #f0f0f0;
                display: flex;
                justify-content: space-between;
            }

            .brush-analysis-modal .data-row:last-child {
                border-bottom: none;
            }

            .brush-analysis-modal .data-label {
                font-weight: 600;
                color: #333;
                flex: 0 0 120px;
                font-size: 11px;
                white-space: nowrap;
            }

            .brush-analysis-modal .data-value {
                color: #666;
                font-family: 'Courier New', monospace;
                text-align: right;
                flex: 1;
                font-size: 11px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                min-width: 0;
            }

            .brush-analysis-modal .modal-section {
                background: #fafafa;
                border-radius: 4px;
                padding: 10px;
                margin-bottom: 10px;
                border: 1px solid #e8e8e8;
            }

            .brush-analysis-modal .modal-section:last-child {
                margin-bottom: 0;
            }

            /* 拖拽时的样式 */
            .brush-analysis-modal.dragging {
                opacity: 0.8;
                transform: scale(0.98);
                z-index: 10001;
            }

            .brush-analysis-modal .modal-header {
                cursor: move;
                user-select: none;
            }

            /* 交易原因显示样式 - 支持换行 */
            .trade-reason {
                white-space: pre-line;
                word-wrap: break-word;
                word-break: break-word;
                line-height: 1.4;
                max-width: 100%;
                overflow-wrap: break-word;
            }

            /* 在tooltip中显示原因的样式 */
            .tooltip-reason {
                white-space: pre-line;
                word-wrap: break-word;
                word-break: break-word;
                line-height: 1.3;
                max-width: 300px;
                overflow-wrap: break-word;
            }

            /* 在弹窗中显示原因的样式 */
            .modal-reason {
                white-space: pre-line;
                word-wrap: break-word;
                word-break: break-word;
                line-height: 1.5;
                max-width: 100%;
                overflow-wrap: break-word;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                background-color: #f8f9fa;
                padding: 8px;
                border-radius: 4px;
                border: 1px solid #e9ecef;
            }
        </style>

        <script>
            // 全局变量
            let currentInterval = "1m";
            let chart;
            let klineData = [];
            let volumes = [];
            let timestamps = [];
            let allAccountValueData = [];
            let accountValues = [];  // 添加账户价值数组
            let timedata = {};       // 添加timedata对象
            let strategyLogs = {};
            let orderData = {};       // 添加委托数据对象
            let logParser = new StrategyLogParser(); // 策略日志解析器
            let strategyLogData = null; // 策略日志数据

            // 更新策略ID到输入框
            function updateStrategyIdInput(strategyId) {
                if (strategyId) {
                    const strategyIdInput = document.getElementById('strategyIdInput');
                    if (strategyIdInput) {
                        strategyIdInput.value = strategyId;
                        console.log('策略ID已更新到输入框:', strategyId);
                    }
                }
            }

            // 本地存储工具函数
            const LocalStorage = {
                // 保存开关状态
                saveToggleState: function(toggleId, state) {
                    try {
                        localStorage.setItem(`toggle_${toggleId}`, JSON.stringify(state));
                    } catch (e) {
                        console.warn('无法保存开关状态到本地存储:', e);
                    }
                },

                // 获取开关状态
                getToggleState: function(toggleId, defaultState = true) {
                    try {
                        const saved = localStorage.getItem(`toggle_${toggleId}`);
                        return saved !== null ? JSON.parse(saved) : defaultState;
                    } catch (e) {
                        console.warn('无法从本地存储读取开关状态:', e);
                        return defaultState;
                    }
                },

                // 保存图表配置
                saveChartConfig: function(config) {
                    try {
                        localStorage.setItem('chart_config', JSON.stringify(config));
                    } catch (e) {
                        console.warn('无法保存图表配置到本地存储:', e);
                    }
                },

                // 获取图表配置
                getChartConfig: function() {
                    try {
                        const saved = localStorage.getItem('chart_config');
                        return saved ? JSON.parse(saved) : {};
                    } catch (e) {
                        console.warn('无法从本地存储读取图表配置:', e);
                        return {};
                    }
                }
            };

            // 键盘快捷键支持
            function initializeKeyboardControls() {
                document.addEventListener('keydown', function(event) {
                    // 只在图表容器获得焦点时响应键盘事件
                    if (!chart) return;

                    // 检查是否在输入框中，如果是则不响应快捷键
                    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
                        return;
                    }

                    switch(event.key) {
                        case 'ArrowLeft':
                            event.preventDefault();
                            panLeft();
                            break;
                        case 'ArrowRight':
                            event.preventDefault();
                            panRight();
                            break;
                        case '+':
                        case '=':
                            event.preventDefault();
                            zoomIn();
                            break;
                        case '-':
                            event.preventDefault();
                            zoomOut();
                            break;
                        case 'Home':
                            event.preventDefault();
                            resetZoom();
                            break;
                        case 'End':
                            event.preventDefault();
                            fitToData();
                            break;
                        case '1':
                            if (event.ctrlKey) {
                                event.preventDefault();
                                showTimeRange('1h');
                            }
                            break;
                        case '4':
                            if (event.ctrlKey) {
                                event.preventDefault();
                                showTimeRange('4h');
                            }
                            break;
                        case '0':
                            if (event.ctrlKey) {
                                event.preventDefault();
                                showTimeRange('all');
                            }
                            break;
                    }
                });

                // 让图表容器可以获得焦点
                const chartContainer = document.getElementById('chart');
                if (chartContainer) {
                    chartContainer.setAttribute('tabindex', '0');
                    chartContainer.style.outline = 'none';
                }
            }

            // 初始化开关状态
            function initializeToggles() {
                // 交易数据开关
                const loadTradeDataToggle = document.getElementById('loadTradeDataToggle');
                if (loadTradeDataToggle) {
                    const savedState = LocalStorage.getToggleState('loadTradeData', true);
                    loadTradeDataToggle.checked = savedState;

                    // 添加事件监听器
                    loadTradeDataToggle.addEventListener('change', function() {
                        LocalStorage.saveToggleState('loadTradeData', this.checked);
                        console.log('交易箭头显示开关状态已保存:', this.checked);

                        // 立即应用设置
                        if (this.checked) {
                            // 开启智能显示，检查当前范围
                            checkAndLoadTradeData();
                        } else {
                            // 关闭智能显示，隐藏所有交易箭头
                            hideTradeMarkers();
                            updateTradeDataStatus('已手动关闭', 'disabled');
                        }
                    });
                }

                // 委托数据开关
                const loadOrderDataToggle = document.getElementById('loadOrderDataToggle');
                if (loadOrderDataToggle) {
                    const savedState = LocalStorage.getToggleState('loadOrderData', true);
                    loadOrderDataToggle.checked = savedState;

                    // 添加事件监听器
                    loadOrderDataToggle.addEventListener('change', function() {
                        LocalStorage.saveToggleState('loadOrderData', this.checked);
                        console.log('委托数据开关状态已保存:', this.checked);
                    });
                }

                console.log('开关状态已初始化');
            }

            // 保存图表折线显示状态
            function saveChartLegendState() {
                if (chart && chart.getOption) {
                    try {
                        const option = chart.getOption();
                        if (option && option.legend && option.legend[0] && option.legend[0].selected) {
                            const legendState = option.legend[0].selected;
                            LocalStorage.saveChartConfig({ legendSelected: legendState });
                            console.log('图表折线状态已保存:', legendState);
                        }
                    } catch (e) {
                        console.warn('保存图表折线状态失败:', e);
                    }
                }
            }

            // 恢复图表折线显示状态
            function restoreChartLegendState(option) {
                try {
                    const chartConfig = LocalStorage.getChartConfig();
                    if (chartConfig.legendSelected && option.legend) {
                        option.legend.selected = { ...option.legend.selected, ...chartConfig.legendSelected };
                        console.log('图表折线状态已恢复:', chartConfig.legendSelected);
                    }
                } catch (e) {
                    console.warn('恢复图表折线状态失败:', e);
                }
            }












            /**
             * 添加策略日志数据到图表
             * @param {Object} option - 图表配置
             */
            function addStrategyLogSeries(option) {
                if (!strategyLogData || !strategyLogData.rows || strategyLogData.rows.length === 0) {
                    console.log('没有策略日志数据可添加');
                    return;
                }

                console.log('添加策略日志数据到图表');

                // 按单位分组列
                const columnGroups = logParser.groupColumnsByUnit();
                console.log('列分组:', columnGroups);

                // 创建新的Y轴
                let yAxisIndex = option.yAxis.length;
                const yAxisOffset = 60; // 每个Y轴的偏移量

                // 为每个分组创建一个Y轴和一组系列
                Object.keys(columnGroups).forEach((group, groupIndex) => {
                    const columns = columnGroups[group];
                    if (columns.length === 0) return; // 跳过空分组

                    // 跳过时间戳列
                    if (group === 'timestamp') return;

                    // 添加新的Y轴
                    option.yAxis.push({
                        name: group,
                        nameLocation: 'middle',
                        nameGap: 50,
                        scale: true,
                        position: 'left',
                        offset: yAxisOffset * groupIndex,
                        splitLine: { show: false },
                        axisLine: {
                            show: true,
                            lineStyle: { color: getGroupColor(group) }
                        },
                        axisLabel: {
                            formatter: function(value) {
                                return value.toFixed(2);
                            },
                            color: getGroupColor(group)
                        }
                    });

                    // 如果是价格类型的数据，使用与K线相同的Y轴
                    if (group === 'price') {
                        // 价格类型使用K线的Y轴（索引0）
                        yAxisIndex = 0;
                    }

                    // 为每个列添加一个系列
                    columns.forEach(column => {

                        const columnData = logParser.getColumnData(column);
                        const timestamps = logParser.getTimestamps();
                        
                        // 创建数据点
                        const seriesData = [];
                        for (let i = 0; i < timestamps.length; i++) {
                            if (columnData[i] !== undefined && columnData[i] !== null) {
                                if (columnData[i] == 0 || columnData[i] == 999 || columnData[i] > 10000) {
                                    seriesData.push([timestamps[i], 'none']);
                                } else {
                                    seriesData.push([timestamps[i], columnData[i]]);
                                }
                            }
                        }

                        // 获取列的显示名称
                        const columnDisplayName = getColumnDisplayName(column);

                        // 将列名添加到图例数据中
                        if (!option.legend.data.includes(columnDisplayName)) {
                            option.legend.data.push(columnDisplayName);

                            // 默认只显示一些重要的数据系列，其他的默认隐藏
                            const importantColumns = ['账户价值', '当前仓位', '总利润', '触发价格', '实际触发价', '保护价', '最小触发价格', '收盘价', '开盘价'];
                            const chineseName = getColumnDisplayName(column);
                            const isImportant = importantColumns.some(name => chineseName.includes(name));

                            // 设置默认显示状态
                            option.legend.selected[columnDisplayName] = isImportant;
                        }

                        // 获取列的颜色
                        const seriesColor = getColumnColor(column, group);

                        // 添加系列
                        option.series.push({
                            name: columnDisplayName,
                            type: 'line',
                            yAxisIndex: yAxisIndex,
                            data: seriesData,
                            showSymbol: false,
                            lineStyle: {
                                width: 1,  // 增加线宽
                                shadowColor: seriesColor,
                                shadowBlur: 5,
                                shadowOffsetY: 2,
                                cap: 'round'  // 线段末端使用圆形
                            },
                            itemStyle: {
                                color: seriesColor,
                                borderWidth: 1,
                                borderColor: seriesColor,
                                shadowColor: seriesColor,
                                shadowBlur: 5
                            },
                            emphasis: {
                                lineStyle: {
                                    width: 1,  // 鼠标悬停时线宽增加
                                    shadowBlur: 10
                                },
                                itemStyle: {
                                    shadowBlur: 10
                                }
                            }
                        });
                    });

                    // 增加Y轴索引
                    yAxisIndex++;
                });
            }

            /**
             * 获取列的显示名称
             * @param {string} column - 列名
             * @returns {string} - 显示名称
             */
            function getColumnDisplayName(column) {
                // 如果有中文标题，使用中文标题
                if (strategyLogData && strategyLogData.headers && strategyLogData.headers.zh) {
                    const index = strategyLogData.headers.en.indexOf(column);
                    if (index !== -1 && strategyLogData.headers.zh[index]) {
                        return strategyLogData.headers.zh[index];
                    }
                }
                return column;
            }

            /**
             * 获取分组的颜色
             * @param {string} group - 分组名
             * @returns {string} - 颜色
             */
            function getGroupColor(group) {
                const colors = {
                    price: '#d81b60',     // 更深的红色
                    percentage: '#e65100', // 更深的橙色
                    count: '#2e7d32',     // 更深的绿色
                    minutes: '#0d47a1',   // 更深的蓝色
                    account: '#4a148c',   // 更深的紫色
                    position: '#880e4f',  // 更深的粉色
                    profit: '#e65100',    // 更深的橙色
                    other: '#424242'      // 更深的灰色
                };
                return colors[group] || '#424242';
            }

            /**
             * 获取列的颜色
             * @param {string} column - 列名
             * @param {string} group - 分组名
             * @returns {string} - 颜色
             */
            function getColumnColor(column, group) {
                // 使用分组颜色的不同色调
                const baseColor = getGroupColor(group);
                const index = strategyLogData.headers.en.indexOf(column);

                // 根据索引生成不同的色调
                return adjustColor(baseColor, index * 10);
            }

            /**
             * 调整颜色的亮度
             * @param {string} color - 基础颜色
             * @param {number} percent - 调整百分比
             * @returns {string} - 调整后的颜色
             */
            function adjustColor(color, percent) {
                const R = parseInt(color.substring(1,3),16);
                const G = parseInt(color.substring(3,5),16);
                const B = parseInt(color.substring(5,7),16);

                // 使用较小的百分比变化，保持颜色深度
                const adjustR = Math.min(255, Math.max(0, R + percent * 0.5));
                const adjustG = Math.min(255, Math.max(0, G + percent * 0.5));
                const adjustB = Math.min(255, Math.max(0, B + percent * 0.5));

                const RR = ((adjustR.toString(16).length === 1) ? "0" + adjustR.toString(16) : adjustR.toString(16));
                const GG = ((adjustG.toString(16).length === 1) ? "0" + adjustG.toString(16) : adjustG.toString(16));
                const BB = ((adjustB.toString(16).length === 1) ? "0" + adjustB.toString(16) : adjustB.toString(16));

                return "#"+RR+GG+BB;
            }

            // 在更新图表后重新绑定事件
            function updateChart(data, orders = {}) {
                //console.log('Updating chart with data:', data);

                // 清除之前的数据
                allAccountValueData = [];  // 清除账户价值数据
                orderData = orders;  // 更新委托数据

                // 更新全局变量
                timedata = data.timedata || {};

                // 从timedata获取时间戳并按时间排序
                timestamps = Object.keys(timedata).sort();

                // 收集交易数据
                const trades = [];
                for (const timestamp in timedata) {
                    if (timedata[timestamp].trade) {
                        // 确保每个交易对象都有timestamp_no_seconds属性
                        const trade = timedata[timestamp].trade;
                        if (!trade.timestamp_no_seconds) {
                            trade.timestamp_no_seconds = timestamp;
                        }
                        trades.push(trade);
                    }
                }
                //console.log('Trades:', trades.length);

                // 收集委托数据
                const orderMarkers = [];
                console.log('开始处理委托数据, 分钟时间点数:', Object.keys(orderData).length);

                // 检查时间戳格式
                const timestampFormat = timestamps.length > 0 ? timestamps[0] : 'N/A';
                console.log('图表时间戳格式样例:', timestampFormat);

                // 创建时间戳映射表，加速查找
                const timestampMap = {};
                timestamps.forEach(ts => {
                    timestampMap[ts] = true;
                });
                console.log(`创建了时间戳映射表，包含 ${timestamps.length} 个时间点`);

                // 打印委托数据的分钟时间戳格式
                const minuteTimestamps = Object.keys(orderData);
                if (minuteTimestamps.length > 0) {
                    console.log('委托数据分钟时间戳样例:', minuteTimestamps[0]);

                    // 检查分钟时间戳是否在图表时间范围内
                    const firstMinuteTs = minuteTimestamps[0];
                    const isInTimeRange = timestampMap[firstMinuteTs] === true;
                    console.log(`第一个分钟时间戳 ${firstMinuteTs} 是否在图表时间范围内: ${isInTimeRange}`);

                    // 如果不在范围内，检查时间格式是否一致
                    if (!isInTimeRange) {
                        console.log('图表时间范围:', timestamps[0], 'to', timestamps[timestamps.length-1]);
                    }

                    // 打印第一个分钟的订单数量
                    const firstMinuteOrders = orderData[firstMinuteTs];
                    if (Array.isArray(firstMinuteOrders) && firstMinuteOrders.length > 0) {
                        console.log(`第一个分钟 ${firstMinuteTs} 有 ${firstMinuteOrders.length} 条委托`);
                        console.log('第一个分钟的第一条委托:', firstMinuteOrders[0]);
                    }
                }

                // 统计每分钟的委托数量
                const ordersPerMinute = {};
                let totalOrders = 0;

                for (const minute in orderData) {
                    const minuteOrders = orderData[minute];
                    if (Array.isArray(minuteOrders)) {
                        ordersPerMinute[minute] = minuteOrders.length;
                        totalOrders += minuteOrders.length;
                    }
                }

                console.log(`总共 ${Object.keys(ordersPerMinute).length} 个分钟时间点，共 ${totalOrders} 条委托`);

                // 处理每个分钟的委托
                for (const minute in orderData) {
                    const minuteOrders = orderData[minute];
                    if (!Array.isArray(minuteOrders) || minuteOrders.length === 0) {
                        continue;
                    }

                    // 检查该分钟是否在图表范围内
                    let useMinuteTs = minute;
                    let isMinuteInRange = timestampMap[minute] === true;

                    // 如果分钟时间戳不在范围内，尝试找到匹配的时间点
                    if (!isMinuteInRange) {
                        // 尝试找到同一天的时间点
                        const datePart = minute.split(' ')[0];
                        for (const ts of timestamps) {
                            if (ts.startsWith(datePart)) {
                                useMinuteTs = ts;
                                isMinuteInRange = true;
                                console.log(`找到分钟时间匹配: ${minute} -> ${useMinuteTs}`);
                                break;
                            }
                        }

                        if (!isMinuteInRange) {
                            console.log(`分钟时间戳 ${minute} 不在图表范围内，跳过`);
                            continue;
                        }
                    }

                    console.log(`处理分钟 ${minute} 的 ${minuteOrders.length} 条委托`);

                    // 将该分钟的所有委托添加到标记中
                    for (const order of minuteOrders) {
                        // 只添加有效的委托
                        if (!order.size) {
                            console.log('跳过无效委托:', order);
                            continue;
                        }

                        // 使用分钟时间戳或原始时间戳
                        const useTimestamp = order.minute_timestamp || useMinuteTs;

                        orderMarkers.push({
                            timestamp: useTimestamp,  // 使用分钟时间戳
                            original_timestamp: order.timestamp,  // 保存原始时间戳
                            order_id: order.order_id,
                            price: order.price === 0 ? order.filled_price : order.price,
                            size: order.size,
                            side: order.side,  // buy/sell
                            position_side: order.position_side,  // long/short
                            order_type: order.order_type,
                            state: order.state,
                            state_cn: order.state_cn,
                            direction_cn: order.direction_cn,
                            filled_price: order.filled_price,
                            filled_size: order.filled_size,
                            fee: order.fee
                        });
                    }
                }
                console.log('委托标记总数:', orderMarkers.length);

                // 打印前几个委托标记的时间戳
                if (orderMarkers.length > 0) {
                    const sampleMarkers = orderMarkers.slice(0, Math.min(3, orderMarkers.length));
                    sampleMarkers.forEach((marker, i) => {
                        console.log(`委托标记 ${i+1}: timestamp=${marker.timestamp}, original_timestamp=${marker.original_timestamp}, price=${marker.price}, side=${marker.side}, state=${marker.state}`);
                    });
                }

                // 构建图表需要的数据结构
                let klineData = [];
                let volumes = [];
                let accountValues = [];
                //debugger
                // 从timedata构建所需的数据数组
                for (const timestamp of timestamps) {
                    const point = timedata[timestamp];
                    if (point) {
                        klineData.push(point.kline);
                        volumes.push(point.volume);
                        accountValues.push(point.account_value);
                    }
                }

                // 定义K线图的颜色
                const upColor = '#00da3c';
                const upBorderColor = '#00da3c';
                const downColor = '#ec0000';
                const downBorderColor = '#ec0000';

                const option = {
                    backgroundColor: '#fff',
                    animation: false,
                    legend: {
                        data: ['K线', '交易', '账户价值'],
                        top: '30px',
                        type: 'scroll',
                        selector: [
                            {
                                title: '全选',
                                type: 'all'
                            },
                            {
                                title: '反选',
                                type: 'inverse'
                            }
                        ],
                        selected: {
                            'K线': true,
                            '交易': true,
                            '账户价值': true
                        },
                        textStyle: {
                            fontSize: 12
                        },
                        width: '80%',
                        padding: [5, 10],
                        itemGap: 10
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross'
                        },
                        position: function(pos, params, el, elRect, size) {
                            // 获取鼠标坐标
                            const [x, y] = pos;
                            const chartWidth = size.viewSize[0];
                            const chartHeight = size.viewSize[1];
                            const tooltipWidth = size.contentSize[0];
                            const tooltipHeight = size.contentSize[1];

                            // 计算tooltip的位置，确保不会超出图表边界
                            let tooltipX = x + 10; // 默认在鼠标右侧10px
                            let tooltipY = y + 10; // 默认在鼠标下方10px

                            // 如果tooltip会超出右边界，则显示在鼠标左侧
                            if (tooltipX + tooltipWidth > chartWidth) {
                                tooltipX = x - tooltipWidth - 10;
                            }

                            // 如果tooltip会超出下边界，则显示在鼠标上方
                            if (tooltipY + tooltipHeight > chartHeight) {
                                tooltipY = y - tooltipHeight - 10;
                            }

                            return [tooltipX, tooltipY];
                        },
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        borderColor: '#ccc',
                        borderWidth: 1,
                        textStyle: {
                            color: '#333'
                        },
                        formatter: function(params) {
                            if (!params || !Array.isArray(params) || params.length === 0) {
                                return 'No data available';
                            }

                            const candlestick = params[0];
                            if (!candlestick || !candlestick.data || !Array.isArray(candlestick.data)) {
                                return 'No data available';
                            }

                            const index = candlestick.dataIndex;
                            const timestamp = timestamps[index];

                            // 使用timedata直接获取数据点
                            const dataPoint = timedata[timestamp];
                            if (!dataPoint) {
                                return 'No data available for this timestamp';
                            }

                            const [open, close, low, high] = dataPoint.kline;
                            const volume = dataPoint.volume;
                            const priceChange = dataPoint.price_change;
                            const priceRange = dataPoint.price_range;
                            const trade = dataPoint.trade;
                            const accountValue = dataPoint.account_value;

                            // 调试信息
                           // console.log('Current timestamp:', timestamp);
                           // console.log('Data point:', dataPoint);

                            // 计算相对于全局最高/最低点的百分比
                            const globalHigh = data.global_high || 0;
                            const globalLow = data.global_low || 0;
                            let fromHighPct = '0.00';
                            let fromLowPct = '0.00';

                            if (globalHigh > 0 && close > 0) {
                                fromHighPct = ((globalHigh - close) / globalHigh * 100).toFixed(2);
                            }
                            if (globalLow > 0 && close > 0) {
                                fromLowPct = ((close - globalLow) / globalLow * 100).toFixed(2);
                            }

                            const color = close >= open ? upColor : downColor;
                            let priceInfo = [
                                `<div style="margin-bottom: 5px;">时间: ${timestamp}</div>`,
                                `<div style="color: ${color}">开盘: ${(open || 0).toFixed(5)}</div>`,
                                `<div style="color: ${color}">收盘: ${(close || 0).toFixed(5)}</div>`,
                                `<div style="color: ${color}">最高: ${(high || 0).toFixed(5)}</div>`,
                                `<div style="color: ${color}">最低: ${(low || 0).toFixed(5)}</div>`,
                                `<div>涨跌幅: ${(priceChange || 0)}%</div>`,
                                `<div>震幅: ${(priceRange || 0)}%</div>`
                            ];

                            if (globalHigh > 0 && globalLow > 0) {
                                priceInfo.push(`<div>距最高: -${fromHighPct}%</div>`);
                                priceInfo.push(`<div>距最低: +${fromLowPct}%</div>`);
                            }

                            priceInfo.push(`<div>成交量: ${(volume || 0).toFixed(2)}</div>`);

                            // 显示账户价值
                            if (accountValue) {
                                priceInfo.push(`<div style="color: #1976d2">账户价值: ${accountValue.toFixed(2)}</div>`);
                            }

                            // 显示交易信息
                            let tradeInfo = '';
                            if (trade) {
                                const tradeColor = trade.position_type.includes('long') ? '#26a69a' : '#ef5350';

                                // 计算距离last_high和last_low的百分比
                                let fromLastHighPct = '0.00';
                                let fromLastLowPct = '0.00';

                                if (trade.last_high && parseFloat(trade.last_high) > 0 && parseFloat(trade.price) > 0) {
                                    fromLastHighPct = ((parseFloat(trade.last_high) - parseFloat(trade.price)) / parseFloat(trade.last_high) * 100).toFixed(2);
                                }

                                if (trade.last_low && parseFloat(trade.last_low) > 0 && parseFloat(trade.price) > 0) {
                                    fromLastLowPct = ((parseFloat(trade.price) - parseFloat(trade.last_low)) / parseFloat(trade.last_low) * 100).toFixed(2);
                                }

                                tradeInfo = `
                                <div style="margin-top: 10px; border-top: 1px solid #ddd; padding-top: 5px;">
                                    <div style="font-weight: bold; color: ${tradeColor}">交易信息:</div>
                                    <div>时间: ${trade.timestamp}</div>
                                    <div>类型: ${trade.position_type}</div>
                                    <div>价格: ${parseFloat(trade.price).toFixed(5)}</div>
                                    <div>数量: ${trade.amount}</div>
                                    <div>手续费: ${parseFloat(trade.fee || 0).toFixed(5)}</div>
                                    <div>距最高点: -${fromLastHighPct}%</div>
                                    <div>距最低点: +${fromLastLowPct}%</div>
                                `;

                                if (trade.profit_amount) {
                                    const profitColor = parseFloat(trade.profit_amount) >= 0 ? '#26a69a' : '#ef5350';
                                    tradeInfo += `<div style="color: ${profitColor}">盈亏: ${parseFloat(trade.profit_amount).toFixed(2)} (${trade.profit_percentage}%)</div>`;
                                }

                                if (trade.trigger_reason) {
                                    tradeInfo += `<div>原因: <span class="tooltip-reason">${trade.trigger_reason}</span></div>`;
                                }

                                tradeInfo += '</div>';
                            }

                            // 获取策略日志
                            let logInfo = '';
                            const logData = strategyLogs[timestamp.replace(' ', 'T')];

                            if (logData) {
                                logInfo = `
                                <div style="margin-top: 10px; border-top: 1px solid #ddd; padding-top: 5px;">
                                    <div style="font-weight: bold;">策略分析:</div>
                                `;

                                if (logData.action) {
                                    logInfo += `<div>动作: ${logData.action}</div>`;
                                }

                                if (logData.reason) {
                                    logInfo += `<div>原因: <span class="tooltip-reason">${logData.reason}</span></div>`;
                                }

                                if (logData.trigger_price) {
                                    const triggerPrice = typeof logData.trigger_price === 'number'
                                        ? logData.trigger_price.toFixed(5)
                                        : logData.trigger_price;
                                    logInfo += `<div>触发价格: ${triggerPrice}</div>`;
                                }

                                // 计算与last_high的距离百分比
                                if (logData.last_high && parseFloat(logData.last_high) > 0) {
                                    const fromLastHighPct = ((parseFloat(logData.last_high) - parseFloat(logData.close)) / parseFloat(logData.last_high) * 100).toFixed(2);
                                    logInfo += `<div>距最高点: -${fromLastHighPct}%</div>`;
                                }

                                // 计算与last_low的距离百分比
                                if (logData.last_low && parseFloat(logData.last_low) > 0) {
                                    const fromLastLowPct = ((parseFloat(logData.close) - parseFloat(logData.last_low)) / parseFloat(logData.last_low) * 100).toFixed(2);
                                    logInfo += `<div>距最低点: +${fromLastLowPct}%</div>`;
                                }

                                if (logData.min_trigger_price) {
                                    const minTriggerPrice = typeof logData.min_trigger_price === 'number'
                                        ? logData.min_trigger_price.toFixed(5)
                                        : logData.min_trigger_price;
                                    logInfo += `<div>最小触发价格: ${minTriggerPrice}</div>`;
                                }

                                // 添加其他可能存在的字段
                                for (const key in logData) {
                                    if (!['action', 'reason', 'trigger_price', 'min_trigger_price', 'timestamp', 'last_high', 'last_low'].includes(key)) {
                                        if (typeof logData[key] === 'number') {
                                            logInfo += `<div>${key}: ${logData[key].toFixed(5)}</div>`;
                                        } else if (logData[key] !== null && logData[key] !== undefined) {
                                            logInfo += `<div>${key}: ${logData[key]}</div>`;
                                        }
                                    }
                                }

                                logInfo += '</div>';
                            }

                            // 获取所有折线图在当前时间点的数据
                            let strategyLogSeriesInfo = '';
                            if (strategyLogData && strategyLogData.rowsByTime && strategyLogData.rowsByTime[timestamp]) {
                                const rowData = strategyLogData.rowsByTime[timestamp];

                                strategyLogSeriesInfo = `
                                <div style="margin-top: 10px; border-top: 1px solid #ddd; padding-top: 5px;">
                                    <div style="font-weight: bold;">策略日志数据:</div>
                                `;

                                // 遍历所有列，显示中文名称和数值
                                for (const column in rowData) {
                                    // 跳过时间戳列
                                    if (column === 'timestamp') continue;

                                    // 获取列的中文名称
                                    const columnDisplayName = getColumnDisplayName(column);

                                    // 获取列的值
                                    let value = rowData[column];

                                    // 格式化数值
                                    if (typeof value === 'number') {
                                        value = value.toFixed(5);
                                    }

                                    // 获取列的颜色
                                    let color = '#333';
                                    if (strategyLogData && strategyLogData.headers) {
                                        const columnIndex = strategyLogData.headers.en.indexOf(column);
                                        if (columnIndex !== -1) {
                                            // 找到列所属的分组
                                            const columnGroups = logParser.groupColumnsByUnit();
                                            let group = 'other';
                                            for (const g in columnGroups) {
                                                if (columnGroups[g].includes(column)) {
                                                    group = g;
                                                    break;
                                                }
                                            }
                                            color = getColumnColor(column, group);
                                        }
                                    }

                                    // 添加到tooltip中，使用粗体和文本阴影增强可见性
                                    strategyLogSeriesInfo += `<div style="color: ${color}; text-shadow: 0 0 1px rgba(0,0,0,0.3);">${columnDisplayName}: ${value}</div>`;
                                }
                                // debugger
                                strategyLogSeriesInfo += '</div>';
                            }

                            return `<div style="font-size:12px;line-height:1.5;">
                                <div>${priceInfo.join('')}</div>
                                ${tradeInfo}
                                ${logInfo}
                                ${strategyLogSeriesInfo}
                            </div>`;
                        }
                    },
                    axisPointer: {
                        link: {xAxisIndex: 'all'},
                        label: {
                            backgroundColor: '#777'
                        }
                    },
                    grid: [{
                        left: '5%',  // 减小左边距
                        right: '2%',  // 减小右边距
                        height: '90%',  // 增加高度占比，因为不再显示成交量图表
                        containLabel: true
                    }],
                    xAxis: [{
                        type: 'category',
                        data: timestamps,
                        scale: true,
                        boundaryGap: false,
                        axisLine: { onZero: false },
                        splitLine: { show: false },
                        min: 'dataMin',
                        max: 'dataMax',
                        axisLabel: {
                            show: true,
                            margin: 4
                        },
                        triggerEvent: true
                    }],
                    yAxis: [{
                        scale: true,
                        splitArea: {
                            show: true
                        },
                        position: 'right',
                        axisLabel: {
                            inside: false,
                            margin: 4
                        }
                    }, {
                        scale: true,
                        position: 'right',  // 将账户价值的Y轴也放到右边
                        offset: 60,  // 与主Y轴保持一定距离
                        splitLine: { show: false },
                        axisLabel: {
                            formatter: function(value) {
                                return value.toFixed(2);
                            }
                        }
                    }],
                    toolbox: {
                        show: true,
                        feature: {
                            dataZoom: {
                                yAxisIndex: 'none',
                                title: {
                                    zoom: '区域缩放',
                                    back: '缩放还原'
                                }
                            },
                            restore: {
                                title: '还原'
                            },
                            saveAsImage: {
                                title: '保存为图片',
                                type: 'png',
                                backgroundColor: '#fff'
                            },
                            brush: {
                                type: ['rect', 'lineX', 'lineY', 'keep', 'clear'],
                                title: {
                                    rect: '矩形选择分析',
                                    lineX: 'X轴时间选择',
                                    lineY: 'Y轴价格选择',
                                    keep: '保持选择',
                                    clear: '清除选择'
                                }
                            }
                        },
                        right: '2%',
                        top: '10%',
                        orient: 'vertical'
                    },
                    brush: {
                        toolbox: ['rect', 'lineX', 'lineY', 'keep', 'clear'],
                        xAxisIndex: 0,
                        brushLink: 'all',
                        outOfBrush: {
                            colorAlpha: 0.1
                        },
                        brushStyle: {
                            borderWidth: 2,
                            color: 'rgba(24, 144, 255, 0.3)',
                            borderColor: '#1890ff'
                        },
                        transformable: true,
                        brushMode: 'single'
                    },
                    dataZoom: [{
                        type: 'inside',
                        xAxisIndex: [0],
                        start: 0,
                        end: calculateOneHourRange(timestamps),  // 显示开始一小时内的数据
                        zoomLock: false,  // 允许缩放
                        moveOnMouseMove: true,  // 鼠标移动时平移
                        moveOnMouseWheel: false,  // 鼠标滚轮缩放而不是平移
                        preventDefaultMouseMove: true,
                        minSpan: 0,  // 移除最小范围限制，允许无限放大
                        maxSpan: 100  // 最大范围100%
                    }, {
                        show: true,
                        xAxisIndex: [0],
                        type: 'slider',
                        bottom: '0%',
                        height: '5%',
                        start: 0,
                        end: calculateOneHourRange(timestamps),  // 显示开始一小时内的数据
                        showDetail: true,  // 显示详细信息
                        showDataShadow: true,  // 显示数据阴影
                        realtime: true,  // 实时更新
                        filterMode: 'filter',  // 过滤模式
                        minSpan: 0,  // 移除最小范围限制，允许无限放大
                        maxSpan: 100  // 最大范围100%
                    }],
                    series: [{
                        name: 'K线',
                        type: 'candlestick',
                        data: klineData,
                        itemStyle: {
                            color: upColor,
                            color0: downColor,
                            borderColor: upBorderColor,
                            borderColor0: downBorderColor
                        }

                    },  {
                        name: '交易',
                        type: 'scatter',
                        data: (() => {
                            // 检查是否需要显示交易数据
                            const loadTradeDataToggle = document.getElementById('loadTradeDataToggle');
                            const shouldLoadTradeData = loadTradeDataToggle && loadTradeDataToggle.checked;

                            if (!shouldLoadTradeData) {
                                console.log('交易数据显示已禁用');
                                return [];
                            }

                            return trades.map(trade => {
                            // 确保trade包含必要字段
                            if (!trade || !trade.timestamp || !trade.price) return null;

                            // 使用timestamp_no_seconds定位对应的K线数据点
                            const timeKey = trade.timestamp_no_seconds || trade.timestamp;

                            return {
                                value: [timeKey, trade.price],
                                itemStyle: (function() {
                                    switch(trade.position_type) {
                                        case 'long':
                                            return {
                                                color: '#f5222d',
                                                shadowBlur: 10,
                                                shadowColor: '#f5222d'
                                            };
                                        case 'close_long':
                                            return {
                                                color: '#fa8c16',
                                                shadowBlur: 10,
                                                shadowColor: '#fa8c16'
                                            };
                                        case 'short':
                                            return {
                                                color: '#52c41a',
                                                shadowBlur: 10,
                                                shadowColor: '#52c41a'
                                            };
                                        case 'close_short':
                                            return {
                                                color: '#1890ff',
                                                shadowBlur: 10,
                                                shadowColor: '#1890ff'
                                            };
                                        default:
                                            return {
                                                color: '#9e9e9e',
                                                shadowBlur: 10,
                                                shadowColor: '#9e9e9e'
                                            };
                                    }
                                })(),
                                symbol: 'arrow',
                                symbolRotate: trade.action === 'BUY' ? 90 : -90,
                                symbolSize: 30,
                                label: {
                                    show: true,
                                    position: trade.position_type && trade.position_type.startsWith('long') ? 'bottom' : 'top',
                                    formatter: () => {
                                        // 从时间戳中提取时分秒
                                        const timeStr = trade.timestamp.split(' ')[1].substring(0, 8);
                                        return `${trade.position_type}\n${timeStr}`;
                                    }
                                }
                            };
                        }).filter(Boolean);
                        })() // 闭合立即执行函数
                    }, {
                        name: '委托',
                        type: 'scatter',
                        data: (() => {
                            // 检查是否需要显示委托数据
                            const loadOrderDataToggle = document.getElementById('loadOrderDataToggle');
                            const shouldLoadOrderData = loadOrderDataToggle && loadOrderDataToggle.checked;

                            if (!shouldLoadOrderData) {
                                console.log('委托数据显示已禁用');
                                return [];
                            }

                            return orderMarkers.map(order => {
                            // 确保委托包含必要字段
                            if (!order || !order.timestamp) {
                                console.log('跳过无效委托:', order);
                                return null;
                            }

                            // 定位对应的K线数据点
                            const timeKey = order.timestamp;

                            // 检查时间戳是否在图表范围内
                            const isInRange = timestampMap[timeKey] === true;

                            if (!isInRange) {
                                console.log(`委托时间戳 ${timeKey} 不在图表范围内`);
                                return null;
                            }

                            // 打印成功添加的委托标记
                            // console.log(`添加委托标记: ${timeKey}, 价格: ${order.price}, 状态: ${order.state}`);


                            // 根据订单状态和类型决定颜色
                            let color, symbolType, symbolSize;

                            // 所有委托都使用箭头图标，但比交易箭头小
                            symbolType = 'arrow';
                            symbolSize = 15;  // 比交易箭头小

                            // 打印委托利润信息（如果有）
                            if (order.profit) {
                                console.log(`委托利润: ${order.profit}, 百分比: ${order.profit_percentage || 'N/A'}`);
                            }



                            // 根据订单状态决定颜色
                            if (order.state === 'filled') {
                                // 已成交
                                if (order.side === 'buy' && order.position_side === 'long') {
                                    color = '#f5222d';
                                } else if (order.side === 'sell' && order.position_side === 'long') {
                                    color = '#fa8c16';
                                } else if (order.side === 'sell' && order.position_side === 'short') {
                                    color = '#52c41a';
                                } else if (order.side === 'buy' && order.position_side === 'short') {
                                    color = '#1890ff';
                                } else {
                                    color = '#000000';
                                }

                            } else if (order.state === 'canceled') {
                                // 已撤单
                                color = '#bfbfbf';
                            } else {
                                // 其他状态（待成交等）
                                color = '#faad14';
                            }

                            return {
                                value: [timeKey, order.price],
                                itemStyle: {
                                    color: color,
                                    shadowBlur: 5,
                                    shadowColor: color,
                                    opacity: 0.8
                                },
                                symbol: symbolType,
                                symbolRotate: (order.side === 'buy' && order.position_side === 'long') || (order.side === 'sell' && order.position_side === 'short') ? 90 : -90,
                                symbolSize: symbolSize,

                                label: {
                                    show: true,
                                    position: order.side === 'buy' ? 'left' : 'right',
                                    formatter: () => {
                                        // 从时间戳中提取时分秒
                                        const timeStr = order.timestamp.split(' ')[1].substring(0, 8);
                                        // 显示简洁的委托信息
                                        //return `${order.direction_cn}\n${timeStr}`;
                                        return ``;
                                    },
                                    color: '#fff',
                                    backgroundColor: color,
                                    padding: [2, 4],
                                    borderRadius: 2,
                                    fontSize: 10
                                },
                                // 添加原始数据，方便悬停时显示详细信息
                                orderData: order  // 使用orderData属性名而非data，避免与ECharts内部属性冲突
                            };
                        }).filter(Boolean);
                        })() // 闭合立即执行函数
                    }, {
                        name: '触发价',
                        type: 'line',
                        data: (() => {
                            // 从策略日志数据中获取触发价数据
                            if (!strategyLogData || !strategyLogData.rows) return [];

                            const triggerPriceData = [];
                            strategyLogData.rows.forEach(row => {
                                if (row.trigger_price && row.trigger_price > 0) {
                                    triggerPriceData.push([row.timestamp, row.trigger_price]);
                                }
                            });
                            return triggerPriceData;
                        })(),
                        lineStyle: {
                            color: '#ff7875',
                            width: 2,
                            type: 'dashed'
                        },
                        symbol: 'none',
                        yAxisIndex: 0
                    }, {
                        name: '实际触发价',
                        type: 'line',
                        data: (() => {
                            // 从策略日志数据中获取实际触发价数据
                            if (!strategyLogData || !strategyLogData.rows) return [];

                            const actualTriggerPriceData = [];
                            strategyLogData.rows.forEach(row => {
                                if (row.actual_trigger_price && row.actual_trigger_price > 0) {
                                    actualTriggerPriceData.push([row.timestamp, row.actual_trigger_price]);
                                }
                            });
                            return actualTriggerPriceData;
                        })(),
                        lineStyle: {
                            color: '#ff4d4f',
                            width: 2
                        },
                        symbol: 'circle',
                        symbolSize: 4,
                        yAxisIndex: 0
                    }, {
                        name: '保护价',
                        type: 'line',
                        data: (() => {
                            // 从策略日志数据中获取保护价数据
                            if (!strategyLogData || !strategyLogData.rows) return [];

                            const protectionPriceData = [];
                            strategyLogData.rows.forEach(row => {
                                if (row.protection_price && row.protection_price > 0) {
                                    protectionPriceData.push([row.timestamp, row.protection_price]);
                                }
                            });
                            return protectionPriceData;
                        })(),
                        lineStyle: {
                            color: '#faad14',
                            width: 2,
                            type: 'dotted'
                        },
                        symbol: 'none',
                        yAxisIndex: 0
                    }]
                };

                // 添加策略日志数据
                if (strategyLogData && strategyLogData.rows &&
                   (strategyLogData.rows.length > 0 || Object.keys(strategyLogData.rowsByTime || {}).length > 0)) {
                    addStrategyLogSeries(option);
                }

                // 恢复图表折线显示状态
                restoreChartLegendState(option);

                // 创建和初始化图表
                if (chart) {
                    chart.dispose();
                }
                chart = echarts.init(document.getElementById('chart'));
                chart.setOption(option, true);

                // 添加图例状态变化监听器
                chart.on('legendselectchanged', function(params) {
                    console.log('图例状态变化:', params);
                    // 延迟保存，确保状态已更新
                    setTimeout(() => {
                        saveChartLegendState();
                    }, 100);
                });

                // 添加刷选事件监听器
                chart.on('brushSelected', function(params) {
                    console.log('刷选事件:', params);
                    if (params.batch && params.batch.length > 0) {
                        const brushComponent = params.batch[0];
                        if (brushComponent.selected && brushComponent.selected.length > 0) {
                            const selection = brushComponent.selected[0];
                            if (selection.dataIndex && selection.dataIndex.length > 0) {
                                // 处理矩形选择的数据
                                handleBrushSelection(selection, brushComponent);
                            }
                        }
                    }
                });

                // 添加刷选结束事件监听器
                chart.on('brushEnd', function(params) {
                    console.log('刷选结束:', params);
                    if (params.areas && params.areas.length > 0) {
                        // 处理矩形选择区域
                        handleBrushArea(params.areas[0]);
                    }
                });

                // 添加dataZoom事件监听器 - 智能交易数据加载
                chart.on('dataZoom', function(params) {
                    console.log('DataZoom事件:', params);
                    handleDataZoomChange(params);
                });

                // 定义标记点集合
                let highLowMarkers = [];

            // 智能交易箭头显示管理
            let tradeDataLoadTimeout = null;

            /**
             * 处理dataZoom变化事件 - 智能加载交易数据
             */
            function handleDataZoomChange(params) {
                // 清除之前的延时加载
                if (tradeDataLoadTimeout) {
                    clearTimeout(tradeDataLoadTimeout);
                }

                // 延时处理，避免频繁触发
                tradeDataLoadTimeout = setTimeout(() => {
                    checkAndLoadTradeData();
                }, 300);
            }

            /**
             * 检查当前视图范围并决定是否显示交易箭头标注
             */
            function checkAndLoadTradeData() {
                if (!chart) return;

                // 检查用户是否手动关闭了智能显示
                const loadTradeDataToggle = document.getElementById('loadTradeDataToggle');
                if (loadTradeDataToggle && !loadTradeDataToggle.checked) {
                    console.log('用户已手动关闭交易箭头显示');
                    return;
                }

                const option = chart.getOption();
                const dataZoom = option.dataZoom[0];

                // 获取当前显示的时间范围
                const currentRange = getCurrentVisibleTimeRange();
                if (!currentRange) return;

                const { startTime, endTime, durationHours } = currentRange;

                console.log(`当前视图范围: ${startTime} 到 ${endTime}, 持续时间: ${durationHours.toFixed(2)}小时`);

                // 更新状态显示
                updateTradeDataStatus(`检查范围: ${durationHours.toFixed(1)}小时`);

                // 判断是否需要显示交易箭头标注 - 大于1天就隐藏
                if (durationHours <= 24) {
                    // 小于等于1天，显示交易箭头标注
                    console.log(`范围≤1天 (${durationHours.toFixed(1)}h)，显示交易箭头标注`);
                    showTradeMarkers();
                } else {
                    // 大于1天，隐藏交易箭头标注
                    console.log(`范围>1天 (${durationHours.toFixed(1)}h)，隐藏交易箭头标注`);
                    hideTradeMarkers();
                }
            }

            /**
             * 更新交易数据状态显示
             */
            function updateTradeDataStatus(message, type = 'info') {
                const statusElement = document.getElementById('tradeDataStatus');
                if (statusElement) {
                    const icons = {
                        'info': '📊',
                        'loading': '⏳',
                        'success': '✅',
                        'error': '❌',
                        'disabled': '🚫',
                        'visible': '👁️',
                        'hidden': '🙈'
                    };

                    statusElement.innerHTML = `${icons[type] || '📊'} 交易箭头: ${message}`;
                    statusElement.style.color = type === 'error' ? '#f44336' :
                                               type === 'success' ? '#4caf50' :
                                               type === 'loading' ? '#ff9800' : '#666';
                }
            }

            /**
             * 显示交易箭头标注 - 使用图例选择
             */
            function showTradeMarkers() {
                if (!chart) return;

                // 使用ECharts原生的图例选择功能显示交易系列
                chart.dispatchAction({
                    type: 'legendSelect',
                    name: '交易'
                });

                updateTradeDataStatus('已显示', 'visible');
                console.log('通过图例选择显示交易标记');
            }

            /**
             * 隐藏交易箭头标注 - 使用图例取消选择
             */
            function hideTradeMarkers() {
                if (!chart) return;

                // 使用ECharts原生的图例取消选择功能隐藏交易系列
                chart.dispatchAction({
                    type: 'legendUnSelect',
                    name: '交易'
                });

                updateTradeDataStatus('已隐藏 (范围>1天)', 'hidden');
                console.log('通过图例取消选择隐藏交易标记');
            }

            /**
             * 获取当前可见的时间范围
             */
            function getCurrentVisibleTimeRange() {
                if (!chart || !timestamps || timestamps.length === 0) return null;

                const option = chart.getOption();
                const dataZoom = option.dataZoom[0];

                // 计算实际的数据索引范围
                const totalCount = timestamps.length;
                const startIndex = Math.floor(dataZoom.start / 100 * totalCount);
                const endIndex = Math.ceil(dataZoom.end / 100 * totalCount);

                const startTime = timestamps[Math.max(0, startIndex)];
                const endTime = timestamps[Math.min(totalCount - 1, endIndex - 1)];

                // 计算时间差（小时）
                const startDate = new Date(startTime);
                const endDate = new Date(endTime);
                const durationHours = (endDate - startDate) / (1000 * 60 * 60);

                return {
                    startTime,
                    endTime,
                    durationHours,
                    startIndex,
                    endIndex
                };
            }








                // 复制到剪贴板的统一函数
                function copyToClipboard(textToCopy, isDefault) {
                    // 使用现代的 Clipboard API
                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(textToCopy).then(function() {
                            console.log('已复制到剪贴板:', textToCopy);
                            if (isDefault) {
                                showToast('已复制 "showDetailModal" 到剪贴板', 'success');
                            } else {
                                showToast('已复制K线详细信息到剪贴板', 'success');
                            }
                        }).catch(function(err) {
                            console.error('复制失败:', err);
                            fallbackCopyTextToClipboard(textToCopy, isDefault);
                        });
                    } else {
                        // 降级方案
                        fallbackCopyTextToClipboard(textToCopy, isDefault);
                    }
                }

                // 降级复制方案（适用于不支持 Clipboard API 的浏览器）
                function fallbackCopyTextToClipboard(text, isDefault) {
                    const textArea = document.createElement("textarea");
                    textArea.value = text;

                    // 避免滚动到底部
                    textArea.style.top = "0";
                    textArea.style.left = "0";
                    textArea.style.position = "fixed";
                    textArea.style.opacity = "0";

                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();

                    try {
                        const successful = document.execCommand('copy');
                        if (successful) {
                            console.log('已复制到剪贴板 (降级方案):', text);
                            if (isDefault) {
                                showToast('已复制 "showDetailModal" 到剪贴板', 'success');
                            } else {
                                showToast('已复制K线详细信息到剪贴板', 'success');
                            }
                        } else {
                            console.error('复制失败 (降级方案)');
                            showToast('复制失败，请手动复制', 'error');
                        }
                    } catch (err) {
                        console.error('复制失败 (降级方案):', err);
                        showToast('复制失败，请手动复制', 'error');
                    }

                    document.body.removeChild(textArea);
                }

                // 绑定事件 - 右键复制功能（使用DOM事件）
                const chartContainer = document.getElementById('chart');
                chartContainer.addEventListener('contextmenu', function(e) {
                    e.preventDefault(); // 阻止默认右键菜单
                    console.log('DOM右键事件触发:', e);

                    let textToCopy = 'showDetailModal'; // 默认复制内容

                    // 获取点击坐标
                    const rect = chartContainer.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    // 将点击坐标转换为数据坐标
                    if (chart) {
                        const pointInGrid = chart.convertFromPixel({seriesIndex: 0}, [x, y]);
                        console.log('转换后的数据坐标:', pointInGrid);

                        if (pointInGrid && pointInGrid[0] >= 0 && pointInGrid[0] < timestamps.length) {
                            const dataIndex = Math.floor(pointInGrid[0]);
                            const timestamp = timestamps[dataIndex];

                            if (timedata[timestamp]) {
                                console.log('右键点击K线:', timestamp, 'dataIndex:', dataIndex);

                                try {
                                    // 模拟params对象
                                    const mockParams = {
                                        componentType: 'series',
                                        seriesType: 'candlestick',
                                        seriesIndex: 0,
                                        dataIndex: dataIndex,
                                        data: [timestamp, ...timedata[timestamp].kline],
                                        name: timestamp
                                    };

                                    // 获取tooltip配置
                                    const option = chart.getOption();
                                    const tooltipFormatter = option.tooltip[0].formatter;

                                    // 调用formatter函数获取格式化内容
                                    const tooltipContent = tooltipFormatter([mockParams]);

                                    // 将HTML转换为纯文本
                                    const tempDiv = document.createElement('div');
                                    tempDiv.innerHTML = tooltipContent;
                                    textToCopy = tempDiv.innerText || tempDiv.textContent;

                                    console.log('复制tooltip内容长度:', textToCopy.length);

                                    // 执行复制
                                    copyToClipboard(textToCopy, false);
                                    return; // 提前返回，避免执行下面的默认复制
                                } catch (error) {
                                    console.error('获取tooltip内容失败:', error);
                                    // 降级方案：复制基本信息
                                    const dataPoint = timedata[timestamp];
                                    textToCopy = `时间: ${timestamp}\n开盘: ${dataPoint.kline[0]}\n收盘: ${dataPoint.kline[1]}\n最高: ${dataPoint.kline[3]}\n最低: ${dataPoint.kline[2]}`;
                                    copyToClipboard(textToCopy, false);
                                    return;
                                }
                            }
                        }
                    }

                    // 默认复制 showDetailModal
                    console.log('复制默认内容:', textToCopy);
                    copyToClipboard(textToCopy, true);
                });

                // 绑定事件 - 点击显示详细信息
                chart.on('click', function(params) {
                    // 如果点击的是委托标记
                    if (params.seriesIndex === 3 && params.componentSubType === 'scatter') {
                        console.log('点击了委托标记:', params);

                        // 获取委托数据
                        const orderData = params.data?.orderData || params.data;
                        console.log('委托数据:', orderData);

                        if (orderData) {
                            // 格式化时间，确保显示到秒
                            const formatTimestamp = (timestamp) => {
                                if (!timestamp) return '无时间数据';
                                // 确保时间包含秒
                                if (timestamp.length >= 19) {
                                    return timestamp; // 已经包含秒
                                } else if (timestamp.length === 16) {
                                    return timestamp + ':00'; // 添加秒
                                } else {
                                    return timestamp; // 其他格式保持原样
                                }
                            };

                            // 使用委托数据中的直接利润值
                            let profit = orderData.profit_loss ? parseFloat(orderData.profit_loss) : 0;
                            let profitPercentage = orderData.profit_percentage ? parseFloat(orderData.profit_percentage) : 0;
                            let profitClass = profit >= 0 ? 'profit-positive' : 'profit-negative';

                            // 如果没有直接提供利润百分比，则计算
                            if (profit !== 0 && !profitPercentage && orderData.price && orderData.filled_size) {
                                const orderValue = parseFloat(orderData.price) * parseFloat(orderData.filled_size);
                                if (orderValue > 0) {
                                    profitPercentage = (Math.abs(profit) / orderValue) * 100;
                                }
                            }

                            // 委托价值
                            const orderValue = orderData.order_value ?
                                parseFloat(orderData.order_value) :
                                (orderData.price && orderData.size ? parseFloat(orderData.price) * parseFloat(orderData.size) *1000: 0);

                            // 创建弹窗显示详细委托信息
                            const orderInfo = `
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h2>委托详情</h2>
                                        <span class="close">&times;</span>
                                    </div>
                                    <div class="modal-body">
                                        <div class="modal-section">
                                            <div class="section-title">基本信息</div>
                                            <div class="data-row">
                                                <div class="data-label">时间</div>
                                                <div class="data-value">${formatTimestamp(orderData.original_timestamp)}</div>
                                            </div>
                                            <div class="data-row">
                                                <div class="data-label">方向</div>
                                                <div class="data-value">${orderData.direction_cn || (orderData.side === 'buy' ? '买入' : '卖出')}</div>
                                            </div>
                                            <div class="data-row">
                                                <div class="data-label">类型</div>
                                                <div class="data-value">${orderData.order_type_cn || orderData.order_type || '限价单'}</div>
                                            </div>
                                            <div class="data-row">
                                                <div class="data-label">状态</div>
                                                <div class="data-value">${orderData.state_cn || orderData.state}</div>
                                            </div>
                                            <div class="data-row">
                                                <div class="data-label">委托价格</div>
                                                <div class="data-value">${parseFloat(orderData.price).toFixed(5)}</div>
                                            </div>
                                            <div class="data-row">
                                                <div class="data-label">委托数量</div>
                                                <div class="data-value">${parseFloat(orderData.size).toFixed(5)}</div>
                                            </div>
                                            <div class="data-row">
                                                <div class="data-label">委托价值</div>
                                                <div class="data-value">${orderValue.toFixed(5)} USDT</div>
                                            </div>
                                            ${orderData.client_order_id ? `
                                            <div class="data-row">
                                                <div class="data-label">客户端订单ID</div>
                                                <div class="data-value">${orderData.client_order_id}</div>
                                            </div>` : ''}
                                            ${orderData.order_id ? `
                                            <div class="data-row">
                                                <div class="data-label">订单ID</div>
                                                <div class="data-value">${orderData.order_id}</div>
                                            </div>` : ''}
                                        </div>

                                        ${(orderData.filled_price || orderData.filled_size) ? `
                                        <div class="modal-section">
                                            <div class="section-title">成交信息</div>
                                            ${orderData.filled_price ? `
                                            <div class="data-row">
                                                <div class="data-label">成交价格</div>
                                                <div class="data-value">${parseFloat(orderData.filled_price).toFixed(5)}</div>
                                            </div>` : ''}
                                            ${orderData.filled_size ? `
                                            <div class="data-row">
                                                <div class="data-label">成交数量</div>
                                                <div class="data-value">${parseFloat(orderData.filled_size).toFixed(5)}</div>
                                            </div>` : ''}
                                            ${orderData.filled_price && orderData.filled_size ? `
                                            <div class="data-row">
                                                <div class="data-label">成交金额</div>
                                                <div class="data-value">${(parseFloat(orderData.filled_price) * parseFloat(orderData.filled_size)).toFixed(5)} USDT</div>
                                            </div>` : ''}
                                            ${orderData.profit_loss || profit !== 0 ? `
                                            <div class="data-row">
                                                <div class="data-label">交易利润</div>
                                                <div class="data-value ${profitClass}">${profit.toFixed(5)} USDT ${profitPercentage ? `(${profitPercentage.toFixed(2)}%)` : ''}</div>
                                            </div>` : ''}
                                            ${orderData.fee ? `
                                            <div class="data-row">
                                                <div class="data-label">手续费</div>
                                                <div class="data-value">${parseFloat(orderData.fee).toFixed(5)} ${orderData.fee_currency || 'USDT'}</div>
                                            </div>` : ''}
                                            ${orderData.rebate ? `
                                            <div class="data-row">
                                                <div class="data-label">返佣</div>
                                                <div class="data-value">${parseFloat(orderData.rebate).toFixed(5)} ${orderData.rebate_currency || 'USDT'}</div>
                                            </div>` : ''}
                                            ${orderData.filled_time ? `
                                            <div class="data-row">
                                                <div class="data-label">成交时间</div>
                                                <div class="data-value">${formatTimestamp(orderData.filled_time)}</div>
                                            </div>` : ''}
                                        </div>` : ''}

                                        ${orderData.position_side || orderData.lever || orderData.margin_mode ? `
                                        <div class="modal-section">
                                            <div class="section-title">交易设置</div>
                                            ${orderData.position_side ? `
                                            <div class="data-row">
                                                <div class="data-label">持仓方向</div>
                                                <div class="data-value">${orderData.position_side}</div>
                                            </div>` : ''}
                                            ${orderData.lever ? `
                                            <div class="data-row">
                                                <div class="data-label">杠杆倍数</div>
                                                <div class="data-value">${orderData.lever}x</div>
                                            </div>` : ''}
                                            ${orderData.margin_mode ? `
                                            <div class="data-row">
                                                <div class="data-label">保证金模式</div>
                                                <div class="data-value">${orderData.margin_mode === 'cross' ? '全仓' : '逐仓'}</div>
                                            </div>` : ''}
                                        </div>` : ''}

                                        ${orderData.cancel_time || orderData.update_time || orderData.create_time ? `
                                        <div class="modal-section">
                                            <div class="section-title">时间信息</div>
                                            ${orderData.create_time ? `
                                            <div class="data-row">
                                                <div class="data-label">创建时间</div>
                                                <div class="data-value">${formatTimestamp(orderData.create_time)}</div>
                                            </div>` : ''}
                                            ${orderData.update_time ? `
                                            <div class="data-row">
                                                <div class="data-label">更新时间</div>
                                                <div class="data-value">${formatTimestamp(orderData.update_time)}</div>
                                            </div>` : ''}
                                            ${orderData.cancel_time ? `
                                            <div class="data-row">
                                                <div class="data-label">撤单时间</div>
                                                <div class="data-value">${formatTimestamp(orderData.cancel_time)}</div>
                                            </div>` : ''}
                                        </div>` : ''}
                                    </div>
                                </div>
                            `;

                            // 显示弹窗
                            const modal = document.getElementById('detailModal');
                            if (!modal) {
                                console.error('找不到detailModal元素!');
                                return;
                            }
                            console.log('将显示弹窗:', orderInfo);
                            modal.innerHTML = orderInfo;
                            modal.style.display = 'block';
                            console.log('弹窗已显示');

                            // 绑定关闭按钮事件
                            const closeBtn = modal.querySelector('.close');
                            closeBtn.onclick = function() {
                                modal.style.display = 'none';
                            };

                            // 点击弹窗外部关闭
                            window.onclick = function(event) {
                                if (event.target === modal) {
                                    modal.style.display = 'none';
                                }
                            };

                            return;
                        }
                    }

                    // 如果点击的是X轴
                    if (params.componentType === 'xAxis') {
                        const index = params.dataIndex;
                        if (index >= 0 && index < timestamps.length) {
                            const timestamp = timestamps[index];
                            console.log('Clicked on timestamp:', timestamp);

                            // 显示该时间点的详细信息
                            if (timedata[timestamp]) {
                                const dataPoint = timedata[timestamp];
                                console.log('Data point:', dataPoint);

                                // 在这里可以添加显示详细信息的逻辑
                                const detailModal = document.getElementById('detailModal');
                                if (detailModal) {
                                    showDetailModal(timestamp, dataPoint);
                                }
                            }
                        }
                    }
                });

                // 初始化智能交易箭头显示控制 - 延迟确保图表完全初始化
                setTimeout(() => {
                    // 检查初始视图范围，决定是否显示交易箭头
                    const initialRange = getCurrentVisibleTimeRange();
                    if (initialRange && initialRange.durationHours <= 24) {
                        checkAndLoadTradeData();
                    } else {
                        updateTradeDataStatus('范围>1天，已隐藏', 'hidden');
                        hideTradeMarkers();
                        console.log(`初始范围>1天 (${initialRange ? initialRange.durationHours.toFixed(1) : '?'}h)，隐藏交易箭头标注`);
                    }
                }, 2000); // 延迟2秒，给大数据集更多时间初始化

                // 返回已创建的图表实例
                return chart;
            }

            /**
             * 加载策略日志数据
             * @param {string} strategyId - 策略ID
             * @param {boolean} isLive - 是否实盘交易
             * @returns {Promise<Object>} - 策略日志数据
             */
            async function loadStrategyLog(strategyId, isLive = false) {
                try {
                    console.log(`加载策略日志: 策略ID=${strategyId}, 实盘=${isLive}`);
                    const data = await logParser.loadLogFile(strategyId, isLive);
                    strategyLogData = data;
                    console.log('策略日志数据加载成功:', data);
                    return data;
                } catch (error) {
                    console.error('加载策略日志失败:', error);
                    showToast(`加载策略日志失败: ${error.message}`, 'warning');
                    return null;
                }
            }

            // 图表控制函数
            function zoomIn() {
                if (!chart) return;
                const option = chart.getOption();
                const dataZoom = option.dataZoom[0];

                // 获取当前范围
                const currentStart = dataZoom.start;
                const currentEnd = dataZoom.end;
                const currentRange = currentEnd - currentStart;
                const center = (currentStart + currentEnd) / 2;

                // 放大：减少显示范围，保持中心点
                const zoomFactor = 0.7; // 缩小到70%
                // 移除最小范围限制，允许无限放大
                const newRange = currentRange * zoomFactor;
                const newStart = Math.max(0, center - newRange / 2);
                const newEnd = Math.min(100, center + newRange / 2);

                console.log(`🔍 放大: ${currentStart.toFixed(3)}%-${currentEnd.toFixed(3)}% (${currentRange.toFixed(3)}%) → ${newStart.toFixed(3)}%-${newEnd.toFixed(3)}% (${newRange.toFixed(3)}%)`);

                chart.dispatchAction({
                    type: 'dataZoom',
                    start: newStart,
                    end: newEnd
                });
            }

            function zoomOut() {
                if (!chart) return;
                const option = chart.getOption();
                const dataZoom = option.dataZoom[0];

                // 获取当前范围
                const currentStart = dataZoom.start;
                const currentEnd = dataZoom.end;
                const currentRange = currentEnd - currentStart;
                const center = (currentStart + currentEnd) / 2;

                // 缩小：增加显示范围，保持中心点
                const zoomFactor = 1.5; // 扩大到150%
                const newRange = Math.min(100, currentRange * zoomFactor);
                let newStart = center - newRange / 2;
                let newEnd = center + newRange / 2;

                // 处理边界情况
                if (newStart < 0) {
                    newStart = 0;
                    newEnd = Math.min(100, newRange);
                }
                if (newEnd > 100) {
                    newEnd = 100;
                    newStart = Math.max(0, 100 - newRange);
                }

                console.log(`🔍 缩小: ${currentStart.toFixed(3)}%-${currentEnd.toFixed(3)}% (${currentRange.toFixed(3)}%) → ${newStart.toFixed(3)}%-${newEnd.toFixed(3)}% (${newRange.toFixed(3)}%)`);

                chart.dispatchAction({
                    type: 'dataZoom',
                    start: newStart,
                    end: newEnd
                });
            }

            function panLeft() {
                if (!chart) return;
                const option = chart.getOption();
                const dataZoom = option.dataZoom[0];
                const range = dataZoom.end - dataZoom.start;

                // 按当前显示范围的100%移动（即移动一个完整的当前视图范围）
                const step = range;
                const newStart = Math.max(0, dataZoom.start - step);
                const newEnd = newStart + range;

                console.log(`向左移动: 当前范围=${range.toFixed(2)}%, 移动到 ${newStart.toFixed(2)}%-${newEnd.toFixed(2)}%`);

                chart.dispatchAction({
                    type: 'dataZoom',
                    start: newStart,
                    end: newEnd
                });
            }

            function panRight() {
                if (!chart) return;
                const option = chart.getOption();
                const dataZoom = option.dataZoom[0];
                const range = dataZoom.end - dataZoom.start;

                // 按当前显示范围的100%移动（即移动一个完整的当前视图范围）
                const step = range;
                const newEnd = Math.min(100, dataZoom.end + step);
                const newStart = Math.max(0, newEnd - range);

                console.log(`向右移动: 当前范围=${range.toFixed(2)}%, 移动到 ${newStart.toFixed(2)}%-${newEnd.toFixed(2)}%`);

                chart.dispatchAction({
                    type: 'dataZoom',
                    start: newStart,
                    end: newEnd
                });
            }

            function resetZoom() {
                if (!chart) return;
                const endValue = calculateOneHourRange(timestamps) || 100;
                console.log(`重置视图: 0% - ${endValue.toFixed(2)}%`);

                chart.dispatchAction({
                    type: 'dataZoom',
                    start: 0,
                    end: endValue
                });
            }

            function fitToData() {
                if (!chart) return;
                console.log('适应数据: 显示全部数据 0% - 100%');

                chart.dispatchAction({
                    type: 'dataZoom',
                    start: 0,
                    end: 100
                });
            }

            // 跳到上个交易
            function jumpToPrevTrade() {
                if (!chart || !timestamps || !timedata) return;

                const option = chart.getOption();
                const dataZoom = option.dataZoom[0];
                const currentCenter = (dataZoom.start + dataZoom.end) / 2;
                const currentIndex = Math.floor(currentCenter / 100 * timestamps.length);

                // 从当前位置向前查找交易
                for (let i = currentIndex - 1; i >= 0; i--) {
                    const timestamp = timestamps[i];
                    if (timedata[timestamp] && timedata[timestamp].trade) {
                        jumpToIndex(i);
                        showToast(`跳转到交易: ${timestamp}`, 'success');
                        return;
                    }
                }

                showToast('没有找到更早的交易', 'warning');
            }

            // 跳到下个交易
            function jumpToNextTrade() {
                if (!chart || !timestamps || !timedata) return;

                const option = chart.getOption();
                const dataZoom = option.dataZoom[0];
                const currentCenter = (dataZoom.start + dataZoom.end) / 2;
                const currentIndex = Math.floor(currentCenter / 100 * timestamps.length);

                // 从当前位置向后查找交易
                for (let i = currentIndex + 1; i < timestamps.length; i++) {
                    const timestamp = timestamps[i];
                    if (timedata[timestamp] && timedata[timestamp].trade) {
                        jumpToIndex(i);
                        showToast(`跳转到交易: ${timestamp}`, 'success');
                        return;
                    }
                }

                showToast('没有找到更晚的交易', 'warning');
            }

            // 跳到开始，保留当前时间宽度
            function jumpToStart() {
                if (!chart) return;

                // 获取当前的时间范围宽度
                const option = chart.getOption();
                const dataZoom = option.dataZoom[0];
                const currentRangeSize = dataZoom.end - dataZoom.start;

                console.log(`跳转到开始，保持宽度 ${currentRangeSize.toFixed(1)}%`);

                chart.dispatchAction({
                    type: 'dataZoom',
                    start: 0,
                    end: currentRangeSize
                });

                showToast('已跳转到开始位置', 'success');
            }

            // 跳到结尾，保留当前时间宽度
            function jumpToEnd() {
                if (!chart) return;

                // 获取当前的时间范围宽度
                const option = chart.getOption();
                const dataZoom = option.dataZoom[0];
                const currentRangeSize = dataZoom.end - dataZoom.start;

                console.log(`跳转到结尾，保持宽度 ${currentRangeSize.toFixed(1)}%`);

                chart.dispatchAction({
                    type: 'dataZoom',
                    start: 100 - currentRangeSize,
                    end: 100
                });

                showToast('已跳转到结尾位置', 'success');
            }

            // 跳转到指定索引位置，保留当前时间宽度
            function jumpToIndex(index) {
                if (!chart || !timestamps) return;

                // 获取当前的时间范围宽度
                const option = chart.getOption();
                const dataZoom = option.dataZoom[0];
                const currentRangeSize = dataZoom.end - dataZoom.start; // 保留当前宽度

                const percentage = (index / timestamps.length) * 100;

                let start = percentage - currentRangeSize / 2;
                let end = percentage + currentRangeSize / 2;

                // 确保范围在有效区间内
                if (start < 0) {
                    start = 0;
                    end = currentRangeSize;
                } else if (end > 100) {
                    end = 100;
                    start = 100 - currentRangeSize;
                }

                console.log(`跳转到索引 ${index}, 保持宽度 ${currentRangeSize.toFixed(1)}%, 范围 [${start.toFixed(1)}%, ${end.toFixed(1)}%]`);

                chart.dispatchAction({
                    type: 'dataZoom',
                    start: start,
                    end: end
                });
            }

            // 显示时间选择器
            function showTimeSelector() {
                const modal = document.getElementById('timeSelectorModal');
                const timeInput = document.getElementById('timeInput');

                // 设置当前时间为默认值
                if (timestamps && timestamps.length > 0) {
                    const option = chart.getOption();
                    const dataZoom = option.dataZoom[0];
                    const currentCenter = (dataZoom.start + dataZoom.end) / 2;
                    const currentIndex = Math.floor(currentCenter / 100 * timestamps.length);
                    const currentTimestamp = timestamps[currentIndex];

                    // 转换为datetime-local格式
                    const date = new Date(currentTimestamp);
                    const localDateTime = new Date(date.getTime() - date.getTimezoneOffset() * 60000)
                        .toISOString().slice(0, 16);
                    timeInput.value = localDateTime;
                }

                modal.style.display = 'block';
            }

            // 关闭时间选择器
            function closeTimeSelector() {
                const modal = document.getElementById('timeSelectorModal');
                modal.style.display = 'none';
            }

            // 设置快捷时间
            function setQuickTime(type) {
                const timeInput = document.getElementById('timeInput');

                if (!timestamps || timestamps.length === 0) return;

                let targetTimestamp = '';

                switch(type) {
                    case 'start':
                        targetTimestamp = timestamps[0];
                        break;
                    case 'end':
                        targetTimestamp = timestamps[timestamps.length - 1];
                        break;
                    case 'firstTrade':
                        // 查找第一个交易
                        for (let i = 0; i < timestamps.length; i++) {
                            const timestamp = timestamps[i];
                            if (timedata[timestamp] && timedata[timestamp].trade) {
                                targetTimestamp = timestamp;
                                break;
                            }
                        }
                        break;
                    case 'lastTrade':
                        // 查找最后一个交易
                        for (let i = timestamps.length - 1; i >= 0; i--) {
                            const timestamp = timestamps[i];
                            if (timedata[timestamp] && timedata[timestamp].trade) {
                                targetTimestamp = timestamp;
                                break;
                            }
                        }
                        break;
                }

                if (targetTimestamp) {
                    const date = new Date(targetTimestamp);
                    const localDateTime = new Date(date.getTime() - date.getTimezoneOffset() * 60000)
                        .toISOString().slice(0, 16);
                    timeInput.value = localDateTime;
                } else {
                    showToast(`未找到${type === 'firstTrade' ? '第一个' : '最后一个'}交易`, 'warning');
                }
            }

            // 跳转到选择的时间
            function jumpToSelectedTime() {
                const timeInput = document.getElementById('timeInput');
                const selectedTime = timeInput.value;

                if (!selectedTime) {
                    showToast('请选择时间', 'warning');
                    return;
                }

                // 转换为时间戳格式
                const selectedDate = new Date(selectedTime);
                const targetTimestamp = selectedDate.toISOString().slice(0, 19).replace('T', ' ');

                // 查找最接近的时间点
                let closestIndex = 0;
                let minDiff = Math.abs(new Date(timestamps[0]).getTime() - selectedDate.getTime());

                for (let i = 1; i < timestamps.length; i++) {
                    const diff = Math.abs(new Date(timestamps[i]).getTime() - selectedDate.getTime());
                    if (diff < minDiff) {
                        minDiff = diff;
                        closestIndex = i;
                    }
                }

                // 跳转到最接近的时间点
                jumpToIndex(closestIndex);
                closeTimeSelector();

                const actualTimestamp = timestamps[closestIndex];
                showToast(`已跳转到最接近的时间: ${actualTimestamp}`, 'success');
            }

            function showTimeRange(range) {
                if (!chart || !timestamps || timestamps.length === 0) return;

                // 获取当前视图的中心点
                const option = chart.getOption();
                const dataZoom = option.dataZoom[0];
                const currentCenter = (dataZoom.start + dataZoom.end) / 2;

                let start = 0;
                let end = 100;
                let targetRange = 0;

                // 计算目标时间范围的百分比
                switch(range) {
                    case '15m':
                        targetRange = calculateTimeRange(timestamps, 15); // 15分钟
                        break;
                    case '30m':
                        targetRange = calculateTimeRange(timestamps, 30); // 30分钟
                        break;
                    case '1h':
                        targetRange = calculateTimeRange(timestamps, 60); // 1小时
                        break;
                    case '4h':
                        targetRange = calculateTimeRange(timestamps, 4 * 60); // 4小时
                        break;
                    case '12h':
                        targetRange = calculateTimeRange(timestamps, 12 * 60); // 12小时
                        break;
                    case '1d':
                        targetRange = calculateTimeRange(timestamps, 24 * 60); // 1天
                        break;
                    case '3d':
                        targetRange = calculateTimeRange(timestamps, 3 * 24 * 60); // 3天
                        break;
                    case 'all':
                        start = 0;
                        end = 100;
                        break;
                }

                // 如果不是显示全部，以当前中心点为基准设置新范围
                if (range !== 'all') {
                    start = Math.max(0, currentCenter - targetRange / 2);
                    end = Math.min(100, currentCenter + targetRange / 2);

                    // 如果超出边界，调整到边界
                    if (end > 100) {
                        end = 100;
                        start = Math.max(0, end - targetRange);
                    }
                    if (start < 0) {
                        start = 0;
                        end = Math.min(100, start + targetRange);
                    }
                }

                console.log(`显示时间范围 ${range}: 以当前中心${currentCenter.toFixed(1)}%为基准，范围${start.toFixed(2)}%-${end.toFixed(2)}%`);

                chart.dispatchAction({
                    type: 'dataZoom',
                    start: start,
                    end: end
                });
            }

            // 计算指定分钟数对应的百分比范围
            function calculateTimeRange(timestamps, minutes) {
                if (!timestamps || timestamps.length === 0) return 100;

                // 假设每个时间戳间隔1分钟
                const targetDataPoints = minutes;
                const totalDataPoints = timestamps.length;
                const percentage = Math.min(100, (targetDataPoints / totalDataPoints) * 100);

                return percentage;
            }

            // 从最新数据开始计算指定分钟数的结束位置
            function calculateTimeRangeFromEnd(timestamps, minutes) {
                if (!timestamps || timestamps.length === 0) return 100;

                // 计算从最新数据点开始的百分比
                const targetDataPoints = minutes;
                const totalDataPoints = timestamps.length;

                // 如果目标数据点数大于等于总数据点数，显示全部
                if (targetDataPoints >= totalDataPoints) {
                    return 100;
                }

                // 从最新数据开始，计算百分比
                return 100; // 总是显示到最新数据
            }

            // 切换快捷键帮助显示
            function toggleKeyboardHelp() {
                const helpDiv = document.getElementById('keyboardHelp');
                if (helpDiv) {
                    helpDiv.style.display = helpDiv.style.display === 'none' ? 'block' : 'none';
                    // 隐藏其他帮助
                    const brushHelp = document.getElementById('brushHelp');
                    if (brushHelp && helpDiv.style.display === 'block') {
                        brushHelp.style.display = 'none';
                    }
                }
            }

            // 切换矩形选择帮助显示
            function toggleBrushHelp() {
                const helpDiv = document.getElementById('brushHelp');
                if (helpDiv) {
                    helpDiv.style.display = helpDiv.style.display === 'none' ? 'block' : 'none';
                    // 隐藏其他帮助
                    const keyboardHelp = document.getElementById('keyboardHelp');
                    if (keyboardHelp && helpDiv.style.display === 'block') {
                        keyboardHelp.style.display = 'none';
                    }
                }
            }

            // 处理矩形选择区域
            function handleBrushArea(area) {
                console.log('处理矩形选择区域:', area);
                console.log('area详细结构:', JSON.stringify(area, null, 2));

                if (!area || !timestamps || timestamps.length === 0) {
                    console.warn('无效的选择区域或数据');
                    return;
                }

                try {
                    let startIndex, endIndex, minPrice, maxPrice;

                    // 检查不同的坐标格式
                    if (area.coordRange && Array.isArray(area.coordRange)) {
                        // 格式1: coordRange数组
                        const coordRange = area.coordRange;
                        const xRange = coordRange[0]; // [startX, endX] - 时间索引
                        const yRange = coordRange[1]; // [startY, endY] - 价格值

                        console.log('坐标范围 (coordRange):', { xRange, yRange });
                        console.log('检测到的数据类型:', {
                            xRange类型: typeof xRange[0],
                            yRange类型: typeof yRange[0],
                            xRange值: xRange,
                            yRange值: yRange
                        });

                        // 检查yRange是否已经是价格值
                        if (typeof yRange[0] === 'number' && yRange[0] > 0 && yRange[0] < 10) {
                            // yRange看起来已经是价格值，直接使用
                            console.log('yRange已经是价格值，直接使用');
                            minPrice = Math.min(yRange[0], yRange[1]);
                            maxPrice = Math.max(yRange[0], yRange[1]);

                            // 时间索引也可能已经是数据索引
                            if (typeof xRange[0] === 'number' && xRange[0] >= 0 && xRange[0] < timestamps.length) {
                                startIndex = Math.floor(Math.min(xRange[0], xRange[1]));
                                endIndex = Math.floor(Math.max(xRange[0], xRange[1]));
                            } else {
                                // 需要转换时间索引
                                const startDataCoord = chart.convertFromPixel({ seriesIndex: 0 }, [xRange[0], 0]);
                                const endDataCoord = chart.convertFromPixel({ seriesIndex: 0 }, [xRange[1], 0]);

                                if (startDataCoord && endDataCoord) {
                                    startIndex = Math.max(0, Math.floor(Math.min(startDataCoord[0], endDataCoord[0])));
                                    endIndex = Math.min(timestamps.length - 1, Math.floor(Math.max(startDataCoord[0], endDataCoord[0])));
                                }
                            }
                        } else {
                            // yRange是像素坐标，需要转换
                            console.log('yRange是像素坐标，进行转换');
                            const startDataCoord = chart.convertFromPixel({ seriesIndex: 0 }, [xRange[0], yRange[1]]);
                            const endDataCoord = chart.convertFromPixel({ seriesIndex: 0 }, [xRange[1], yRange[0]]);

                            if (!startDataCoord || !endDataCoord) {
                                console.warn('坐标转换失败');
                                return;
                            }

                            // 获取时间范围索引
                            startIndex = Math.max(0, Math.floor(Math.min(startDataCoord[0], endDataCoord[0])));
                            endIndex = Math.min(timestamps.length - 1, Math.floor(Math.max(startDataCoord[0], endDataCoord[0])));

                            // 获取价格范围
                            minPrice = Math.min(startDataCoord[1], endDataCoord[1]);
                            maxPrice = Math.max(startDataCoord[1], endDataCoord[1]);
                        }

                        // 确保索引有效
                        if (startIndex >= endIndex) {
                            endIndex = Math.max(startIndex + 1, Math.min(timestamps.length - 1, startIndex + 1));
                        }

                        console.log('矩形Y轴边界价格:', {
                            minPrice: minPrice.toFixed(5),
                            maxPrice: maxPrice.toFixed(5),
                            yRange: yRange,
                            note: '这是矩形框Y轴边界对应的实际价格，与K线数据无关'
                        });

                        console.log('转换后的索引:', { startIndex, endIndex });
                        console.log('对应的时间:', {
                            startTime: timestamps[startIndex],
                            endTime: timestamps[endIndex]
                        });

                    } else if (area.range && Array.isArray(area.range)) {
                        // 格式2: range数组 [xMin, xMax, yMin, yMax]
                        const range = area.range;
                        console.log('坐标范围 (range):', range);

                        startIndex = Math.max(0, Math.floor(Math.min(range[0], range[1])));
                        endIndex = Math.min(timestamps.length - 1, Math.ceil(Math.max(range[0], range[1])));
                        minPrice = Math.min(range[2], range[3]);
                        maxPrice = Math.max(range[2], range[3]);

                    } else {
                        console.warn('未知的区域格式:', area);
                        return;
                    }

                    // 验证数据有效性
                    if (isNaN(startIndex) || isNaN(endIndex) || isNaN(minPrice) || isNaN(maxPrice)) {
                        console.warn('计算出的数据包含NaN:', { startIndex, endIndex, minPrice, maxPrice });
                        return;
                    }

                    if (startIndex >= endIndex) {
                        console.warn('无效的时间范围:', { startIndex, endIndex });
                        // 如果索引相同，至少包含一个数据点
                        if (startIndex === endIndex && startIndex < timestamps.length) {
                            endIndex = Math.min(timestamps.length - 1, startIndex + 1);
                        } else {
                            return;
                        }
                    }

                    if (minPrice >= maxPrice) {
                        console.warn('无效的价格范围:', { minPrice, maxPrice });
                        // 如果价格相同，给一个小的差值
                        if (Math.abs(minPrice - maxPrice) < 0.00001) {
                            const avgPrice = (minPrice + maxPrice) / 2;
                            minPrice = avgPrice * 0.999;
                            maxPrice = avgPrice * 1.001;
                        } else {
                            return;
                        }
                    }
                    // debugger
                    console.log('最终选择范围:', {
                        startIndex,
                        endIndex,
                        minPrice,
                        maxPrice,
                        startTime: timestamps[startIndex],
                        endTime: timestamps[endIndex],
                        dataPointCount: endIndex - startIndex + 1
                    });

                    // 分析选择区域内的数据
                    const analysisResult = analyzeSelectedArea(startIndex, endIndex, minPrice, maxPrice);

                    // 显示分析结果
                    showBrushAnalysisModal(analysisResult, area);

                } catch (error) {
                    console.error('处理矩形选择时出错:', error);
                    showToast('分析选择区域时出错: ' + error.message, 'error');
                }
            }

            // 处理刷选数据
            function handleBrushSelection(selection, brushComponent) {
                console.log('处理刷选数据:', selection, brushComponent);

                if (!selection.dataIndex || selection.dataIndex.length === 0) {
                    return;
                }

                // 获取选中的数据索引范围
                const startIndex = Math.min(...selection.dataIndex);
                const endIndex = Math.max(...selection.dataIndex);

                console.log('选中数据索引范围:', { startIndex, endIndex });

                // 对于数据选择（非矩形选择），我们需要获取图表的Y轴范围
                // 这种情况下没有明确的Y轴边界，所以使用当前图表的Y轴范围
                const option = chart.getOption();
                let minPrice = 0;
                let maxPrice = 1;

                if (option && option.yAxis && option.yAxis[0]) {
                    const yAxis = option.yAxis[0];
                    if (yAxis.min !== undefined && yAxis.max !== undefined) {
                        minPrice = yAxis.min;
                        maxPrice = yAxis.max;
                    }
                }

                console.log('使用图表Y轴范围:', { minPrice, maxPrice });

                // 使用完整的分析函数
                const analysisResult = analyzeSelectedArea(startIndex, endIndex, minPrice, maxPrice);

                // 显示分析结果
                showBrushAnalysisModal(analysisResult);
            }

            // 分析选择区域内的数据
            function analyzeSelectedArea(startIndex, endIndex, minPrice, maxPrice) {
                // 验证输入参数
                if (isNaN(startIndex) || isNaN(endIndex) || isNaN(minPrice) || isNaN(maxPrice)) {
                    console.error('分析参数包含NaN:', { startIndex, endIndex, minPrice, maxPrice });
                    return null;
                }

                if (startIndex < 0 || endIndex >= timestamps.length || startIndex >= endIndex) {
                    console.error('无效的索引范围:', { startIndex, endIndex, timestampsLength: timestamps.length });
                    return null;
                }

                const priceRange = maxPrice - minPrice;
                const rangePercent = minPrice > 0 ? ((priceRange / minPrice) * 100).toFixed(2) : '0.00';

                const dataPointCount = endIndex - startIndex + 1;

                const result = {
                    timeRange: {
                        start: timestamps[startIndex] || 'N/A',
                        end: timestamps[endIndex] || 'N/A',
                        duration: calculateTimeDuration(timestamps[startIndex], timestamps[endIndex])
                    },
                    priceRange: {
                        min: minPrice,
                        max: maxPrice,
                        range: priceRange,
                        rangePercent: rangePercent
                    },
                    klineAnalysis: null,
                    rectangleAnalysis: {
                        minPrice: minPrice,
                        maxPrice: maxPrice,
                        priceRange: priceRange,
                        rangePercent: rangePercent,
                        // Y轴基础信息
                        yAxisInfo: `基于矩形选择的Y轴范围，最高价${maxPrice.toFixed(5)}，最低价${minPrice.toFixed(5)}`
                    },
                    dataPointCount: dataPointCount,
                    debugInfo: {
                        startIndex: startIndex,
                        endIndex: endIndex,
                        timestampsLength: timestamps.length
                    }
                };

                // 分析矩形区域内的K线数据（用于K线统计）
                try {
                    result.klineAnalysis = analyzeKlineInRange(startIndex, endIndex);
                    console.log('Y轴矩形分析 - 价格范围:', { minPrice, maxPrice, priceRange, rangePercent });
                } catch (error) {
                    console.error('分析K线数据时出错:', error);
                    result.klineAnalysis = null;
                }

                return result;
            }



            // 分析指定范围内的K线数据
            function analyzeKlineInRange(startIndex, endIndex) {
                let klineHigh = -Infinity;
                let klineLow = Infinity;
                let startPrice = null;
                let endPrice = null;
                let totalVolume = 0;
                let candleCount = 0;
                let upCandles = 0;
                let downCandles = 0;

                // 验证输入参数
                if (isNaN(startIndex) || isNaN(endIndex) || startIndex < 0 || endIndex >= timestamps.length) {
                    console.error('无效的K线分析范围:', { startIndex, endIndex, timestampsLength: timestamps.length });
                    return null;
                }

                for (let i = startIndex; i <= endIndex; i++) {
                    const timestamp = timestamps[i];
                    if (!timestamp) continue;

                    const dataPoint = timedata[timestamp];

                    if (dataPoint && dataPoint.kline && Array.isArray(dataPoint.kline)) {
                        const [open, close, low, high] = dataPoint.kline;

                        // 验证K线数据
                        if (isNaN(open) || isNaN(close) || isNaN(low) || isNaN(high)) {
                            console.warn('K线数据包含NaN:', { timestamp, kline: dataPoint.kline });
                            continue;
                        }

                        // 记录第一根K线的开盘价作为起始价格
                        if (startPrice === null) {
                            startPrice = open;
                        }

                        // 记录最后一根K线的收盘价作为结束价格
                        endPrice = close;

                        // 更新最高最低价
                        klineHigh = Math.max(klineHigh, high);
                        klineLow = Math.min(klineLow, low);

                        // 累计成交量
                        const volume = dataPoint.volume || 0;
                        if (!isNaN(volume)) {
                            totalVolume += volume;
                        }

                        // 统计涨跌K线数量
                        candleCount++;
                        if (close > open) {
                            upCandles++;
                        } else if (close < open) {
                            downCandles++;
                        }
                    }
                }

                // 计算涨跌幅
                let priceChange = '0.00';
                if (startPrice && endPrice && !isNaN(startPrice) && !isNaN(endPrice) && startPrice > 0) {
                    priceChange = ((endPrice - startPrice) / startPrice * 100).toFixed(2);
                }

                let highLowRange = '0.00';
                if (klineHigh !== -Infinity && klineLow !== Infinity && !isNaN(klineHigh) && !isNaN(klineLow) && klineLow > 0) {
                    highLowRange = ((klineHigh - klineLow) / klineLow * 100).toFixed(2);
                }

                return {
                    startPrice: startPrice,
                    endPrice: endPrice,
                    highest: klineHigh !== -Infinity ? klineHigh : null,
                    lowest: klineLow !== Infinity ? klineLow : null,
                    priceChange: priceChange,
                    priceChangeAbs: Math.abs(parseFloat(priceChange) || 0).toFixed(2),
                    highLowRange: highLowRange,
                    totalVolume: totalVolume.toFixed(2),
                    candleCount: candleCount,
                    upCandles: upCandles,
                    downCandles: downCandles,
                    upCandlePercent: candleCount > 0 ? ((upCandles / candleCount) * 100).toFixed(1) : '0.0',
                    downCandlePercent: candleCount > 0 ? ((downCandles / candleCount) * 100).toFixed(1) : '0.0'
                };
            }

            // 拖拽功能
            function makeDraggable(element) {
                let isDragging = false;
                let startX, startY, startLeft, startTop;

                const header = element.querySelector('.modal-header');
                if (!header) {
                    // 如果没有header，整个元素都可以拖拽
                    element.style.cursor = 'move';
                    element.addEventListener('mousedown', dragStart);
                } else {
                    header.style.cursor = 'move';
                    header.addEventListener('mousedown', dragStart);
                }

                document.addEventListener('mousemove', drag);
                document.addEventListener('mouseup', dragEnd);

                function dragStart(e) {
                    // 避免在关闭按钮上开始拖拽
                    if (e.target.classList.contains('close') || e.target.classList.contains('close-btn')) {
                        return;
                    }

                    isDragging = true;
                    element.classList.add('dragging');

                    startX = e.clientX;
                    startY = e.clientY;
                    startLeft = parseInt(element.style.left) || 0;
                    startTop = parseInt(element.style.top) || 0;

                    e.preventDefault();
                    e.stopPropagation();
                }

                function drag(e) {
                    if (!isDragging) return;

                    e.preventDefault();

                    const deltaX = e.clientX - startX;
                    const deltaY = e.clientY - startY;

                    let newLeft = startLeft + deltaX;
                    let newTop = startTop + deltaY;

                    // 限制在屏幕范围内
                    const modalRect = element.getBoundingClientRect();
                    newLeft = Math.max(0, Math.min(newLeft, window.innerWidth - modalRect.width));
                    newTop = Math.max(0, Math.min(newTop, window.innerHeight - modalRect.height));

                    element.style.left = newLeft + 'px';
                    element.style.top = newTop + 'px';
                    element.style.right = 'auto';
                }

                function dragEnd() {
                    if (isDragging) {
                        isDragging = false;
                        element.classList.remove('dragging');
                    }
                }
            }

            // 计算时间持续时间
            function calculateTimeDuration(startTime, endTime) {
                try {
                    if (!startTime || !endTime) {
                        return '未知';
                    }

                    console.log('计算时间持续时间:', { startTime, endTime });

                    const start = new Date(startTime);
                    const end = new Date(endTime);

                    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
                        console.error('无效的时间格式:', { startTime, endTime });
                        return '时间格式错误';
                    }

                    const diffMs = end - start;
                    console.log('时间差毫秒:', diffMs);

                    if (diffMs < 0) {
                        console.warn('结束时间早于开始时间');
                        return '时间顺序错误';
                    }

                    const diffMinutes = Math.floor(diffMs / (1000 * 60));
                    const diffHours = Math.floor(diffMinutes / 60);
                    const diffDays = Math.floor(diffHours / 24);

                    console.log('时间差计算:', { diffMinutes, diffHours, diffDays });

                    if (diffDays > 0) {
                        return `${diffDays}天 ${diffHours % 24}小时 ${diffMinutes % 60}分钟`;
                    } else if (diffHours > 0) {
                        return `${diffHours}小时 ${diffMinutes % 60}分钟`;
                    } else if (diffMinutes > 0) {
                        return `${diffMinutes}分钟`;
                    } else {
                        return '不足1分钟';
                    }
                } catch (error) {
                    console.error('计算时间持续时间出错:', error, { startTime, endTime });
                    return '计算错误';
                }
            }

            // 显示矩形选择分析结果模态框
            function showBrushAnalysisModal(analysisResult, brushArea) {
                console.log('显示分析结果:', analysisResult);

                if (!analysisResult) {
                    showToast('分析结果无效', 'error');
                    return;
                }

                // 创建或获取模态框
                let modal = document.getElementById('brushAnalysisModal');
                if (!modal) {
                    modal = document.createElement('div');
                    modal.id = 'brushAnalysisModal';
                    modal.className = 'brush-analysis-modal';
                    document.body.appendChild(modal);

                    // 添加拖拽功能
                    makeDraggable(modal);
                }

                // 构建模态框内容
                let content = `
                    <div class="modal-content">
                        <div class="modal-header">
                            <h2>矩形选择区域分析</h2>
                            <span class="close" onclick="document.getElementById('brushAnalysisModal').style.display='none'">&times;</span>
                        </div>
                        <div class="modal-body">
                `;

                // 时间范围信息
                content += `
                    <div class="modal-section">
                        <div class="section-title">📅 时间范围</div>
                        <div class="data-row">
                            <div class="data-label">开始时间</div>
                            <div class="data-value">${analysisResult.timeRange.start || 'N/A'}</div>
                        </div>
                        <div class="data-row">
                            <div class="data-label">结束时间</div>
                            <div class="data-value">${analysisResult.timeRange.end || 'N/A'}</div>
                        </div>
                        <div class="data-row">
                            <div class="data-label">持续时间</div>
                            <div class="data-value">${analysisResult.timeRange.duration || 'N/A'}</div>
                        </div>
                        <div class="data-row">
                            <div class="data-label">数据点数</div>
                            <div class="data-value">${analysisResult.dataPointCount || 'N/A'}个</div>
                        </div>
                    </div>
                `;

                // K线分析（如果有）
                if (analysisResult.klineAnalysis) {
                    const kline = analysisResult.klineAnalysis;
                    const priceChange = parseFloat(kline.priceChange) || 0;
                    const priceChangeClass = priceChange >= 0 ? 'profit-positive' : 'profit-negative';

                    content += `
                        <div class="modal-section">
                            <div class="section-title">📊 K线数据分析</div>
                            <div class="data-row">
                                <div class="data-label">起始价格</div>
                                <div class="data-value">${kline.startPrice ? kline.startPrice.toFixed(5) : 'N/A'}</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">结束价格</div>
                                <div class="data-value">${kline.endPrice ? kline.endPrice.toFixed(5) : 'N/A'}</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">最高价</div>
                                <div class="data-value">${kline.highest ? kline.highest.toFixed(5) : 'N/A'}</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">最低价</div>
                                <div class="data-value">${kline.lowest ? kline.lowest.toFixed(5) : 'N/A'}</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">涨跌幅</div>
                                <div class="data-value ${priceChangeClass}">${kline.priceChange || '0.00'}%</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">最高最低价差</div>
                                <div class="data-value">${kline.highLowRange || '0.00'}%</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">总成交量</div>
                                <div class="data-value">${kline.totalVolume || '0.00'}</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">K线数量</div>
                                <div class="data-value">${kline.candleCount || 0}根</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">上涨K线</div>
                                <div class="data-value profit-positive">${kline.upCandles || 0}根 (${kline.upCandlePercent || '0.0'}%)</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">下跌K线</div>
                                <div class="data-value profit-negative">${kline.downCandles || 0}根 (${kline.downCandlePercent || '0.0'}%)</div>
                            </div>
                        </div>
                    `;
                }

                // 矩形区域价格分析（基于Y轴纯数值计算）
                if (analysisResult.rectangleAnalysis) {
                    const rect = analysisResult.rectangleAnalysis;

                    content += `
                        <div class="modal-section">
                            <div class="section-title">📐 矩形区域价格分析 (基于Y轴)</div>
                            <div class="data-row">
                                <div class="data-label">矩形最高价</div>
                                <div class="data-value">${(rect.maxPrice || 0).toFixed(5)}</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">矩形最低价</div>
                                <div class="data-value">${(rect.minPrice || 0).toFixed(5)}</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">价格区间</div>
                                <div class="data-value">${(rect.priceRange || 0).toFixed(5)}</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">区间幅度</div>
                                <div class="data-value">${rect.rangePercent || '0.00'}%</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">中位价格</div>
                                <div class="data-value">${((rect.maxPrice + rect.minPrice) / 2).toFixed(5)}</div>
                            </div>
                        </div>
                    `;
                }

                // 价格范围信息（如果有）
                if (analysisResult.priceRange) {
                    const price = analysisResult.priceRange;

                    content += `
                        <div class="modal-section">
                            <div class="section-title">💰 选择区域价格范围</div>
                            <div class="data-row">
                                <div class="data-label">最低价</div>
                                <div class="data-value">${price.min.toFixed(5)}</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">最高价</div>
                                <div class="data-value">${price.max.toFixed(5)}</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">价格区间</div>
                                <div class="data-value">${price.range.toFixed(5)}</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">区间百分比</div>
                                <div class="data-value">${price.rangePercent}%</div>
                            </div>
                        </div>
                    `;
                }

                content += `
                        </div>
                    </div>
                `;

                // 设置模态框内容并显示
                modal.innerHTML = content;

                // 智能定位模态框
                positionModalSmartly(modal, brushArea);

                modal.style.display = 'block';
                
                // 显示成功提示
                showToast('矩形选择区域分析完成', 'success');
            }

            // 智能定位模态框，避免遮挡矩形选择区域
            function positionModalSmartly(modal, brushArea) {
                const chartContainer = document.getElementById('chart');
                if (!chartContainer) return;

                const chartRect = chartContainer.getBoundingClientRect();
                const modalWidth = 600; // 模态框预估宽度
                const modalHeight = 400; // 模态框预估高度
                const padding = 20; // 边距
                // debugger
                let left, top;

                if (brushArea && brushArea.coordRange) {
                    // 如果有矩形区域信息，尝试放在矩形外面
                    const coordRange = brushArea.coordRange;
                    const rectLeft = coordRange[0][0];
                    const rectRight = coordRange[0][1];
                    const rectTop = coordRange[1][0];
                    const rectBottom = coordRange[1][1];

                    // 计算矩形在页面中的位置
                    const rectPageLeft = chartRect.left + rectLeft;
                    const rectPageRight = chartRect.left + rectRight;
                    const rectPageTop = chartRect.top + rectTop;
                    const rectPageBottom = chartRect.top + rectBottom;

                    // 尝试放在矩形右侧
                    if (rectPageRight + modalWidth + padding < window.innerWidth) {
                        left = rectPageRight + padding;
                        top = Math.max(padding, rectPageTop);
                    }
                    // 尝试放在矩形左侧
                    else if (rectPageLeft - modalWidth - padding > 0) {
                        left = rectPageLeft - modalWidth - padding;
                        top = Math.max(padding, rectPageTop);
                    }
                    // 尝试放在矩形下方
                    else if (rectPageBottom + modalHeight + padding < window.innerHeight) {
                        left = Math.max(padding, Math.min(rectPageLeft, window.innerWidth - modalWidth - padding));
                        top = rectPageBottom + padding;
                    }
                    // 尝试放在矩形上方
                    else if (rectPageTop - modalHeight - padding > 0) {
                        left = Math.max(padding, Math.min(rectPageLeft, window.innerWidth - modalWidth - padding));
                        top = rectPageTop - modalHeight - padding;
                    }
                    // 默认放在右上角
                    else {
                        left = window.innerWidth - modalWidth - padding;
                        top = padding;
                    }
                } else {
                    // 没有矩形信息，放在右上角
                    left = window.innerWidth - modalWidth - padding;
                    top = padding;
                }

                // 确保不超出屏幕边界
                left = Math.max(padding, Math.min(left, window.innerWidth - modalWidth - padding));
                top = Math.max(padding, Math.min(top, window.innerHeight - modalHeight - padding));

                modal.style.left = left + 'px';
                modal.style.top = top + 'px';
            }



            // 获取K线数据
            async function fetchData() {
                try {
                    const currency = 'DOGE'; // 默认使用DOGE
                    const startTime = ''; // 不使用时间限制，让后端决定
                    const endTime = '';
                    let strategyId = document.getElementById('strategyIdInput').value || document.getElementById('strategySelect').value;

                    // 如果没有从选择框获取到strategy_id，尝试从URL参数获取
                    if (!strategyId) {
                        const params = getUrlParams();
                        strategyId = params.strategy_id;
                        // 如果从URL获取到策略ID，更新到input框
                        if (strategyId) {
                            updateStrategyIdInput(strategyId);
                        }
                    }

                    // 如果有策略ID，尝试加载策略日志
                    if (strategyId) {
                        try {
                            // 默认使用非实盘数据
                            await loadStrategyLog(strategyId, false);
                        } catch (error) {
                            console.warn('加载策略日志失败，可能不存在:', error);
                        }
                    }

                    // 初始化策略日志对象
                    strategyLogs = {};

                    // 获取K线数据
                    const url = new URL('/api/kline', window.location.origin);
                    const params = {
                        currency_pair: currency,
                        interval: currentInterval,
                        start_time: startTime,
                        end_time: endTime
                    };

                    if (strategyId) {
                        params.strategy_id = strategyId;
                    }

                    Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));

                    console.log('Fetching kline data from URL:', url.toString());
                    const response = await fetch(url);
                    const result = await response.json();

                    if (result.code === 0 && result.data) {
                        console.log('Kline data received:', result.data);

                        // 检查是否需要加载委托数据
                        const loadOrderDataToggle = document.getElementById('loadOrderDataToggle');
                        const shouldLoadOrderData = loadOrderDataToggle && loadOrderDataToggle.checked;

                        if (shouldLoadOrderData) {
                            // 获取委托数据
                            try {
                                // 确保currency不为空
                                const currencyValue = currency || 'DOGE';
                                console.log('Using currency for order history:', currencyValue);

                            // 使用与K线数据相同的时间范围
                            // 从结果中提取第一个和最后一个时间戳
                            let validStartTime = startTime;
                            let validEndTime = endTime;

                            // 如果有K线数据，使用K线数据的时间范围
                            if (result.data && result.data.timedata) {
                                const timeKeys = Object.keys(result.data.timedata).sort();
                                if (timeKeys.length > 0) {
                                    validStartTime = timeKeys[0];
                                    validEndTime = timeKeys[timeKeys.length - 1];
                                    console.log('使用K线数据的时间范围:', validStartTime, '至', validEndTime);
                                }
                            } else {
                                // 如果没有K线数据，使用输入的时间范围
                                if (!validStartTime || validStartTime.trim() === '') {
                                    // 如果没有开始时间，使用当前时间前24小时
                                    const yesterday = new Date();
                                    yesterday.setDate(yesterday.getDate() - 1);
                                    validStartTime = yesterday.toISOString().split('T')[0] + ' 00:00:00';
                                    console.log('使用默认开始时间:', validStartTime);
                                }

                                if (!validEndTime || validEndTime.trim() === '') {
                                    // 如果没有结束时间，使用当前时间
                                    const now = new Date();
                                    validEndTime = now.toISOString().split('T')[0] + ' ' +
                                                now.getHours().toString().padStart(2, '0') + ':' +
                                                now.getMinutes().toString().padStart(2, '0') + ':' +
                                                now.getSeconds().toString().padStart(2, '0');
                                    console.log('使用默认结束时间:', validEndTime);
                                }
                            }

                            // 检查时间格式是否有效
                            try {
                                // 验证并格式化时间
                                const startDate = new Date(validStartTime);
                                const endDate = new Date(validEndTime);

                                // 检查是否是有效日期
                                if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                                    throw new Error('无效的日期格式');
                                }

                                // 检查是否是未来日期
                                const now = new Date();
                                if (startDate > now || endDate > now) {
                                    console.warn('检测到未来日期，将使用当前时间代替');
                                    if (startDate > now) {
                                        // 如果开始时间是未来时间，使用当前时间前24小时
                                        const yesterday = new Date(now);
                                        yesterday.setDate(yesterday.getDate() - 1);
                                        validStartTime = yesterday.toISOString().split('T')[0] + ' 00:00:00';
                                    }
                                    if (endDate > now) {
                                        // 如果结束时间是未来时间，使用当前时间
                                        validEndTime = now.toISOString().split('T')[0] + ' ' +
                                                    now.getHours().toString().padStart(2, '0') + ':' +
                                                    now.getMinutes().toString().padStart(2, '0') + ':' +
                                                    now.getSeconds().toString().padStart(2, '0');
                                    }
                                    console.log('修正后的时间范围:', validStartTime, '至', validEndTime);
                                }
                            } catch (e) {
                                console.error('时间格式验证失败:', e);
                                // 使用默认时间范围
                                const now = new Date();
                                const yesterday = new Date(now);
                                yesterday.setDate(yesterday.getDate() - 1);

                                validStartTime = yesterday.toISOString().split('T')[0] + ' 00:00:00';
                                validEndTime = now.toISOString().split('T')[0] + ' ' +
                                            now.getHours().toString().padStart(2, '0') + ':' +
                                            now.getMinutes().toString().padStart(2, '0') + ':' +
                                            now.getSeconds().toString().padStart(2, '0');
                                console.log('使用默认时间范围:', validStartTime, '至', validEndTime);
                            }

                            const orderUrl = new URL('/api/order_history', window.location.origin);
                            const orderParams = {
                                start_time: validStartTime,
                                end_time: validEndTime,
                                currency: currencyValue,
                                inst_id: currencyValue + '-USDT-SWAP'
                            };

                            console.log('Order params:', orderParams);
                            Object.keys(orderParams).forEach(key => orderUrl.searchParams.append(key, orderParams[key]));

                            const fullUrl = orderUrl.toString();
                            console.log('Fetching order history from URL:', fullUrl);
                            console.log('时间范围:', validStartTime, '至', validEndTime, '币种:', currencyValue);
                            const orderResponse = await fetch(orderUrl);
                            const orderResult = await orderResponse.json();

                            if (orderResult.code === 0 && orderResult.data) {
                                console.log('Order data received:', orderResult.data);
                                console.log('Order metadata:', orderResult.metadata);

                                // 检查委托数据结构
                                const orderDataKeys = Object.keys(orderResult.data);
                                console.log(`委托数据包含 ${orderDataKeys.length} 个分钟时间点`);

                                if (orderDataKeys.length > 0) {
                                    // 打印前几个时间点
                                    console.log('时间点样例:', orderDataKeys.slice(0, 5));

                                    // 打印第一个时间点的数据
                                    const firstKey = orderDataKeys[0];
                                    const firstMinuteOrders = orderResult.data[firstKey];
                                    console.log(`第一个时间点 ${firstKey} 有 ${firstMinuteOrders.length} 条委托`);

                                    if (firstMinuteOrders.length > 0) {
                                        console.log('第一条委托数据:', firstMinuteOrders[0]);
                                    }
                                }

                                // 使用新的数据结构更新图表，包含委托数据
                                // 获取URL参数，检查是否有指定的时间范围
                                const urlParams = getUrlParams();
                                const hasTimeRange = urlParams.start_time && urlParams.end_time;

                                // 更新图表
                                updateChart(result.data, orderResult.data);

                                // 如果URL中包含时间参数，自动缩放图表到指定时间范围
                                if (hasTimeRange) {
                                    // 转换时间格式为图表可识别的格式
                                    let startTimeFormatted = urlParams.start_time.replace('T', ' ').substring(0, 19);
                                    let endTimeFormatted = urlParams.end_time.replace('T', ' ').substring(0, 19);

                                    console.log('自动缩放图表到时间范围:', startTimeFormatted, '至', endTimeFormatted);

                                    // 延迟执行以确保图表已完全加载
                                    setTimeout(() => {
                                        if (chart) {
                                            chart.dispatchAction({
                                                type: 'dataZoom',
                                                startValue: startTimeFormatted,
                                                endValue: endTimeFormatted
                                            });
                                            showToast(`已自动缩放图表到指定时间范围`, 'info');
                                        }
                                    }, 500);
                                }

                                showToast(`数据加载成功，包含${orderResult.metadata?.total_orders || 0}条委托数据`, 'success');
                            } else {
                                console.warn('获取委托数据失败:', orderResult.msg);
                                // 即使没有委托数据，也更新图表
                                updateChart(result.data, {});

                                // 如果URL中包含时间参数，自动缩放图表到指定时间范围
                                const urlParams = getUrlParams();
                                if (urlParams.start_time && urlParams.end_time) {
                                    // 转换时间格式为图表可识别的格式
                                    let startTimeFormatted = urlParams.start_time.replace('T', ' ').substring(0, 19);
                                    let endTimeFormatted = urlParams.end_time.replace('T', ' ').substring(0, 19);

                                    // 延迟执行以确保图表已完全加载
                                    setTimeout(() => {
                                        if (chart) {
                                            chart.dispatchAction({
                                                type: 'dataZoom',
                                                startValue: startTimeFormatted,
                                                endValue: endTimeFormatted
                                            });
                                        }
                                    }, 500);
                                }

                                showToast('数据加载成功，无委托数据', 'info');
                            }
                        } catch (orderError) {
                            console.error('获取委托数据时出错:', orderError);
                            // 出错时仍然更新图表
                            updateChart(result.data, {});

                            // 如果URL中包含时间参数，自动缩放图表到指定时间范围
                            const urlParams = getUrlParams();
                            if (urlParams.start_time && urlParams.end_time) {
                                // 转换时间格式为图表可识别的格式
                                let startTimeFormatted = urlParams.start_time.replace('T', ' ').substring(0, 19);
                                let endTimeFormatted = urlParams.end_time.replace('T', ' ').substring(0, 19);

                                // 延迟执行以确保图表已完全加载
                                setTimeout(() => {
                                    if (chart) {
                                        chart.dispatchAction({
                                            type: 'dataZoom',
                                            startValue: startTimeFormatted,
                                            endValue: endTimeFormatted
                                        });
                                    }
                                }, 500);
                            }

                            showToast('数据加载成功，获取委托数据失败', 'warning');
                        }
                        } else {
                            // 不加载委托数据，直接更新图表
                            console.log('委托数据加载已禁用');
                            updateChart(result.data, {});

                            // 如果URL中包含时间参数，自动缩放图表到指定时间范围
                            const urlParams = getUrlParams();
                            if (urlParams.start_time && urlParams.end_time) {
                                // 转换时间格式为图表可识别的格式
                                let startTimeFormatted = urlParams.start_time.replace('T', ' ').substring(0, 19);
                                let endTimeFormatted = urlParams.end_time.replace('T', ' ').substring(0, 19);

                                // 延迟执行以确保图表已完全加载
                                setTimeout(() => {
                                    if (chart) {
                                        chart.dispatchAction({
                                            type: 'dataZoom',
                                            startValue: startTimeFormatted,
                                            endValue: endTimeFormatted
                                        });
                                    }
                                }, 500);
                            }

                            showToast('数据加载成功，未加载委托数据', 'info');
                        }
                    } else {
                        console.error('API返回错误:', result);

                        // 即使API返回错误，也尝试根据URL参数缩放图表
                        const urlParams = getUrlParams();
                        if (urlParams.start_time && urlParams.end_time && chart) {
                            // 转换时间格式为图表可识别的格式
                            let startTimeFormatted = urlParams.start_time.replace('T', ' ').substring(0, 19);
                            let endTimeFormatted = urlParams.end_time.replace('T', ' ').substring(0, 19);

                            // 延迟执行以确保图表已完全加载
                            setTimeout(() => {
                                if (chart) {
                                    chart.dispatchAction({
                                        type: 'dataZoom',
                                        startValue: startTimeFormatted,
                                        endValue: endTimeFormatted
                                    });
                                }
                            }, 500);
                        }

                        showToast('获取数据失败: ' + (result.message || '未知错误'), 'error');
                    }
                } catch (error) {
                    console.error('获取数据出错:', error);

                    // 即使出错，也尝试根据URL参数缩放图表
                    const urlParams = getUrlParams();
                    if (urlParams.start_time && urlParams.end_time && chart) {
                        // 转换时间格式为图表可识别的格式
                        let startTimeFormatted = urlParams.start_time.replace('T', ' ').substring(0, 19);
                        let endTimeFormatted = urlParams.end_time.replace('T', ' ').substring(0, 19);

                        // 延迟执行以确保图表已完全加载
                        setTimeout(() => {
                            if (chart) {
                                chart.dispatchAction({
                                    type: 'dataZoom',
                                    startValue: startTimeFormatted,
                                    endValue: endTimeFormatted
                                });
                            }
                        }, 500);
                    }

                    showToast('获取数据出错: ' + error.message, 'error');
                }
            }

            // 添加消息提示函数
            function showToast(message, type = 'info', duration = 3000) {
                const container = document.getElementById('toastContainer');
                const toast = document.createElement('div');
                toast.className = `toast-message ${type}`;
                toast.textContent = message;

                // 计算新消息的位置
                const existingToasts = container.children;
                const totalHeight = Array.from(existingToasts).reduce((height, t) => {
                    return height + t.offsetHeight + 10; // 10px 是消息间的间距
                }, 0);

                container.appendChild(toast);

                // 强制重绘
                toast.offsetHeight;

                // 显示消息
                toast.classList.add('show');

                // 3秒后自动关闭
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => {
                        container.removeChild(toast);
                        // 重新调整其他消息的位置
                        Array.from(container.children).forEach((t, index) => {
                            const top = index * (t.offsetHeight + 10);
                            t.style.transform = `translateY(${top}px)`;
                        });
                    }, 300);
                }, duration);
            }

            // 监听策略选择变化
            document.getElementById('strategySelect').addEventListener('change', (e) => {
                if (e.target.value) {
                    fetchData();
                }
            });

            // 页面加载时获取策略列表
            document.addEventListener('DOMContentLoaded', async function() {
                console.log('DOM loaded, chart ready:', chart);
                try {
                    const response = await fetch('/get_strategies');
                    const data = await response.json();

                    if (!Array.isArray(data)) {
                        console.error('Invalid data format:', data);
                        showToast('获取策略列表失败：数据格式错误', 'error');
                        return;
                    }

                    const select = document.getElementById('strategySelect');
                    select.innerHTML = '<option value="">选择策略</option>';

                    data.forEach(strategy => {
                        const option = document.createElement('option');
                        option.value = strategy.strategy_id;
                        option.textContent = `策略${strategy.strategy_id} 时间: ${formatDateTime(strategy.start_time)}
                            买${strategy.buy_rate}%卖${strategy.sell_rate}%
                            - 胜率:${strategy.win_rate}% - 休息:${strategy.rest_minutes}
                            回买:${strategy.lookback_minutes_buy} 回卖${strategy.lookback_minutes_sell} 次
                            收益:${strategy.profit_percentage}% 交易:${strategy.total_trades}次`;
                        select.appendChild(option);
                    });

                    // 如果有策略数据，且URL中没有strategy_id时，自动选择第一个策略
                    const params = getUrlParams();
                    if (data.length > 0 && !params.strategy_id) {
                        const firstStrategy = data[0];
                        select.value = firstStrategy.strategy_id;
                        fetchData();  // 加载第一个策略的数据
                        showToast('已加载策略数据', 'info');
                    } else if (data.length === 0) {
                        showToast('策略列表已更新，但没有可用策略', 'info');
                    }
                } catch (error) {
                    console.error('Error fetching strategies:', error);
                    showToast('获取策略列表失败', 'error');
                }

                // 添加容器点击事件
                const chartContainer = document.getElementById('chart');
                chartContainer.addEventListener('click', function(e) {
                    console.log('Container clicked', e);
                    const rect = chartContainer.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    // 将点击坐标转换为数据坐标
                    if (chart) {
                        const pointInGrid = chart.convertFromPixel({seriesIndex: 0}, [x, y]);
                        if (pointInGrid) {
                            console.log('Clicked point in data:', pointInGrid);
                            //window.open('https://www.baidu.com/s?wd=7878');
                        }
                    }
                });

                // 右键事件监听器已在图表初始化时添加
            });

            // 处理窗口大小变化
            window.addEventListener('resize', function() {
                chart.resize();
            });

            // 格式化日期时间
            function formatDateTime(isoString) {
                if (!isoString) return 'N/A';
                try {
                    const date = new Date(isoString);
                    if (isNaN(date.getTime())) return 'N/A';

                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit'
                    });
                } catch (e) {
                    console.error('Error formatting date:', e);
                    return 'N/A';
                }
            }

            // 计算当前时间点的账户价值
            function calculateAccountValue(timestamp, currentPrice) {
                let currentValue = 0;
                let lastLongTrade = null;
                let lastShortTrade = null;

                // 找到该时间点之前的最后一次做多和做空交易
                for (let i = trades.length - 1; i >= 0; i--) {
                    const trade = trades[i];
                    if (trade.timestamp <= timestamp) {
                        if (trade.position_type === 'long' && !lastLongTrade) {
                            lastLongTrade = trade;
                        }
                        if (trade.position_type === 'close_long' && !lastLongTrade) {
                            lastLongTrade = null;
                        }
                        if (trade.position_type === 'short' && !lastShortTrade) {
                            lastShortTrade = trade;
                        }
                        if (trade.position_type === 'close_short' && !lastShortTrade) {
                            lastShortTrade = null;
                        }
                        if (!currentValue) {
                            currentValue = parseFloat(trade.account_value);
                        }
                        if (lastLongTrade && lastShortTrade) break;
                    }
                }
                //debugger
                // 计算做多的价值
                if (lastLongTrade) {
                    const longAmount = parseFloat(lastLongTrade.position_size);
                    const longEntryPrice = parseFloat(lastLongTrade.entry_price);
                    const longProfit = (currentPrice - longEntryPrice) * longAmount;
                    currentValue = parseFloat(lastLongTrade.account_value) + longProfit;
                }

                // 计算做空的价值
                if (lastShortTrade) {
                    const shortAmount = parseFloat(lastShortTrade.position_size);
                    const shortEntryPrice = parseFloat(lastShortTrade.entry_price);
                    const shortProfit = (shortEntryPrice - currentPrice) * shortAmount;
                    currentValue = parseFloat(lastShortTrade.account_value) + shortProfit;
                }

                return currentValue.toFixed(2);
            }

            // 更新tooltip配置
            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    },
                    formatter: function(params) {
                        const klineData = params[0];
                        if (!klineData) return '';

                        const timestamp = klineData.axisValue;
                        const data = klineData.data;
                        const currentPrice = parseFloat(data[1]); // 收盘价
                        const accountValue = calculateAccountValue(timestamp, currentPrice);

                        let color = '';
                        if (data[1] > data[0]) {
                            color = '#26a69a';  // 绿色，表示上涨
                        } else {
                            color = '#ef5350';  // 红色，表示下跌
                        }

                        return `
                            <div style="font-size:12px;line-height:1.5;">
                                <div>时间：${timestamp}</div>
                                <div style="color:${color}">
                                    开盘价：${data[0]}<br/>
                                    收盘价：${data[1]}<br/>
                                    最低价：${data[2]}<br/>
                                    最高价：${data[3]}<br/>
                                    成交量：${data[4]}<br/>
                                    账户价值：${accountValue}
                                </div>
                            </div>
                        `;
                    }
                },
                // ... 其他配置保持不变
            };

            // 解析URL参数
            function getUrlParams() {
                const params = {};
                window.location.search.replace(/[?&]+([^=&]+)=([^&]*)/gi, function(str, key, value) {
                    params[key] = decodeURIComponent(value);
                });
                return params;
            }

            // 初始化
            function init() {
                // 获取URL参数
                const params = getUrlParams();
                console.log('URL参数:', params);

                // 检查URL中是否有策略ID
                if (params.strategy_id) {
                    updateStrategyIdInput(params.strategy_id);
                    console.log('设置策略ID:', params.strategy_id);

                    // 自动加载策略
                    setTimeout(() => {
                        fetchStrategyById();
                    }, 500);
                }
            }



            // 计算一小时范围内的数据占比
            function calculateOneHourRange(timestamps) {
                if (!timestamps || timestamps.length === 0) return 100;

                const startTime = new Date(timestamps[0]);
                let oneHourLater = new Date(startTime);
                oneHourLater.setHours(oneHourLater.getHours() + 1);

                for (let i = 0; i < timestamps.length; i++) {
                    const currentTime = new Date(timestamps[i]);
                    if (currentTime > oneHourLater) {
                        // 计算一小时内的数据占总数据的百分比
                        return Math.min(Math.max((i / timestamps.length) * 100, 5), 100);
                    }
                }

                // 如果所有数据都在一小时内，显示全部
                return 100;
            }

            // 添加时间点详细信息弹窗
            function showDetailModal(timestamp, dataPoint) {
                // 创建弹窗元素（如果不存在）
                let detailModal = document.getElementById('detailModal');
                if (!detailModal) {
                    detailModal = document.createElement('div');
                    detailModal.id = 'detailModal';
                    detailModal.className = 'modal';
                    document.body.appendChild(detailModal);

                    // 添加弹窗样式
                    const styleElement = document.createElement('style');
                    styleElement.textContent = `
                        .modal {
                            display: none;
                            position: fixed;
                            z-index: 1000;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%);
                            width: 80%;
                            max-width: 600px;
                            max-height: 80vh;
                            overflow-y: auto;
                            background-color: #fff;
                            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                            border-radius: 4px;
                        }
                        .modal-header {
                            padding: 15px;
                            background-color: #f5f5f5;
                            border-bottom: 1px solid #ddd;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                        }
                        .modal-title {
                            font-size: 18px;
                            font-weight: bold;
                            margin: 0;
                        }
                        .close-button {
                            background: none;
                            border: none;
                            font-size: 20px;
                            cursor: pointer;
                        }
                        .modal-body {
                            padding: 15px;
                        }
                        .modal-section {
                            margin-bottom: 15px;
                            padding-bottom: 15px;
                            border-bottom: 1px solid #eee;
                        }
                        .modal-section:last-child {
                            border-bottom: none;
                        }
                        .section-title {
                            font-weight: bold;
                            margin-bottom: 8px;
                        }
                        .data-row {
                            display: flex;
                            margin-bottom: 5px;
                        }
                        .data-label {
                            flex: 0 0 120px;
                            font-weight: 500;
                        }
                        .data-value {
                            flex: 1;
                        }
                        .profit-positive {
                            color: #26a69a;
                        }
                        .profit-negative {
                            color: #ef5350;
                        }
                    `;
                    document.head.appendChild(styleElement);
                }

                // 构建弹窗内容
                const [open, close, low, high] = dataPoint.kline;
                const trade = dataPoint.trade;
                const accountValue = dataPoint.account_value;
                const logData = strategyLogs[timestamp.replace(' ', 'T')];

                // 获取委托数据
                let orders = [];
                const minuteKey = timestamp.substring(0, 17) + '00';
                if (orderData[timestamp] && orderData[timestamp].length > 0) {
                    orders = orderData[timestamp];
                } else if (orderData[minuteKey] && orderData[minuteKey].length > 0) {
                    orders = orderData[minuteKey];
                }

                let content = `
                    <div class="modal-header">
                        <h3 class="modal-title">详细数据 - ${timestamp}</h3>
                        <button class="close-button" onclick="document.getElementById('detailModal').style.display='none'">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="modal-section">
                            <div class="section-title">价格数据</div>
                            <div class="data-row">
                                <div class="data-label">开盘价</div>
                                <div class="data-value">${open.toFixed(5)}</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">收盘价</div>
                                <div class="data-value">${close.toFixed(5)}</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">最高价</div>
                                <div class="data-value">${high.toFixed(5)}</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">最低价</div>
                                <div class="data-value">${low.toFixed(5)}</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">成交量</div>
                                <div class="data-value">${dataPoint.volume.toFixed(2)}</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">涨跌幅</div>
                                <div class="data-value">${dataPoint.price_change}%</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">震幅</div>
                                <div class="data-value">${dataPoint.price_range}%</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">账户价值</div>
                                <div class="data-value">${accountValue ? accountValue.toFixed(2) : '无数据'}</div>
                            </div>
                        </div>
                `;

                // 添加交易信息（如果有）
                if (trade) {
                    const profitClass = parseFloat(trade.profit_amount || 0) >= 0 ? 'profit-positive' : 'profit-negative';

                    content += `
                        <div class="modal-section">
                            <div class="section-title">交易信息</div>
                            <div class="data-row">
                                <div class="data-label">交易时间</div>
                                <div class="data-value">${trade.timestamp}</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">交易类型</div>
                                <div class="data-value">${trade.position_type}</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">价格</div>
                                <div class="data-value">${parseFloat(trade.price).toFixed(5)}</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">数量</div>
                                <div class="data-value">${trade.amount}</div>
                            </div>
                            <div class="data-row">
                                <div class="data-label">手续费</div>
                                <div class="data-value">${parseFloat(trade.fee || 0).toFixed(5)}</div>
                            </div>
                    `;

                    if (trade.profit_amount) {
                        content += `
                            <div class="data-row">
                                <div class="data-label">盈亏</div>
                                <div class="data-value ${profitClass}">${parseFloat(trade.profit_amount).toFixed(5)} (${trade.profit_percentage}%)</div>
                            </div>
                        `;
                    }

                    if (trade.trigger_reason) {
                        content += `
                            <div class="data-row">
                                <div class="data-label">触发原因</div>
                                <div class="data-value modal-reason">${trade.trigger_reason}</div>
                            </div>
                        `;
                    }

                    if (trade.entry_price) {
                        content += `
                            <div class="data-row">
                                <div class="data-label">入场价格</div>
                                <div class="data-value">${parseFloat(trade.entry_price).toFixed(5)}</div>
                            </div>
                        `;
                    }

                    if (trade.position_size) {
                        content += `
                            <div class="data-row">
                                <div class="data-label">仓位大小</div>
                                <div class="data-value">${parseFloat(trade.position_size).toFixed(2)}</div>
                            </div>
                        `;
                    }

                    if (trade.last_high) {
                        content += `
                            <div class="data-row">
                                <div class="data-label">最高价</div>
                                <div class="data-value">${parseFloat(trade.last_high).toFixed(5)}</div>
                            </div>
                        `;
                    }

                    if (trade.last_low) {
                        content += `
                            <div class="data-row">
                                <div class="data-label">最低价</div>
                                <div class="data-value">${parseFloat(trade.last_low).toFixed(5)}</div>
                            </div>
                        `;
                    }

                    content += '</div>';
                }

                // 添加策略日志信息（如果有）
                if (logData) {
                    content += `
                        <div class="modal-section">
                            <div class="section-title">策略分析</div>
                    `;

                    if (logData.action) {
                        content += `
                            <div class="data-row">
                                <div class="data-label">动作</div>
                                <div class="data-value">${logData.action}</div>
                            </div>
                        `;
                    }

                    if (logData.reason) {
                        content += `
                            <div class="data-row">
                                <div class="data-label">原因</div>
                                <div class="data-value modal-reason">${logData.reason}</div>
                            </div>
                        `;
                    }

                    if (logData.trigger_price) {
                        const triggerPrice = typeof logData.trigger_price === 'number'
                            ? logData.trigger_price.toFixed(5)
                            : logData.trigger_price;

                        content += `
                            <div class="data-row">
                                <div class="data-label">触发价格</div>
                                <div class="data-value">${triggerPrice}</div>
                            </div>
                        `;
                    }

                    if (logData.min_trigger_price) {
                        const minTriggerPrice = typeof logData.min_trigger_price === 'number'
                            ? logData.min_trigger_price.toFixed(5)
                            : logData.min_trigger_price;

                        content += `
                            <div class="data-row">
                                <div class="data-label">最小触发价格</div>
                                <div class="data-value">${minTriggerPrice}</div>
                            </div>
                        `;
                    }

                    // 添加其他可能存在的字段
                    for (const key in logData) {
                        if (!['action', 'reason', 'trigger_price', 'min_trigger_price', 'timestamp'].includes(key)) {
                            let value = logData[key];
                            if (typeof value === 'number') {
                                value = value.toFixed(5);
                            }

                            if (value !== null && value !== undefined) {
                                content += `
                                    <div class="data-row">
                                        <div class="data-label">${key}</div>
                                        <div class="data-value">${value}</div>
                                    </div>
                                `;
                            }
                        }
                    }

                    content += '</div>';
                }

                // 添加委托信息（如果有）
                if (orders && orders.length > 0) {
                    content += `
                        <div class="modal-section">
                            <div class="section-title">委托信息 (${orders.length}条)</div>
                            <div class="orders-table">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>时间</th>
                                            <th>方向</th>
                                            <th>类型</th>
                                            <th>价格</th>
                                            <th>数量</th>
                                            <th>成交价</th>
                                            <th>成交量</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                    `;

                    // 按时间排序委托，最新的在前面
                    const sortedOrders = [...orders].sort((a, b) => {
                        return new Date(b.timestamp) - new Date(a.timestamp);
                    });

                    for (const order of sortedOrders) {
                        // 根据状态设置颜色
                        let statusClass = '';
                        if (order.state === 'filled') {
                            statusClass = 'status-filled';
                        } else if (order.state === 'canceled') {
                            statusClass = 'status-canceled';
                        } else {
                            statusClass = 'status-live';
                        }

                        // 根据方向设置颜色
                        let directionClass = '';
                        if (order.side === 'buy') {
                            directionClass = 'direction-buy';
                        } else {
                            directionClass = 'direction-sell';
                        }

                        // 提取时间中的时分秒
                        const timeStr = order.timestamp.split(' ')[1];

                        content += `
                            <tr>
                                <td>${timeStr}</td>
                                <td class="${directionClass}">${order.direction_cn || (order.side === 'buy' ? '买入' : '卖出')}</td>
                                <td>${order.order_type_cn || order.order_type}</td>
                                <td>${parseFloat(order.price).toFixed(5)}</td>
                                <td>${parseFloat(order.size).toFixed(5)}</td>
                                <td>${order.filled_price ? parseFloat(order.filled_price).toFixed(5) : '-'}</td>
                                <td>${order.filled_size ? parseFloat(order.filled_size).toFixed(5) : '-'}</td>
                                <td class="${statusClass}">${order.state_cn || order.state}</td>
                            </tr>
                        `;
                    }

                    content += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    `;

                    // 添加表格样式
                    const styleElement = document.getElementById('orderTableStyle');
                    if (!styleElement) {
                        const newStyle = document.createElement('style');
                        newStyle.id = 'orderTableStyle';
                        newStyle.textContent = `
                            .orders-table {
                                max-height: 300px;
                                overflow-y: auto;
                                margin-top: 10px;
                            }
                            .data-table {
                                width: 100%;
                                border-collapse: collapse;
                                font-size: 12px;
                            }
                            .data-table th, .data-table td {
                                padding: 5px;
                                text-align: center;
                                border: 1px solid #e8e8e8;
                            }
                            .data-table th {
                                background-color: #f5f5f5;
                                font-weight: bold;
                            }
                            .data-table tr:nth-child(even) {
                                background-color: #f9f9f9;
                            }
                            .data-table tr:hover {
                                background-color: #f0f0f0;
                            }
                            .status-filled {
                                color: #52c41a;
                                font-weight: bold;
                            }
                            .status-canceled {
                                color: #bfbfbf;
                            }
                            .status-live {
                                color: #faad14;
                                font-weight: bold;
                            }
                            .direction-buy {
                                color: #1890ff;
                                font-weight: bold;
                            }
                            .direction-sell {
                                color: #f5222d;
                                font-weight: bold;
                            }
                        `;
                        document.head.appendChild(newStyle);
                    }
                }

                content += '</div>'; // 关闭modal-body

                // 设置弹窗内容并显示
                detailModal.innerHTML = content;
                detailModal.style.display = 'block';

                // 添加点击空白区域关闭弹窗的功能
                window.onclick = function(event) {
                    if (event.target === detailModal) {
                        detailModal.style.display = 'none';
                    }
                };
            }

            // 根据策略ID查询
            async function fetchStrategyById() {
                const strategyId = document.getElementById('strategyIdInput').value;
                if (!strategyId) {
                    showToast('请输入策略ID', 'warning');
                    return;
                }

                try {
                    showToast(`正在查询策略ID: ${strategyId}...`, 'info');

                    // 获取策略详情
                    const response = await fetch(`/api/strategy_chart/${strategyId}`);
                    if (!response.ok) {
                        const errorText = await response.text();
                        throw new Error(`查询策略失败: ${response.status} - ${errorText}`);
                    }

                    const result = await response.json();
                    console.log('策略数据:', result);

                    if (result.strategy_info) {
                        showToast(`策略ID: ${strategyId} 查询成功`, 'success');

                        // 更新选择框，添加该策略作为选项
                        const strategySelect = document.getElementById('strategySelect');
                        const existingOption = Array.from(strategySelect.options).find(option => option.value == strategyId);

                        if (!existingOption) {
                            const newOption = document.createElement('option');
                            newOption.value = strategyId;
                            newOption.text = `策略ID: ${strategyId} (${result.strategy_info.currency || 'DOGE'})`;
                            newOption.selected = true;
                            strategySelect.appendChild(newOption);
                        } else {
                            existingOption.selected = true;
                        }

                        // 更新策略信息
                        updateStrategyInfo(result.strategy_info);

                        // 获取URL参数，检查是否有指定的时间范围
                        const params = getUrlParams();

                        // 默认使用策略的时间范围
                        let startTime = result.time_range?.start_time || result.strategy_info.start_time;
                        let endTime = result.time_range?.end_time || result.strategy_info.end_time;

                        // 如果URL中指定了时间范围，优先使用URL的
                        if (params.start_time) {
                            startTime = params.start_time;
                        }

                        if (params.end_time) {
                            endTime = params.end_time;
                        }

                        // 注意：货币和日期设置已移除，因为相关控件已删除

                        // 拉取数据
                        fetchData();
                    } else {
                        showToast(`查询策略失败: ${result.error || '未知错误'}`, 'error');
                    }
                } catch (error) {
                    console.error('查询策略出错:', error);
                    showToast(`查询策略出错: ${error.message}`, 'error');
                }
            }

            // 更新策略信息面板
            function updateStrategyInfo(strategy) {
                // 显示策略信息面板
                const strategyInfo = document.getElementById('strategyInfo');
                if (strategyInfo) {
                    strategyInfo.style.display = 'block';
                }

                // 安全地设置元素文本内容的辅助函数
                function setElementText(id, value) {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = value;
                    }
                }

                // 填充策略详情
                setElementText('strategyId', strategy.id || '');
                setElementText('strategyName', strategy.strategy_name || '');
                setElementText('strategyType', strategy.strategy_type || '');
                setElementText('currency', strategy.currency || 'DOGE');
                setElementText('roi', (strategy.roi || 0).toFixed(2));

                const successRate = strategy.success_rate ||
                    (strategy.successful_trades && strategy.total_trades
                    ? ((strategy.successful_trades / strategy.total_trades) * 100).toFixed(2)
                    : '0.00');
                setElementText('successRate', successRate);

                setElementText('totalTrades', strategy.total_trades || 0);
                setElementText('successfulTrades', strategy.successful_trades || 0);
                setElementText('initialCapital', (strategy.initial_capital || 0).toFixed(2));
                setElementText('finalCapital', (strategy.final_capital || 0).toFixed(2));
                setElementText('totalProfit', (strategy.total_profit || 0).toFixed(2));
                setElementText('totalFees', (strategy.total_fees || 0).toFixed(2));

                // 设置策略参数
                setElementText('buyRate', (strategy.buy_rate * 100 || 0).toFixed(2));
                setElementText('sellRate', (strategy.sell_rate * 100 || 0).toFixed(2));
                setElementText('restMinutes', strategy.rest_minutes || 0);
                setElementText('minTriggerRest', strategy.min_trigger_rest || 0);
                setElementText('lookbackMinutesBuy', strategy.lookback_minutes_buy || 0);
                setElementText('lookbackMinutesSell', strategy.lookback_minutes_sell || 0);

                // 设置账户价值相关
                setElementText('highestAccountValue', (strategy.highest_account_value || 0).toFixed(2));
                setElementText('lowestAccountValue', (strategy.lowest_account_value || 0).toFixed(2));

                // 设置是否实盘
                setElementText('isLiveTrading', strategy.is_live_trading ? '是' : '否');

                // 设置时间范围
                setElementText('startTime', strategy.start_time || '');
                setElementText('endTime', strategy.end_time || '');
            }

            // 添加策略选择框事件处理
            document.addEventListener('DOMContentLoaded', function() {
                const strategySelect = document.getElementById('strategySelect');
                if (strategySelect) {
                    strategySelect.addEventListener('change', function() {
                        const selectedStrategyId = this.value;
                        if (selectedStrategyId) {
                            // 更新输入框的值
                            updateStrategyIdInput(selectedStrategyId);
                            // 加载策略
                            fetchStrategyById();
                        }
                    });
                }

                // 初始化开关状态
                initializeToggles();

                // 初始化键盘控制
                initializeKeyboardControls();

                // 初始化页面
                init();

                // 获取策略列表
                try {
                    fetch('/get_strategies')
                        .then(response => response.json())
                        .then(data => {
                            if (Array.isArray(data)) {
                                const select = document.getElementById('strategySelect');
                                select.innerHTML = '<option value="">选择策略</option>';

                                data.forEach(strategy => {
                                    const option = document.createElement('option');
                                    option.value = strategy.strategy_id;
                                    option.textContent = `策略${strategy.strategy_id} 收益:${strategy.profit_percentage}%`;
                                    select.appendChild(option);
                                });

                                // 如果有策略数据，自动选择第一个策略
                                if (data.length > 0) {
                                    const firstStrategy = data[0];
                                    select.value = firstStrategy.strategy_id;
                                    updateStrategyIdInput(firstStrategy.strategy_id);
                                    console.log('已自动选择第一个策略:', firstStrategy.strategy_id);
                                }

                                console.log('策略列表已加载');
                            } else {
                                console.error('无效的策略数据格式:', data);
                            }
                        })
                        .catch(error => {
                            console.error('获取策略列表失败:', error);
                        });
                } catch (error) {
                    console.error('初始化策略列表出错:', error);
                }
            });
        </script>
    </div>

    <!-- 时间选择器弹窗 -->
    <div id="timeSelectorModal" class="modal" style="display: none;">
        <div class="modal-content" style="width: 400px; max-width: 90%;">
            <div class="modal-header">
                <h3 class="modal-title">选择时间</h3>
                <button class="close-button" onclick="closeTimeSelector()">&times;</button>
            </div>
            <div class="modal-body">
                <div style="margin-bottom: 15px;">
                    <label for="timeInput" style="display: block; margin-bottom: 5px;">选择时间:</label>
                    <input type="datetime-local" id="timeInput" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px;">快捷选择:</label>
                    <div style="display: flex; flex-wrap: wrap; gap: 5px;">
                        <button onclick="setQuickTime('start')" style="padding: 5px 10px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 4px; cursor: pointer;">开始时间</button>
                        <button onclick="setQuickTime('end')" style="padding: 5px 10px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 4px; cursor: pointer;">结束时间</button>
                        <button onclick="setQuickTime('firstTrade')" style="padding: 5px 10px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 4px; cursor: pointer;">首次交易</button>
                        <button onclick="setQuickTime('lastTrade')" style="padding: 5px 10px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 4px; cursor: pointer;">最后交易</button>
                    </div>
                </div>
                <div style="text-align: right;">
                    <button onclick="closeTimeSelector()" style="padding: 8px 15px; margin-right: 10px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 4px; cursor: pointer;">取消</button>
                    <button onclick="jumpToSelectedTime()" style="padding: 8px 15px; border: 1px solid #007bff; background: #007bff; color: white; border-radius: 4px; cursor: pointer;">跳转</button>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
