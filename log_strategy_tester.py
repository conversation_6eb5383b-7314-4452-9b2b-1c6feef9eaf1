import json
import os
import time
import traceback
import pymysql
from datetime import datetime, timedelta, timezone
import copy
from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG
import argparse

def print_log(message):
    """打印日志信息"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {message}")

class LogStrategyTester:
    def __init__(self, log_file, currency="DOGE", buy_rate=0.002, sell_rate=0.003, 
                 rest_minutes=1, min_trigger_rest=2, lookback_minutes_buy=2, 
                 lookback_minutes_sell=3, initial_capital=8.1574):
        """初始化日志策略测试器"""
        self.log_file = log_file
        self.currency = currency
        self.buy_rate = buy_rate
        self.sell_rate = sell_rate
        self.rest_minutes = rest_minutes
        self.min_trigger_rest = min_trigger_rest
        self.lookback_minutes_buy = lookback_minutes_buy
        self.lookback_minutes_sell = lookback_minutes_sell
        self.initial_capital = initial_capital
        self.conn = None
        
        # 创建策略分析器
        self.strategy_analyzer = StrategyAnalyzer(
            db_config=DB_CONFIG
        )
        self.strategy_analyzer.save_row_reson = 1
        self.strategy_analyzer.save_trades = 1
        
        # 创建模拟交易策略分析器
        self.strategy_analyzer_simulate = StrategyAnalyzer(
            db_config=DB_CONFIG
        )
        self.strategy_analyzer_simulate.save_row_reson = 1
        self.strategy_analyzer_simulate.save_trades = 1
        
        # 初始化参数
        self.params = self._init_params(is_live_trading=0, run_type='live')
        self.params_simulate = self._init_params(is_live_trading=0, run_type='analyze')
        
        # 确保日志目录存在
        os.makedirs("log/strategy_test", exist_ok=True)
        
    def _init_params(self, is_live_trading, run_type):
        """初始化策略参数"""
        return {
            'currency': self.currency,
            'position': "无仓",
            'entry_price': 0,
            'position_size': 0,
            'available_capital': float(self.initial_capital),
            'initial_capital': float(self.initial_capital),
            'total_fees': 0,
            'trades': [],
            'last_high': 0,
            'last_low': 9999,
            'total_profit': 0,
            'total_trades': 0,
            'successful_trades': 0,
            'trade_log': [],
            'profit': 0,
            'account_value': self.initial_capital,
            'current_position': 0,
            'last_position_price': 0,
            'buy_rate': self.buy_rate,
            'sell_rate': self.sell_rate,
            'size': 0,
            'rest_minutes': self.rest_minutes,
            'min_trigger_rest': self.min_trigger_rest,
            'last_close_time': None,
            'last_entry_time': None,
            'account_value_history': [],
            'highest_account_value': self.initial_capital,
            'lowest_account_value': self.initial_capital,
            'lookback_minutes_buy': self.lookback_minutes_buy,
            'lookback_minutes_sell': self.lookback_minutes_sell,
            'strategy_report': [],
            'continuous_up_count': 0,
            'continuous_down_count': 0,
            'continuous_up_keep_count': 0,
            'continuous_down_keep_count': 0,
            'max_continuous_keep_count': 10,
            'pre_continuous_up_count': 0,
            'pre_continuous_down_count': 0,
            'last_candle_high': float(0),
            'last_candle_low': float(0),
            'is_live_trading': is_live_trading,
            'trade_condition': {
                'reason': '初始化',
                'trigger_price': 0
            },
            'order_book': {
                'timestamp': None,
                'bids': [],
                'asks': []
            },
            'live_available_balance': 0.0,
            'live_total_equity': 0.0,
            'live_position_value': 0.0,
            'live_unrealized_pnl': 0.0,
            'min_trigger_rate': 0.0004,
            'pre_row': None,
            'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'run_type': run_type,
            'reverse_buy': 0,
            'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'lock_time': 0,
            'row_close': 0
        }
    
    def get_db_connection(self):
        """获取数据库连接"""
        try:
            if self.conn is None or not self.conn.open:
                self.conn = pymysql.connect(**DB_CONFIG)
            return self.conn
        except Exception as e:
            print_log(f"数据库连接失败: {str(e)}")
            raise
    
    def parse_log_line(self, line):
        """解析日志行"""
        try:
            parts = line.strip().split('|')
            if len(parts) < 8:
                return None
                
            # 解析数据
            currency = parts[0]
            timestamp = parts[1]
            open_price = float(parts[2])
            high_price = float(parts[3])
            low_price = float(parts[4])
            close_price = float(parts[5])
            volume = float(parts[6])
            volume_currency = float(parts[7])
            is_complete = parts[8] if len(parts) > 8 else "0"
            
            return {
                'currency': currency,
                'timestamp': timestamp,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume,
                'volume_currency': volume_currency,
                'is_complete': is_complete
            }
        except Exception as e:
            print_log(f"解析日志行失败: {line} - {str(e)}")
            return None
    
    def run_test(self):
        """运行策略测试"""
        print_log(f"开始从日志文件 {self.log_file} 读取数据并测试策略...")
        
        try:
            # 读取日志文件
            with open(self.log_file, 'r') as f:
                lines = f.readlines()
            
            print_log(f"读取了 {len(lines)} 行数据")
            
            # 记录第一条和最后一条有效数据的时间戳
            first_timestamp = None
            last_timestamp = None
            
            # 处理每一行数据
            for i, line in enumerate(lines):
                candle_data = self.parse_log_line(line)
                if not candle_data:
                    continue
                
                # 记录时间戳
                if first_timestamp is None:
                    first_timestamp = candle_data['timestamp']
                last_timestamp = candle_data['timestamp']
                
                # 设置K线是否完成的标志
                self.params['row_close'] = 1 if candle_data['is_complete'] else 0
                
                # 执行模拟策略
                if candle_data['is_complete']=='True':
                    self.params_simulate = self.strategy_analyzer_simulate.process_trade_logic(candle_data, self.params_simulate)
                
                # 执行实盘策略
                self.params = self.strategy_analyzer.process_trade_logic(candle_data, self.params)
                
                # 记录日志
                if i % 100 == 0 or candle_data['is_complete']:
                    print_log(f"处理进度: {i+1}/{len(lines)} - {candle_data['timestamp']}")
                    print_log(f"实盘数据: {candle_data['close']:.5f} {self.params['account_value']:.4f} 升{self.params['continuous_up_count']} 跌{self.params['continuous_down_count']} 理由: {self.params['trade_condition'].get('reason', '无原因')}")
                    print_log(f"模拟数据: {candle_data['close']:.5f} {self.params_simulate['account_value']:.4f} 升{self.params_simulate['continuous_up_count']} 跌{self.params_simulate['continuous_down_count']} 理由: {self.params_simulate['trade_condition'].get('reason', '无原因')}")
                
                # 如果是完整K线，记录日志信息
                if candle_data['is_complete']:
                    # 将日志信息封装成字典
                    row_reson_dict = {
                        "timestamp": str(candle_data['timestamp']),
                        "close": candle_data['close'],
                        "last_high": self.params['last_high'],
                        "last_low": self.params['last_low'],
                        "continuous_up_count": self.params['continuous_up_count'],
                        "continuous_down_count": self.params['continuous_down_count'],
                        "trade_condition": self.params['trade_condition']['reason']
                    }
                    
                    # 将模拟日志信息封装成字典
                    row_reson_dict_simulate = {
                        "timestamp": str(candle_data['timestamp']),
                        "close": candle_data['close'],
                        "last_high": self.params_simulate['last_high'],
                        "last_low": self.params_simulate['last_low'],
                        "continuous_up_count": self.params_simulate['continuous_up_count'],
                        "continuous_down_count": self.params_simulate['continuous_down_count'],
                        "trade_condition": self.params_simulate['trade_condition']['reason']
                    }
                    
                    # 将日志字典添加到列表中
                    self.strategy_analyzer.row_reson_list[str(candle_data['timestamp'])] = row_reson_dict
                    self.strategy_analyzer_simulate.row_reson_list[str(candle_data['timestamp'])] = row_reson_dict_simulate
            
            # 保存策略结果
            print_log("策略测试完成，保存结果...")
            
            # 更新开始和结束时间为数据的实际时间范围
            if first_timestamp and last_timestamp:
                self.params['start_time'] = first_timestamp
                self.params['end_time'] = last_timestamp
                self.params_simulate['start_time'] = first_timestamp
                self.params_simulate['end_time'] = last_timestamp
                print_log(f"设置时间范围: {first_timestamp} 至 {last_timestamp}")
            
            # 保存实盘策略结果
            strategy_id = self.strategy_analyzer.save_strategy_results(self.params)
            print_log(f"实盘策略结果已保存，策略ID: {strategy_id}")
            
            # 保存模拟策略结果
            strategy_id_simulate = self.strategy_analyzer_simulate.save_strategy_results(self.params_simulate)
            print_log(f"模拟策略结果已保存，策略ID: {strategy_id_simulate}")
            
            # 保存参数到文件
            with open(f"log/strategy_test/params_live_{strategy_id}.json", 'w', encoding='utf-8') as file:
                json.dump(self.params, file, ensure_ascii=False, indent=4)
            
            with open(f"log/strategy_test/params_simulate_{strategy_id_simulate}.json", 'w', encoding='utf-8') as file:
                json.dump(self.params_simulate, file, ensure_ascii=False, indent=4)
            
            return {
                'live_strategy_id': strategy_id,
                'simulate_strategy_id': strategy_id_simulate,
                'live_account_value': self.params['account_value'],
                'simulate_account_value': self.params_simulate['account_value'],
                'live_trades': len(self.params['trades']),
                'simulate_trades': len(self.params_simulate['trades']),
                'live_profit': self.params['total_profit'],
                'simulate_profit': self.params_simulate['total_profit']
            }
            
        except Exception as e:
            print_log(f"测试过程中发生错误: {str(e)}\n{traceback.format_exc()}")
            raise
        finally:
            if self.conn:
                self.conn.close()

def main():
    parser = argparse.ArgumentParser(description='从日志文件测试交易策略')
    parser.add_argument('--log-file', type=str, default='log/sec_candle/43504.log', help='日志文件路径')
    parser.add_argument('--currency', type=str, default='DOGE', help='交易币种')
    parser.add_argument('--buy-rate', type=float, default=0.002, help='买入阈值')
    parser.add_argument('--sell-rate', type=float, default=0.003, help='卖出阈值')
    parser.add_argument('--rest', type=int, default=1, help='交易间隔休息时间（分钟）')
    parser.add_argument('--min-trigger-rest', type=int, default=2, help='最小止损间隔时间（分钟）')
    parser.add_argument('--lookback-minutes-buy', type=int, default=2, help='买回看分钟数')
    parser.add_argument('--lookback-minutes-sell', type=int, default=3, help='卖回看分钟数')
    parser.add_argument('--initial-capital', type=float, default=8.1574, help='初始资本')
    
    args = parser.parse_args()
    
    tester = LogStrategyTester(
        log_file=args.log_file,
        currency=args.currency,
        buy_rate=args.buy_rate,
        sell_rate=args.sell_rate,
        rest_minutes=args.rest,
        min_trigger_rest=args.min_trigger_rest,
        lookback_minutes_buy=args.lookback_minutes_buy,
        lookback_minutes_sell=args.lookback_minutes_sell,
        initial_capital=args.initial_capital
    )
    
    results = tester.run_test()
    
    print_log("\n===== 测试结果摘要 =====")
    print_log(f"实盘策略ID: {results['live_strategy_id']}")
    print_log(f"模拟策略ID: {results['simulate_strategy_id']}")
    print_log(f"实盘账户价值: {results['live_account_value']:.4f}")
    print_log(f"模拟账户价值: {results['simulate_account_value']:.4f}")
    print_log(f"实盘交易次数: {results['live_trades']}")
    print_log(f"模拟交易次数: {results['simulate_trades']}")
    print_log(f"实盘总利润: {results['live_profit']:.4f}")
    print_log(f"模拟总利润: {results['simulate_profit']:.4f}")

if __name__ == "__main__":
    main()

