#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, render_template, jsonify
import logging

# 创建最小化的Flask应用
app = Flask(__name__)

# 完全禁用调试和自动重载
app.config['DEBUG'] = False
app.config['TEMPLATES_AUTO_RELOAD'] = False
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0

# 设置日志级别为WARNING以减少日志输出
logging.basicConfig(level=logging.WARNING)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/health')
def health():
    return jsonify({
        'status': 'ok',
        'message': 'Minimal Flask app is running'
    })

@app.route('/api/test')
def test_api():
    return jsonify({
        'success': True,
        'data': 'Test API endpoint'
    })

if __name__ == '__main__':
    print("启动最小化Flask应用...")
    print("访问 http://localhost:5001/health 测试")
    
    # 使用最优化的配置启动
    app.run(
        debug=False,           # 禁用调试模式
        host='127.0.0.1',      # 只监听本地
        port=5001,             # 使用不同端口避免冲突
        use_reloader=False,    # 禁用自动重载
        threaded=True,         # 启用多线程
        processes=1            # 单进程
    )
