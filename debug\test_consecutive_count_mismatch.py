#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试连续上升/下跌计数不匹配问题
验证策略日志与交易条件判断的差异
"""

def analyze_consecutive_count_mismatch():
    """分析连续计数不匹配的问题"""
    
    print("🔍 连续上升/下跌计数不匹配问题分析")
    print("=" * 60)
    
    print("问题描述:")
    print("- 策略日志显示: 连续上升=0, 连续下跌=1")
    print("- 交易原因显示: '空开:2涨2跌'")
    print("- 这表明两个地方使用了不同的计算逻辑")
    
    print(f"\n问题根源:")
    print("1. 策略日志的连续计数来自主循环的简单逻辑")
    print("2. 交易条件判断使用_check_short_condition的复杂逻辑")
    print("3. 两个逻辑对'上涨'和'下跌'的定义不同")
    
    return True

def compare_counting_logic():
    """对比两种计数逻辑"""
    
    print(f"\n📊 两种计数逻辑对比")
    print("=" * 60)
    
    print("主循环简单逻辑 (策略日志使用):")
    simple_logic = '''
if row_close > row_open or (last_candle_high and row_high > last_candle_high and row_low > last_candle_low):
    continuous_up_count += 1
    continuous_down_count = 0
elif row_close < row_open or (last_candle_high and row_high < last_candle_high and row_low < last_candle_low):
    continuous_down_count += 1
    continuous_up_count = 0
else:
    continuous_up_count = 0
    continuous_down_count = 0
'''
    print(simple_logic)
    
    print("_check_short_condition复杂逻辑 (交易条件使用):")
    complex_logic = '''
# 使用新逻辑：如果最低最高都小于上一个K线，算下跌
if (current_candle['low'] < previous_candle['low'] and
    current_candle['high'] < previous_candle['high']):
    is_down = True  # 明确是下跌
elif (current_candle['low'] > previous_candle['low'] and
      current_candle['high'] > previous_candle['high']):
    is_down = False  # 明确是上涨
else:
    # 如果不满足新逻辑，回退到收盘价vs开盘价
    is_down = current_candle['close'] <= current_candle['open']
'''
    print(complex_logic)
    
    print(f"\n关键差异:")
    print("1. 简单逻辑: 主要基于收盘价vs开盘价")
    print("2. 复杂逻辑: 优先使用最高最低价的相对位置")
    print("3. 复杂逻辑更严格: 要求最高最低都满足条件")

def simulate_user_case():
    """模拟用户案例的计数差异"""
    
    print(f"\n🎯 用户案例模拟")
    print("=" * 60)
    
    # 模拟K线数据
    klines = [
        {'timestamp': '00:12:00', 'open': 0.19600, 'close': 0.19610, 'high': 0.19615, 'low': 0.19595},  # 上涨
        {'timestamp': '00:13:00', 'open': 0.19610, 'close': 0.19605, 'high': 0.19620, 'low': 0.19600},  # 下跌
        {'timestamp': '00:14:00', 'open': 0.19605, 'close': 0.19595, 'high': 0.19608, 'low': 0.19589},  # 下跌
        {'timestamp': '00:15:00', 'open': 0.19595, 'close': 0.19590, 'high': 0.19600, 'low': 0.19585},  # 下跌
        {'timestamp': '00:16:00', 'open': 0.19604, 'close': 0.19589, 'high': 0.19604, 'low': 0.19562},  # 当前K线
    ]
    
    print("模拟K线数据:")
    for i, k in enumerate(klines):
        print(f"  {i+1}. {k['timestamp']} 开:{k['open']:.5f} 收:{k['close']:.5f} 高:{k['high']:.5f} 低:{k['low']:.5f}")
    
    # 简单逻辑计算
    print(f"\n简单逻辑计算 (策略日志):")
    simple_up_count = 0
    simple_down_count = 0
    
    for i, k in enumerate(klines):
        if i == 0:
            continue  # 跳过第一个K线
        
        prev_k = klines[i-1]
        
        # 简单逻辑
        if (k['close'] > k['open'] or 
            (k['high'] > prev_k['high'] and k['low'] > prev_k['low'])):
            simple_up_count += 1
            simple_down_count = 0
        elif (k['close'] < k['open'] or 
              (k['high'] < prev_k['high'] and k['low'] < prev_k['low'])):
            simple_down_count += 1
            simple_up_count = 0
        else:
            simple_up_count = 0
            simple_down_count = 0
        
        trend = "上涨" if k['close'] > k['open'] else "下跌"
        print(f"  {k['timestamp']}: {trend}, 连续上涨={simple_up_count}, 连续下跌={simple_down_count}")
    
    print(f"  最终结果: 连续上涨={simple_up_count}, 连续下跌={simple_down_count}")
    
    # 复杂逻辑计算
    print(f"\n复杂逻辑计算 (_check_short_condition):")
    
    # 从最后一个K线开始向前计算连续下跌
    consecutive_down_count = 0
    for i in range(len(klines) - 1, -1, -1):
        current_candle = klines[i]
        if i == 0:
            is_down = current_candle['close'] <= current_candle['open']
        else:
            previous_candle = klines[i-1]
            if (current_candle['low'] < previous_candle['low'] and
                current_candle['high'] < previous_candle['high']):
                is_down = True
            elif (current_candle['low'] > previous_candle['low'] and
                  current_candle['high'] > previous_candle['high']):
                is_down = False
            else:
                is_down = current_candle['close'] <= current_candle['open']
        
        if is_down:
            consecutive_down_count += 1
            print(f"  {current_candle['timestamp']}: 下跌 (连续下跌={consecutive_down_count})")
        else:
            print(f"  {current_candle['timestamp']}: 上涨 (中断连续下跌)")
            break
    
    # 从下跌期开始前查找连续上涨
    start_idx = len(klines) - consecutive_down_count
    consecutive_up_count = 0
    
    if start_idx > 0:
        print(f"  查找连续上涨期 (从索引{start_idx-1}开始):")
        for i in range(start_idx - 1, -1, -1):
            current_candle = klines[i]
            if i == 0:
                is_up = current_candle['close'] > current_candle['open']
            else:
                previous_candle = klines[i-1]
                if (current_candle['low'] > previous_candle['low'] and
                    current_candle['high'] > previous_candle['high']):
                    is_up = True
                elif (current_candle['low'] < previous_candle['low'] and
                      current_candle['high'] < previous_candle['high']):
                    is_up = False
                else:
                    is_up = current_candle['close'] > current_candle['open']
            
            if is_up:
                consecutive_up_count += 1
                print(f"    {current_candle['timestamp']}: 上涨 (连续上涨={consecutive_up_count})")
            else:
                print(f"    {current_candle['timestamp']}: 下跌 (中断连续上涨)")
                break
    
    print(f"  复杂逻辑结果: 连续上涨={consecutive_up_count}, 连续下跌={consecutive_down_count}")
    
    # 对比结果
    print(f"\n结果对比:")
    print(f"  简单逻辑: 连续上涨={simple_up_count}, 连续下跌={simple_down_count}")
    print(f"  复杂逻辑: 连续上涨={consecutive_up_count}, 连续下跌={consecutive_down_count}")
    
    if simple_up_count != consecutive_up_count or simple_down_count != consecutive_down_count:
        print(f"  ❌ 计数不匹配！这解释了用户看到的问题")
        
        # 分析为什么会触发交易
        if consecutive_up_count >= 2 and consecutive_down_count >= 2:
            print(f"  ✅ 复杂逻辑满足'2涨2跌'条件，所以触发了交易")
        else:
            print(f"  ❌ 复杂逻辑不满足'2涨2跌'条件，不应该触发交易")
    else:
        print(f"  ✅ 计数匹配")

def propose_solution():
    """提出解决方案"""
    
    print(f"\n💡 解决方案")
    print("=" * 60)
    
    print("问题根源:")
    print("- 策略日志使用简单逻辑计算连续次数")
    print("- 交易条件使用复杂逻辑计算连续次数")
    print("- 两个逻辑不一致导致显示与实际不符")
    
    print(f"\n解决方案选项:")
    print("方案1: 统一使用复杂逻辑")
    print("- 修改策略日志计算，使用与交易条件相同的逻辑")
    print("- 优点: 显示与实际完全一致")
    print("- 缺点: 计算复杂度增加")
    
    print(f"\n方案2: 在策略日志中显示交易条件的实际计数")
    print("- 从trade_condition中获取实际的连续次数")
    print("- 优点: 简单直接，不影响现有逻辑")
    print("- 缺点: 需要修改交易条件返回值")
    
    print(f"\n方案3: 添加调试信息")
    print("- 在交易原因中显示实际计算的连续次数")
    print("- 在策略日志中添加备注说明差异")
    print("- 优点: 保持现有逻辑，增加透明度")
    
    print(f"\n推荐方案: 方案2")
    print("理由:")
    print("1. 最直接解决用户困惑")
    print("2. 不影响现有交易逻辑")
    print("3. 实现简单，风险最小")

if __name__ == "__main__":
    print("🔧 连续上升/下跌计数不匹配问题分析")
    print("=" * 70)
    
    # 分析问题
    analyze_consecutive_count_mismatch()
    
    # 对比逻辑
    compare_counting_logic()
    
    # 模拟用户案例
    simulate_user_case()
    
    # 提出解决方案
    propose_solution()
    
    print(f"\n✅ 分析完成")
    print("用户的观察是正确的：策略日志与交易条件使用了不同的计数逻辑")
    print("建议实施方案2：在策略日志中显示交易条件的实际计数")
