#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试具体的交易时间点
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def debug_specific_trade():
    """调试具体的交易时间点"""
    
    print("=== 调试具体的交易时间点 ===")
    print()
    
    # 创建策略分析器实例
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 获取00:30:00到00:45:00的数据（需要更多历史数据）
    try:
        with analyzer.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT timestamp, open_price as open, close_price as close,
                       high_price as high, low_price as low
                FROM crypto_prices
                WHERE currency = 'DOGE'
                AND timestamp >= '2025-06-06 00:30:00'
                AND timestamp <= '2025-06-06 00:45:00'
                ORDER BY timestamp
            """)
            results = cursor.fetchall()
            
            if not results:
                print("没有找到数据")
                return
                
            print(f"找到 {len(results)} 条数据")
            print()
            
            # 模拟涨跌判断
            minute_candles = []
            
            for i, row in enumerate(results):
                print(f"=== {row['timestamp']} ===")
                
                # 计算涨跌
                open_price = float(row['open'])
                close_price = float(row['close'])
                high_price = float(row['high'])
                low_price = float(row['low'])
                
                # 新的涨跌判断标准
                is_up_final = None
                if len(minute_candles) > 0:
                    prev_candle = minute_candles[-1]
                    # 上涨：当前最高值和最低值都比上一分钟高
                    if high_price > prev_candle['high'] and low_price > prev_candle['low']:
                        is_up_final = True
                    # 下跌：当前最高值和最低值都比上一分钟低
                    elif high_price < prev_candle['high'] and low_price < prev_candle['low']:
                        is_up_final = False
                    else:
                        # 横盘或混合情况，暂时按收盘价判断
                        is_up_final = close_price > open_price
                else:
                    # 第一条数据，按收盘价判断
                    is_up_final = close_price > open_price
                
                print(f"开盘: {open_price:.6f}, 收盘: {close_price:.6f}, 最高: {high_price:.6f}, 最低: {low_price:.6f}")
                print(f"涨跌判断: {'涨' if is_up_final else '跌'}")
                
                if len(minute_candles) > 0:
                    prev_candle = minute_candles[-1]
                    print(f"上一分钟: 最高{prev_candle['high']:.6f}, 最低{prev_candle['low']:.6f}")
                    print(f"比较结果: 最高值{'高于' if high_price > prev_candle['high'] else '低于'}上一分钟, 最低值{'高于' if low_price > prev_candle['low'] else '低于'}上一分钟")
                
                # 添加到历史数据
                candle_data = {
                    'timestamp': row['timestamp'],
                    'open': open_price,
                    'close': close_price,
                    'high': high_price,
                    'low': low_price,
                    'is_up': is_up_final
                }
                minute_candles.append(candle_data)
                
                print()

            # 在循环结束后分析特定时间点
            print("\n" + "="*50)
            print("分析00:42:00的开仓条件")
            print("="*50)

            # 找到00:42:00的索引
            target_index = -1
            for i, candle in enumerate(minute_candles):
                timestamp_str = str(candle['timestamp'])
                print(f"检查时间戳: {timestamp_str}")
                if '00:42:00' in timestamp_str:
                    target_index = i
                    print(f"找到00:42:00，索引: {i}")
                    break

            if target_index >= 0:
                # 需要先跌5分钟再涨2分钟
                lookback_buy = 5
                lookback_sell = 2
                total_needed = lookback_buy + lookback_sell - 1  # 6分钟

                if target_index >= total_needed:
                    # 获取历史数据 + 当前数据
                    start_index = target_index - total_needed
                    all_candles = minute_candles[start_index:target_index+1]

                    print(f"分析的7分钟数据:")
                    for j, candle in enumerate(all_candles):
                        trend = "涨" if candle['is_up'] else "跌"
                        print(f"  {j+1}. {candle['timestamp']} - {trend}")

                    # 检查做多条件：先跌5分钟，再涨2分钟
                    down_period = all_candles[:lookback_buy]  # 前5分钟
                    up_period = all_candles[lookback_buy:]    # 后2分钟（包含当前）

                    print(f"\n前5分钟（下跌期）:")
                    for j, candle in enumerate(down_period):
                        trend = "涨" if candle['is_up'] else "跌"
                        print(f"  {j+1}. {candle['timestamp']} - {trend}")

                    print(f"\n后2分钟（上涨期）:")
                    for j, candle in enumerate(up_period):
                        trend = "涨" if candle['is_up'] else "跌"
                        print(f"  {j+1}. {candle['timestamp']} - {trend}")

                    # 检查前期是否连续下跌
                    all_down_in_down_period = all(not candle['is_up'] for candle in down_period)
                    # 检查后期是否连续上涨
                    all_up_in_up_period = all(candle['is_up'] for candle in up_period)

                    print(f"\n前5分钟连续下跌: {all_down_in_down_period}")
                    print(f"后2分钟连续上涨: {all_up_in_up_period}")
                    print(f"满足做多条件: {all_down_in_down_period and all_up_in_up_period}")

            print("\n" + "="*50)
            print("分析00:43:00的平仓条件")
            print("="*50)

            # 找到00:43:00的索引
            target_index = -1
            for i, candle in enumerate(minute_candles):
                timestamp_str = str(candle['timestamp'])
                if '00:43:00' in timestamp_str:
                    target_index = i
                    break

            if target_index >= 0:
                # 连续2分钟下跌（含当前）
                lookback_sell = 2

                if target_index >= lookback_sell - 1:
                    # 获取最近2分钟（包含当前）
                    start_index = target_index - (lookback_sell - 1)
                    recent_candles = minute_candles[start_index:target_index+1]

                    print(f"最近2分钟数据:")
                    for j, candle in enumerate(recent_candles):
                        trend = "涨" if candle['is_up'] else "跌"
                        print(f"  {j+1}. {candle['timestamp']} - {trend}")

                    # 检查是否连续下跌
                    all_down = all(not candle['is_up'] for candle in recent_candles)

                    print(f"\n连续2分钟下跌: {all_down}")
                    print(f"满足平多条件: {all_down}")
                
    except Exception as e:
        print(f"调试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_specific_trade()
