#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
专门测试做多保护逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def test_long_protection():
    """测试做多保护逻辑"""
    print("🧪 测试做多保护逻辑")
    print("=" * 50)
    
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 测试参数
    test_params = {
        'current_position': 0,
        'last_close_time': None,
        'rest_minutes': 0,
        'lookback_minutes_buy': 2,
        'lookback_minutes_sell': 2,
        'buy_rate': 0.2,  # 使用较小的buy_rate让触发价格更容易达到
        'sell_rate': 0.015,
        'price_adjust_rate': 0.001,  # 0.1%
        'minute_candles': []
    }
    
    # 模拟K线数据 - 先跌后涨，触发价格较低
    candles_data = [
        # 下跌期
        {'timestamp': '2025-03-03T08:41:00+00:00', 'open': 0.23700, 'close': 0.23680, 'high': 0.23710, 'low': 0.23670, 'is_up': False},
        {'timestamp': '2025-03-03T08:42:00+00:00', 'open': 0.23680, 'close': 0.23660, 'high': 0.23690, 'low': 0.23650, 'is_up': False},
        # 上涨期
        {'timestamp': '2025-03-03T08:43:00+00:00', 'open': 0.23660, 'close': 0.23680, 'high': 0.23690, 'low': 0.23650, 'is_up': True},
        {'timestamp': '2025-03-03T08:44:00+00:00', 'open': 0.23680, 'close': 0.23700, 'high': 0.23710, 'low': 0.23670, 'is_up': True},
        # 当前K线 - 继续上涨，触发价格在范围内但需要保护
        {'timestamp': '2025-03-03T08:45:00+00:00', 'open': 0.23677, 'close': 0.23720, 'high': 0.23750, 'low': 0.23617, 'is_up': True}
    ]
    
    test_params['minute_candles'] = candles_data
    
    # 构造当前K线数据
    current_row = {
        'timestamp': '2025-03-03T08:45:00+00:00',
        'open': 0.23677,
        'close': 0.23720,
        'high': 0.23750,
        'low': 0.23617,
        'is_complete': 1
    }
    
    print(f"当前K线：开盘{current_row['open']:.5f} 最高{current_row['high']:.5f} 最低{current_row['low']:.5f}")
    print(f"buy_rate: {test_params['buy_rate']}")
    print()
    
    # 手动计算预期触发价格
    last_down_candle = candles_data[1]  # 下跌期最后一个K线
    price_range = last_down_candle['high'] - last_down_candle['low']
    expected_trigger_price = last_down_candle['low'] + (price_range * test_params['buy_rate'])
    print(f"预期触发价格：{expected_trigger_price:.5f}")
    print(f"当前最高价：{current_row['high']:.5f}")
    print(f"是否达到触发条件：{'是' if current_row['high'] > expected_trigger_price else '否'}")
    print()
    
    # 测试交易条件
    result = analyzer.trade_condition_new(current_row, test_params)
    
    if result and result['action']:
        print(f"✅ 交易信号：{result['action']}")
        print(f"📝 交易原因：{result['reason']}")
        print(f"💰 触发价格：{result['trigger_price']:.5f}")
        
        if 'actual_trigger_price' in result:
            print(f"💰 实际触发价：{result['actual_trigger_price']:.5f}")
            
            # 验证保护逻辑
            expected_protection_price = current_row['open'] * (1 + test_params['price_adjust_rate'])
            actual_protection_price = result['actual_trigger_price']
            
            print(f"🔍 保护价格验证：")
            print(f"   期望保护价：{expected_protection_price:.5f} (开盘价+{test_params['price_adjust_rate']*100:.1f}%)")
            print(f"   实际保护价：{actual_protection_price:.5f}")
            
            if abs(actual_protection_price - expected_protection_price) < 0.00001:
                print("   ✅ 保护价格正确：体现上涨趋势")
            else:
                print("   ❌ 保护价格错误")
                
            # 验证价格在K线范围内
            if current_row['low'] <= actual_protection_price <= current_row['high']:
                print("   ✅ 保护价格在K线范围内")
            else:
                print("   ❌ 保护价格超出K线范围")
        
    else:
        print("❌ 未产生交易信号")
        if result:
            print(f"📝 原因：{result['reason']}")
    
    print()
    print("✅ 做多保护逻辑测试完成")

if __name__ == "__main__":
    test_long_protection()
