# 新交易条件判断方法 (trade_condition_new)

## 概述

`trade_condition_new()` 是一个基于分钟级K线数据的新交易条件判断方法，通过分析连续涨跌模式来识别趋势反转机会。

## 核心特点

### 1. 分钟级K线分析
- 只处理完整的分钟K线数据 (`is_complete == 1`)
- 维护最近20条分钟K线数据的滚动缓存
- 每条K线记录开盘价、收盘价、最高价、最低价和涨跌状态

### 2. 连续涨跌判断
- 使用 `lookback-minutes-buy` 和 `lookback-minutes-sell` 参数
- 倒序分析：先看最近的 `lookback-minutes-sell` 次，再看之前的 `lookback-minutes-buy` 次
- 识别"先跌后涨"或"先涨后跌"的反弹模式

### 3. 价格突破确认
- 使用 `buy-rate` 参数计算关键价位阈值
- 开仓时必须同时满足连续涨跌模式和价格突破条件
- 平仓时只需要满足连续反向运动条件

## 参数说明

### lookback-minutes-buy
- **含义**：需要先连续涨/跌的分钟数
- **示例**：`lookback-minutes-buy=5` 表示需要先连续涨/跌5分钟
- **默认值**：5

### lookback-minutes-sell  
- **含义**：然后反向运动的分钟数
- **示例**：`lookback-minutes-sell=2` 表示然后反向运动2分钟
- **默认值**：2

### buy-rate
- **含义**：价格突破阈值的计算方式
- **取值说明**：
  - `buy-rate=1.0`：涨到最低K线的最高值 / 跌到最高K线的最低值
  - `buy-rate=0.5`：涨到最低值一半幅度 / 跌到最高值一半幅度  
  - `buy-rate=2.0`：涨到最低值两倍幅度 / 跌到最高值两倍幅度
- **默认值**：1.0

## 交易逻辑

### 开仓条件（需要同时满足）

#### 做多条件
1. **连续涨跌模式**：先跌 `lookback-minutes-buy` 次，再涨 `lookback-minutes-sell` 次
2. **价格突破**：当前价格 >= 最低K线的阈值价格

#### 做空条件  
1. **连续涨跌模式**：先涨 `lookback-minutes-buy` 次，再跌 `lookback-minutes-sell` 次
2. **价格突破**：当前价格 <= 最高K线的阈值价格

### 平仓条件

#### 平多条件
- 连续下跌 `lookback-minutes-sell` 次即可平多

#### 平空条件
- 连续上涨 `lookback-minutes-sell` 次即可平空

## 使用方法

### 1. 命令行使用
```bash
python strategy_analyzer.py --use-new-condition --lookback-minutes-buy=5 --lookback-minutes-sell=2 --buy-rate=1.0
```

### 2. 程序调用
```python
result = analyzer.analyze_rebound_strategy(
    start_time=start_time,
    end_time=end_time,
    use_new_condition=True,  # 启用新方法
    lookback_minutes_buy=5,
    lookback_minutes_sell=2,
    buy_rate=1.0,
    currency='DOGE'
)
```

### 3. 测试验证
```bash
# 运行测试对比
python test_new_condition.py

# 运行示例
python example_new_condition.py
```

## 策略示例

### 示例1：保守策略
```python
params = {
    'lookback_minutes_buy': 5,   # 先跌5分钟
    'lookback_minutes_sell': 2,  # 再涨2分钟
    'buy_rate': 1.0,             # 涨到最低K线最高值
}
```
- **特点**：要求较强的反弹信号，减少假突破
- **适用**：震荡市场，追求稳定收益

### 示例2：激进策略
```python
params = {
    'lookback_minutes_buy': 3,   # 先跌3分钟
    'lookback_minutes_sell': 1,  # 再涨1分钟
    'buy_rate': 0.5,             # 涨到一半幅度
}
```
- **特点**：更快响应市场变化，交易频率高
- **适用**：趋势市场，追求高收益

### 示例3：超激进策略
```python
params = {
    'lookback_minutes_buy': 2,   # 先跌2分钟
    'lookback_minutes_sell': 1,  # 再涨1分钟
    'buy_rate': 0.3,             # 涨到30%幅度
}
```
- **特点**：极快响应，高频交易
- **适用**：高波动市场，需要严格风控

## 优势对比

### 相比旧方法的优势
1. **更精确的趋势识别**：基于连续K线模式而非单点判断
2. **减少假信号**：需要满足连续涨跌条件才触发
3. **更好的风控**：使用关键价位进行开仓确认
4. **适应性强**：可以通过调整参数适应不同市场环境
5. **逻辑清晰**：开仓和平仓条件分离，易于理解和调试

### 风险提示
1. **参数敏感性**：不同参数组合会显著影响策略表现
2. **市场适应性**：需要根据不同币种和市场环境调整参数
3. **延迟性**：需要等待足够的历史数据才能做出判断
4. **假突破风险**：在震荡市场中可能产生频繁的小额亏损

## 调试建议

1. **从保守参数开始**：先使用较大的 `lookback-minutes-buy` 值
2. **逐步优化**：根据回测结果调整 `buy-rate` 参数
3. **多时间段测试**：在不同市场环境下验证策略稳定性
4. **风险控制**：设置合理的止损和仓位管理规则
