import cProfile
import pstats
from line_profiler import LineProfiler
from doge_price_fetcher import get_crypto_prices, save_kline_data
import datetime

def profile_with_cprofile():
    """使用cProfile进行方法级性能分析"""
    profiler = cProfile.Profile()
    start_date = datetime.datetime(2025, 1, 21, 23, 59)
    end_date = datetime.datetime(2025, 2, 3, 0, 0)
    
    profiler.enable()
    get_crypto_prices('DOGE-USDT', start_time=start_date, end_time=end_date)
    profiler.disable()
    
    # 保存分析结果到文件
    stats = pstats.Stats(profiler)
    stats.sort_stats('cumulative')  # 按累计时间排序
    stats.print_stats()
    stats.dump_stats('crypto_profile.stats')
    
def profile_with_line_profiler():
    """使用line_profiler进行行级性能分析"""
    profiler = LineProfiler()
    
    # 添加要分析的函数
    profiler.add_function(get_crypto_prices)
    profiler.add_function(save_kline_data)
    
    # 运行分析
    start_date = datetime.datetime(2025, 1, 21, 23, 59)
    end_date = datetime.datetime(2025, 2, 3, 0, 0)
    
    wrapped = profiler(get_crypto_prices)
    wrapped('DOGE-USDT', start_time=start_date, end_time=end_date)
    
    # 保存分析结果
    profiler.print_stats()
    profiler.dump_stats('line_profile.stats')

if __name__ == "__main__":
    print("开始方法级性能分析...")
    profile_with_cprofile()
    
    print("\n开始行级性能分析...")
    profile_with_line_profiler() 