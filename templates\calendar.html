<!DOCTYPE html>
<html>
<head>
    <title>日历</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/fullcalendar.min.css" rel="stylesheet">
    <link href="/static/css/animate.min.css" rel="stylesheet">
    <style>
        :root {
            --positive-color: #00b07c;  /* 深绿色 */
            --negative-color: #dc3545;  /* 深红色 */
            --neutral-color: #556ee6;
            --border-radius: 8px;
            --card-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            margin: 0;
            padding: 0;
        }

        .container-fluid {
            height: 100%;
            padding: 1rem;
        }

        .row {
            height: calc(100vh - 2rem);
        }

        .calendar-container {
            height: calc(100vh - 100px);  /* 设置容器高度为视窗高度减去100px */
            display: flex;
            flex-direction: column;
        }

        .card {
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
            margin-bottom: 20px;
            border: none;
            background: white;
        }

        .calendar-card {
            height: 100%;
            margin-bottom: 0;
        }

        .fc {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
            height: 100%;
        }

        .fc .fc-view-harness {
            height: calc(100% - 50px) !important;
        }

        .filter-section {
            padding: 20px;
            flex: 0 0 auto;  /* 不伸缩，保持自身大小 */
            margin-bottom: 20px;
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 1px solid #e2e5e8;
            padding: 10px 15px;
            height: auto;
        }

        .form-control:focus, .form-select:focus {
            box-shadow: 0 0 0 0.2rem rgba(85,110,230,0.25);
            border-color: var(--neutral-color);
        }

        .btn-primary {
            background-color: var(--neutral-color);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: #4458b8;
            transform: translateY(-1px);
        }

        .fc-toolbar-title {
            font-size: 1.5em !important;
            color: #2c3e50;
            font-weight: 600;
        }

        .fc-button-primary {
            background-color: var(--neutral-color) !important;
            border: none !important;
            border-radius: 8px !important;
        }

        .fc-event {
            border: none !important;
            padding: 2px 5px;
            background: transparent !important;
            width: 100% !important;
            margin: 0 !important;
        }

        .fc-event-title {
            font-size: 1.4em !important;
            font-weight: 700 !important;
            text-align: center;
            display: block;
            padding: 8px !important;
            margin: 2px;
            border-radius: 4px;
            background: transparent !important;
            text-shadow: 0 0 1px rgba(0, 0, 0, 0.2);
        }

        .profit-positive {
            color: var(--positive-color) !important;
        }

        .profit-negative {
            color: var(--negative-color) !important;
        }

        .fc-daygrid-event-harness {
            width: 100% !important;
            margin: 0 !important;
        }

        .fc-daygrid-day-events {
            margin: 0 !important;
            padding: 0 !important;
        }

        .fc-daygrid-day-number {
            font-size: 1.1em !important;
            padding: 8px !important;
            color: #495057;
        }

        .summary-card {
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
        }

        .summary-title {
            font-size: 0.9em;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .summary-value {
            font-size: 1.5em;
            font-weight: 600;
            color: #2c3e50;
        }

        .tooltip-inner {
            background-color: #2c3e50;
            border-radius: 8px;
            padding: 10px;
        }

        .error-alert {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            min-width: 300px;
            max-width: 500px;
            display: none;
        }

        .error-alert pre {
            margin: 10px 0 0 0;
            white-space: pre-wrap;
            font-size: 12px;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
        }

        #calendar {
            flex: 1;  /* 占用剩余空间 */
            min-height: 600px;  /* 最小高度 */
            margin-top: 20px;
        }

        .btn-group {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-md-3">
                <div class="card">
                    <div class="filter-section">
                        <h4>策略筛选</h4>
                        <form id="filterForm">
                            <div class="mb-3">
                                <label class="form-label">买入阈值</label>
                                <input type="number" class="form-control" id="buy_rate" step="0.001"  value="0.02">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">卖出阈值</label>
                                <input type="number" class="form-control" id="sell_rate" step="0.001"  value="0.02">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">买入回看分钟数</label>
                                <input type="number" class="form-control" id="lookback_minutes_buy" min="1" max="10" value="4">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">卖出回看分钟数</label>
                                <input type="number" class="form-control" id="lookback_minutes_sell" min="1" max="10" value="4">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">休息时间(分钟)</label>
                                <input type="number" class="form-control" id="rest_minutes" min="0" value="0">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">最小触发止损休息时间(分钟)</label>
                                <input type="number" class="form-control" id="min_trigger_rest" min="0" value="0">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">币种</label>
                                <select class="form-select" id="currency">
                                    <option value="">加载中...</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">策略ID</label>
                                <input type="number" class="form-control" id="strategy_id" min="1" placeholder="输入策略ID直接查询">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">月份</label>
                                <input type="month" class="form-control" id="month">
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-filter me-2"></i>应用筛选
                            </button>
                        </form>
                    </div>
                </div>

                <div class="summary-card">
                    <div class="summary-title">本月总收益</div>
                    <div class="summary-value" id="monthlyProfit">$0.00</div>
                </div>

                <div class="summary-card">
                    <div class="summary-title">本月交易次数</div>
                    <div class="summary-value" id="monthlyTrades">0</div>
                </div>
            </div>
            <div class="col-md-9">
                <div class="calendar-container">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="selectMonth('current')">本月</button>
                        <button type="button" class="btn btn-outline-primary" onclick="selectMonth('last')">上月</button>
                        <button type="button" class="btn btn-outline-primary" onclick="selectMonth('previous')">前一个月</button>
                    </div>
                    <div id="calendar"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="alert alert-danger alert-dismissible fade error-alert" role="alert">
        <strong>错误!</strong>
        <p class="error-message mb-0"></p>
        <pre class="error-details" style="display: none;"></pre>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/fullcalendar.min.js"></script>
    <script src="/static/js/fontawesome.min.js"></script>
    <script>
        let calendar;  // 设置为全局变量
        let currentStrategyId;  // 添加全局变量保存当前策略ID
        let calendarDataCache = {};  // 添加缓存对象，用于存储已请求的月份数据
        let lastRequestParams = null;  // 记录上一次请求的参数

        function updateStats(monthlyProfit, monthlyTrades) {
            document.getElementById('monthlyProfit').textContent = `$${monthlyProfit.toFixed(2)}`;
            document.getElementById('monthlyProfit').style.color =
                monthlyProfit >= 0 ? 'var(--positive-color)' : 'var(--negative-color)';
            document.getElementById('monthlyTrades').textContent = monthlyTrades;
        }

        function showError(error) {
            const errorAlert = document.querySelector('.error-alert');
            const errorMessage = errorAlert.querySelector('.error-message');
            const errorDetails = errorAlert.querySelector('.error-details');

            errorMessage.textContent = '获取数据失败';
            errorDetails.textContent = error;
            errorDetails.style.display = 'block';
            errorAlert.classList.add('show');
            errorAlert.style.display = 'block';

            setTimeout(() => {
                errorAlert.classList.remove('show');
                setTimeout(() => {
                    errorAlert.style.display = 'none';
                }, 150);
            }, 5000);
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认月份为当前月份
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const currentMonthStr = `${year}-${month}`;
            document.getElementById('month').value = currentMonthStr;

            // 获取币种列表
            fetch('/api/currencies')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0 && data.data) {
                        const currencySelect = document.getElementById('currency');
                        currencySelect.innerHTML = ''; // 清空现有选项

                        data.data.forEach(currency => {
                            const option = document.createElement('option');
                            option.value = currency;
                            option.textContent = `${currency}-USDT`;
                            currencySelect.appendChild(option);
                        });

                        // 如果URL中有currency参数，设置对应的值
                        const params = new URLSearchParams(window.location.search);
                        const currencyParam = params.get('currency');
                        if (currencyParam && data.data.includes(currencyParam)) {
                            currencySelect.value = currencyParam;
                        }
                    } else {
                        console.error('获取币种列表失败:', data.message);
                        showError('获取币种列表失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('获取币种列表出错:', error);
                    showError('获取币种列表出错: ' + error.message);
                });

            // 检查URL中是否有策略ID参数
            const urlParams = new URLSearchParams(window.location.search);
            const urlStrategyId = urlParams.get('strategy_id');

            if (urlStrategyId) {
                // 如果URL中有策略ID，优先加载该策略
                console.log('从URL加载策略ID:', urlStrategyId);
                document.getElementById('strategy_id').value = urlStrategyId;

                // 使用新的轻量级接口获取策略详情
                fetch(`/api/strategy_info?id=${urlStrategyId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.strategy) {
                            const strategy = data.strategy;
                            console.log('获取到策略详情:', strategy);

                            // 更新表单中的值
                            document.getElementById('buy_rate').value = strategy.buy_rate || 0.02;
                            document.getElementById('sell_rate').value = strategy.sell_rate || 0.02;
                            document.getElementById('lookback_minutes_buy').value = strategy.lookback_minutes_buy || 4;
                            document.getElementById('lookback_minutes_sell').value = strategy.lookback_minutes_sell || 4;
                            document.getElementById('rest_minutes').value = strategy.rest_minutes || 0;
                            document.getElementById('min_trigger_rest').value = strategy.min_trigger_rest || 0;

                            // 保存策略ID到全局变量
                            currentStrategyId = parseInt(urlStrategyId);

                            // 如果有开始时间，跳转到对应月份
                            if (strategy.start_time) {
                                const startDate = new Date(strategy.start_time);
                                const year = startDate.getFullYear();
                                const month = String(startDate.getMonth() + 1).padStart(2, '0');
                                const monthStr = `${year}-${month}`;

                                console.log('跳转到策略开始月份:', monthStr);
                                document.getElementById('month').value = monthStr;
                                calendar.gotoDate(monthStr + '-01');

                                // 清除缓存并触发数据刷新
                                calendarDataCache = {};
                                lastRequestParams = null;
                                setTimeout(() => {
                                    calendar.refetchEvents();
                                }, 100);
                            }
                        } else {
                            console.error('未找到策略详情:', data.message || '未知错误');
                            showError('未找到策略详情，ID: ' + urlStrategyId);
                        }
                    })
                    .catch(error => {
                        console.error('获取策略详情失败:', error);
                        showError('获取策略详情失败: ' + error.message);
                    });
            } else {
                // 如果URL中没有策略ID，加载最优策略
                fetch('/api/best_strategy')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 更新表单中的值
                            document.getElementById('buy_rate').value = data.strategy.buy_rate;
                            document.getElementById('sell_rate').value = data.strategy.sell_rate;
                            document.getElementById('lookback_minutes_buy').value = data.strategy.lookback_minutes_buy;
                            document.getElementById('lookback_minutes_sell').value = data.strategy.lookback_minutes_sell;
                            document.getElementById('rest_minutes').value = data.strategy.rest_minutes;
                            document.getElementById('min_trigger_rest').value = data.strategy.min_trigger_rest;
                            if (data.strategy.start_time) {
                                // 保存策略ID到全局变量
                                currentStrategyId = data.strategy.id;
                                // 设置月份并跳转到对应日期
                                document.getElementById('month').value = data.strategy.start_time;
                                calendar.gotoDate(data.strategy.start_time + '-01');

                                // 清除缓存并触发数据刷新
                                calendarDataCache = {};
                                lastRequestParams = null;
                                setTimeout(() => {
                                    calendar.refetchEvents();
                                }, 100);
                            }
                        }
                    })
                    .catch(error => console.error('获取最优策略失败:', error));
            }

            // 处理筛选表单提交
            document.getElementById('filterForm').addEventListener('submit', function(e) {
                e.preventDefault();

                // 检查是否输入了策略ID
                const strategyIdInput = document.getElementById('strategy_id');
                if (strategyIdInput.value.trim()) {
                    // 如果输入了策略ID，使用策略ID进行查询
                    const newStrategyId = parseInt(strategyIdInput.value.trim(), 10);

                    // 如果策略ID变化了，尝试获取策略详情并跳转到对应月份
                    if (newStrategyId !== currentStrategyId) {
                        fetch(`/api/strategy_info?id=${newStrategyId}`)
                            .then(response => response.json())
                            .then(data => {
                                if (data.success && data.strategy) {
                                    const strategy = data.strategy;
                                    console.log('获取到策略详情:', strategy);

                                    // 如果有开始时间，跳转到对应月份
                                    if (strategy.start_time) {
                                        const startDate = new Date(strategy.start_time);
                                        const year = startDate.getFullYear();
                                        const month = String(startDate.getMonth() + 1).padStart(2, '0');
                                        const monthStr = `${year}-${month}`;

                                        console.log('跳转到策略开始月份:', monthStr);
                                        document.getElementById('month').value = monthStr;
                                        calendar.gotoDate(monthStr + '-01');
                                    }

                                    // 更新当前策略ID
                                    currentStrategyId = newStrategyId;

                                    // 清除缓存并刷新数据
                                    calendarDataCache = {};
                                    lastRequestParams = null;
                                    setTimeout(() => {
                                        calendar.refetchEvents();
                                    }, 100);
                                } else {
                                    console.error('未找到策略详情:', data.message || '未知错误');
                                    showError('未找到策略详情，ID: ' + newStrategyId);

                                    // 仍然更新当前策略ID并刷新数据
                                    currentStrategyId = newStrategyId;
                                    calendarDataCache = {};
                                    lastRequestParams = null;
                                    calendar.refetchEvents();
                                }
                            })
                            .catch(error => {
                                console.error('获取策略详情失败:', error);
                                showError('获取策略详情失败: ' + error.message);

                                // 仍然更新当前策略ID并刷新数据
                                currentStrategyId = newStrategyId;
                                calendarDataCache = {};
                                lastRequestParams = null;
                                calendar.refetchEvents();
                            });
                    } else {
                        // 策略ID没变，只刷新数据
                        calendarDataCache = {};
                        lastRequestParams = null;
                        calendar.refetchEvents();
                    }
                } else {
                    // 否则使用其他筛选条件
                    currentStrategyId = null;
                    calendarDataCache = {};
                    lastRequestParams = null;
                    calendar.refetchEvents();
                }
            });

            // 监听月份选择变化
            document.getElementById('month').addEventListener('change', function(e) {
                if (e.target.value) {
                    // 清除缓存，确保获取新月份的数据
                    calendarDataCache = {};
                    lastRequestParams = null;

                    // 跳转到选择的月份
                    calendar.gotoDate(e.target.value + '-01');

                    // 使用延时确保不会触发多次请求
                    setTimeout(() => {
                        calendar.refetchEvents();
                    }, 100);
                }
            });

            var calendarEl = document.getElementById('calendar');

            // 获取当前设置的月份值
            const initialMonthValue = document.getElementById('month').value;
            const initialDate = initialMonthValue ? initialMonthValue + '-01' : null;

            calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                initialDate: initialDate, // 使用设置的月份作为初始日期
                height: 'auto',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth'
                },
                buttonText: {
                    today: '今天',
                    prev: '上一月',
                    next: '下一月'
                },
                locale: 'zh-cn',
                dayMaxEvents: 1,
                views: {
                    dayGridMonth: {
                        // 月视图加载完成后触发
                        titleFormat: { year: 'numeric', month: 'long' },
                        monthDidMount: function(info) {
                            const currentMonth = info.view.currentStart;
                            const year = currentMonth.getFullYear();
                            const month = String(currentMonth.getMonth() + 1).padStart(2, '0');
                            const monthStr = `${year}-${month}`;

                            // 只有当月份真的变化时才更新和请求数据
                            if (document.getElementById('month').value !== monthStr) {
                                document.getElementById('month').value = monthStr;
                                // 使用延时确保不会触发多次请求
                                setTimeout(() => {
                                    calendar.refetchEvents();
                                }, 50);
                            }
                        }
                    }
                },
                events: function(info, successCallback, failureCallback) {
                    const month = document.getElementById('month').value;

                    // 如果没有选择月份，直接返回空数组
                    if (!month) {
                        successCallback([]);
                        return;
                    }

                    let filters = {
                        month: month
                    };

                    // 检查策略ID输入框
                    const strategyIdInput = document.getElementById('strategy_id');
                    if (strategyIdInput.value.trim()) {
                        // 如果输入了策略ID，优先使用策略ID
                        filters.strategy_id = strategyIdInput.value.trim();
                    } else if (currentStrategyId) {
                        // 如果有全局策略ID，使用全局策略ID
                        filters.strategy_id = currentStrategyId;
                    } else {
                        // 否则使用表单参数
                        filters = {
                            ...filters,
                            buy_rate: document.getElementById('buy_rate').value,
                            sell_rate: document.getElementById('sell_rate').value,
                            lookback_minutes_buy: document.getElementById('lookback_minutes_buy').value,
                            lookback_minutes_sell: document.getElementById('lookback_minutes_sell').value,
                            rest_minutes: document.getElementById('rest_minutes').value,
                            min_trigger_rest: document.getElementById('min_trigger_rest').value,
                            currency: document.getElementById('currency').value
                        };
                    }

                    // 生成缓存键
                    const cacheKey = JSON.stringify(filters);

                    // 检查是否与上一次请求参数相同
                    if (lastRequestParams === cacheKey) {
                        console.log('跳过重复请求，参数相同:', filters);

                        // 即使跳过请求，也要确保返回缓存的数据给日历
                        if (calendarDataCache[cacheKey]) {
                            const cachedData = calendarDataCache[cacheKey];
                            successCallback(cachedData.events);
                            updateStats(cachedData.monthlyProfit, cachedData.monthlyTrades);
                            window.dateToStrategyMap = cachedData.dateToStrategyMap;
                        } else {
                            // 如果没有缓存数据，强制发起请求
                            console.log('没有缓存数据，强制发起请求');
                            lastRequestParams = null; // 重置请求参数，强制发起请求
                            calendar.refetchEvents(); // 重新请求数据
                        }
                        return;
                    }

                    // 检查缓存中是否已有数据
                    if (calendarDataCache[cacheKey]) {
                        console.log('使用缓存数据:', filters);
                        const cachedData = calendarDataCache[cacheKey];
                        successCallback(cachedData.events);
                        updateStats(cachedData.monthlyProfit, cachedData.monthlyTrades);
                        window.dateToStrategyMap = cachedData.dateToStrategyMap;
                        lastRequestParams = cacheKey;
                        return;
                    }

                    // 记录当前请求参数
                    lastRequestParams = cacheKey;

                    console.log('请求新数据:', filters);
                    fetch('/api/calendar_data?' + new URLSearchParams(filters))
                        .then(response => response.json())
                        .then(data => {
                            if (data.error) {
                                console.error('日历数据请求错误:', data.error);
                                showError(data.error);
                                // 返回空数据，避免日历显示错误
                                successCallback([]);
                                return;
                            }

                            // 确保data是数组
                            if (!Array.isArray(data)) {
                                console.error('返回的日历数据不是数组:', data);
                                showError('返回的日历数据格式错误');
                                successCallback([]);
                                return;
                            }

                            let monthlyProfit = 0;
                            let monthlyTrades = 0;
                            const dateToStrategyMap = {};

                            const events = data.map(item => {
                                // 添加安全检查
                                if (!item || typeof item !== 'object') {
                                    console.warn('日历数据项格式错误:', item);
                                    return null;
                                }

                                const profitAmount = parseFloat(item.profit_amount || 0);
                                const tradesCount = parseInt(item.trades_count || 0);

                                monthlyProfit += profitAmount;
                                monthlyTrades += tradesCount;

                                if (item.date) {
                                    dateToStrategyMap[item.date] = item.strategy_id;
                                }

                                const isPositive = profitAmount >= 0;

                                if (item.strategy_id) {
                                    currentStrategyId = item.strategy_id;
                                }

                                return {
                                    title: `${isPositive ? '↑' : '↓'} $${Math.abs(profitAmount).toFixed(2)}`,
                                    start: item.date,
                                    allDay: true,
                                    display: 'list-item',
                                    className: isPositive ? 'profit-positive' : 'profit-negative',
                                    extendedProps: {
                                        profitAmount: profitAmount,
                                        trades: tradesCount,
                                        strategy_id: item.strategy_id
                                    }
                                };
                            }).filter(event => event !== null); // 过滤掉无效的事件

                            // 更新月度统计
                            updateStats(monthlyProfit, monthlyTrades);

                            window.dateToStrategyMap = dateToStrategyMap;

                            // 保存到缓存
                            calendarDataCache[cacheKey] = {
                                events: events,
                                monthlyProfit: monthlyProfit,
                                monthlyTrades: monthlyTrades,
                                dateToStrategyMap: dateToStrategyMap
                            };

                            successCallback(events);
                        })
                        .catch(error => {
                            console.error('Error fetching calendar data:', error);
                            showError('获取日历数据失败: ' + error.message);
                            // 返回空数据，避免日历显示错误
                            successCallback([]);
                            failureCallback(error);
                        });
                },
                eventDidMount: function(info) {
                    info.el.style.width = '100%';
                    info.el.style.margin = '0';
                    info.el.style.padding = '0';
                    const trades = info.event.extendedProps.trades;
                    const profit = info.event.extendedProps.profitAmount;
                    const tooltipContent = `
                        交易次数: ${trades}
                        总收益: $${profit.toFixed(2)}
                    `;

                    new bootstrap.Tooltip(info.el, {
                        title: tooltipContent,
                        placement: 'top',
                        trigger: 'hover',
                        container: 'body'
                    });
                },
                datesSet: function(info) {
                    // 当日期范围改变时，更新月份输入框，但不触发额外的数据刷新
                    const currentMonth = info.view.currentStart;
                    const year = currentMonth.getFullYear();
                    const month = String(currentMonth.getMonth() + 1).padStart(2, '0');
                    const monthStr = `${year}-${month}`;

                    // 只有当月份真的变化时才更新
                    if (document.getElementById('month').value !== monthStr) {
                        document.getElementById('month').value = monthStr;
                        // 这里不调用refetchEvents，避免重复请求
                    }
                },
                dayCellDidMount: function(info) {
                    info.el.style.transition = 'all 0.3s ease';
                },
                dayCellWillUnmount: function(info) {
                    info.el.classList.remove('profit-day-positive', 'profit-day-negative');
                },
                dateClick: function(info) {
                    const clickedDate = info.dateStr;
                    const strategyId = window.dateToStrategyMap[clickedDate];

                    if (!strategyId) {
                        alert('该日期没有交易记录');
                        return;
                    }

                    // 设置当天的开始时间（00:00:00）和结束时间（23:59:00）
                    const startDateTime = clickedDate + 'T00:00:00';
                    const endDateTime = clickedDate + 'T23:59:00';

                    // 跳转到K线图页面，使用当天的完整时间范围
                    const url = `/?strategy_id=${strategyId}&start_time=${startDateTime}&end_time=${endDateTime}`;
                    window.open(url, '_blank');
                },
                customButtons: {
                    prev: {
                        click: function() {
                            const date = calendar.getDate();
                            date.setMonth(date.getMonth() - 1);
                            calendar.gotoDate(date);
                            // 更新月份选择器并刷新数据
                            const year = date.getFullYear();
                            const month = String(date.getMonth() + 1).padStart(2, '0');
                            const monthStr = `${year}-${month}`;

                            // 只有当月份真的变化时才更新和请求数据
                            if (document.getElementById('month').value !== monthStr) {
                                document.getElementById('month').value = monthStr;
                                // 使用延时确保不会触发多次请求
                                setTimeout(() => {
                                    calendar.refetchEvents();
                                }, 50);
                            }
                        }
                    },
                    next: {
                        click: function() {
                            const date = calendar.getDate();
                            date.setMonth(date.getMonth() + 1);
                            calendar.gotoDate(date);
                            // 更新月份选择器并刷新数据
                            const year = date.getFullYear();
                            const month = String(date.getMonth() + 1).padStart(2, '0');
                            const monthStr = `${year}-${month}`;

                            // 只有当月份真的变化时才更新和请求数据
                            if (document.getElementById('month').value !== monthStr) {
                                document.getElementById('month').value = monthStr;
                                // 使用延时确保不会触发多次请求
                                setTimeout(() => {
                                    calendar.refetchEvents();
                                }, 50);
                            }
                        }
                    }
                }
            });

            calendar.render();

            // 如果没有URL参数指定策略ID，则立即加载当前月份的数据
            if (!urlParams.get('strategy_id')) {
                // 使用延时确保日历完全渲染后再加载数据
                setTimeout(() => {
                    calendar.refetchEvents();
                }, 100);
            }
        });

        // 修改月份选择函数
        function selectMonth(type) {
            const now = new Date();
            let targetDate = new Date();

            switch(type) {
                case 'current':
                    targetDate.setDate(1);
                    break;
                case 'last':
                    targetDate.setMonth(targetDate.getMonth() - 1);
                    targetDate.setDate(1);
                    break;
                case 'previous':
                    targetDate.setMonth(targetDate.getMonth() - 2);
                    targetDate.setDate(1);
                    break;
            }

            const monthStr = targetDate.toISOString().slice(0, 7);
            loadCalendarData(monthStr, currentStrategyId);
        }

        function loadCalendarData(monthStr, strategyId) {
            // 检查是否与当前月份和策略ID相同
            const currentMonth = document.getElementById('month').value;
            const isSameMonth = currentMonth === monthStr;
            const isSameStrategy = (currentStrategyId === strategyId) ||
                                  (currentStrategyId === null && strategyId === null);

            // 如果月份和策略ID都没变，则不重新加载数据
            if (isSameMonth && isSameStrategy) {
                console.log('跳过重复加载，月份和策略ID未变化');
                return;
            }

            document.getElementById('month').value = monthStr;
            currentStrategyId = strategyId;  // 可以是 null
            calendar.gotoDate(monthStr + '-01');

            // 使用延时确保不会触发多次请求
            setTimeout(() => {
                calendar.refetchEvents();
            }, 50);
        }
    </script>
</body>
</html>
