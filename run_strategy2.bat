@echo off


start "Script 0" cmd /c "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/python3.11.exe c:/Users/<USER>/python/okx/strategy_analyzer.py  --start "2025-03-01 00:00:00" --end "2025-05-03 23:59:59"  --currency DOGE  --rest 0 --reverse-buy 0" 

start "Script 1" cmd /c "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/python3.11.exe c:/Users/<USER>/python/okx/strategy_analyzer.py --client-id a1" 
start "Script 1" cmd /c "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/python3.11.exe c:/Users/<USER>/python/okx/strategy_analyzer.py --client-id a2" 
start "Script 1" cmd /c "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/python3.11.exe c:/Users/<USER>/python/okx/strategy_analyzer.py --client-id a3" 