# K线图右键复制功能测试

## 功能说明
在K线图上右键点击可以复制内容到剪贴板：
- **空白区域右键**: 复制 "showDetailModal" 文字
- **K线上右键**: 复制该K线弹窗中的完整格式化内容

## 实现方式

### 1. 事件监听
- 在图表容器上添加 `contextmenu` 事件监听器
- 阻止默认的右键菜单显示

### 2. 复制机制
- **现代浏览器**: 使用 `navigator.clipboard.writeText()` API
- **降级方案**: 使用 `document.execCommand('copy')` 方法

### 3. 用户反馈
- 复制成功时显示绿色提示消息
- 复制失败时显示红色错误消息

## 代码实现

```javascript
// 添加右键菜单功能
chartContainer.addEventListener('contextmenu', function(e) {
    e.preventDefault(); // 阻止默认右键菜单
    
    // 复制 showDetailModal 文字到剪贴板
    const textToCopy = 'showDetailModal';
    
    // 使用现代的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(textToCopy).then(function() {
            console.log('已复制到剪贴板:', textToCopy);
            showToast('已复制 "showDetailModal" 到剪贴板', 'success');
        }).catch(function(err) {
            console.error('复制失败:', err);
            fallbackCopyTextToClipboard(textToCopy);
        });
    } else {
        // 降级方案
        fallbackCopyTextToClipboard(textToCopy);
    }
});
```

## 测试步骤

1. **启动应用**
   ```bash
   python app.py
   ```

2. **打开浏览器**
   - 访问 `http://localhost:5000`
   - 等待K线图加载完成

3. **测试右键复制**
   - 在K线图区域右键点击
   - 观察是否出现成功提示消息
   - 在任意文本编辑器中粘贴 (Ctrl+V)
   - 验证是否粘贴出 "showDetailModal"

## 兼容性

### 支持的浏览器
- **Chrome 66+**: 完全支持 Clipboard API
- **Firefox 63+**: 完全支持 Clipboard API  
- **Safari 13.1+**: 完全支持 Clipboard API
- **Edge 79+**: 完全支持 Clipboard API

### 降级支持
- **旧版浏览器**: 使用 `document.execCommand('copy')` 方法
- **HTTP环境**: 自动降级到传统复制方法

## 安全考虑

1. **HTTPS要求**: Clipboard API 只在安全上下文中工作
2. **用户权限**: 某些浏览器可能需要用户授权
3. **降级处理**: 提供完整的降级方案确保功能可用

## 预期效果

### 空白区域右键
- ✅ 右键点击K线图空白区域
- ✅ 不显示浏览器默认右键菜单
- ✅ 显示绿色成功提示消息："已复制 'showDetailModal' 到剪贴板"
- ✅ "showDetailModal" 文字被复制到剪贴板

### K线上右键
- ✅ 右键点击具体的K线
- ✅ 不显示浏览器默认右键菜单
- ✅ 自动生成该K线的详细弹窗
- ✅ 提取弹窗中的完整文本内容
- ✅ 显示绿色成功提示消息："已复制K线详细信息到剪贴板"
- ✅ 弹窗的完整格式化内容被复制到剪贴板，包含：
  - 详细数据标题和时间
  - 价格数据（开盘价、收盘价、最高价、最低价、成交量、涨跌幅、震幅、账户价值）
  - 交易信息（如果有交易记录）
  - 委托信息（如果有委托记录）

## 故障排除

### 复制失败的可能原因
1. **浏览器不支持**: 使用较新版本的浏览器
2. **非HTTPS环境**: 在本地开发环境中通常不受影响
3. **权限被拒绝**: 检查浏览器的剪贴板权限设置

### 调试方法
1. 打开浏览器开发者工具 (F12)
2. 查看控制台输出
3. 检查是否有错误消息
4. 验证事件是否正确触发
