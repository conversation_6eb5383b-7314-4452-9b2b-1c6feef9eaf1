# ECharts原生API右键复制功能

## 🚨 问题分析
之前使用容器的`contextmenu`事件监听器无法正常工作，因为：
- ECharts内部可能拦截了右键事件
- 图表元素的事件处理优先级更高
- 容器事件可能被阻止冒泡

## ✅ 解决方案
使用ECharts原生的事件API来处理右键事件。

## 🔧 实现方式

### 1. ECharts原生右键事件
```javascript
// 使用ECharts原生API绑定右键事件
chart.on('contextmenu', function(params) {
    console.log('图表右键事件触发:', params);
    
    // params 包含丰富的事件信息：
    // - dataIndex: 数据点索引
    // - seriesIndex: 系列索引
    // - componentType: 组件类型
    // - data: 数据值
    // - name: 数据名称
});
```

### 2. 事件参数详解
ECharts的`contextmenu`事件提供的`params`对象包含：

```javascript
{
    componentType: 'series',    // 组件类型
    seriesType: 'candlestick', // 系列类型（K线图）
    seriesIndex: 0,            // 系列索引
    seriesName: 'K线',         // 系列名称
    name: '2025-06-01T22:03:00', // 数据名称（时间戳）
    dataIndex: 1234,           // 数据索引
    data: [timestamp, open, close, low, high], // 数据值
    value: [timestamp, open, close, low, high], // 数据值（同data）
    color: '#color'            // 颜色
}
```

### 3. 完整实现代码
```javascript
// 绑定ECharts原生右键事件
chart.on('contextmenu', function(params) {
    console.log('图表右键事件触发:', params);
    
    let textToCopy = 'showDetailModal'; // 默认复制内容
    
    // 如果右键点击在数据点上
    if (params && params.dataIndex !== undefined && params.dataIndex >= 0) {
        const timestamp = timestamps[params.dataIndex];
        if (timedata[timestamp]) {
            const dataPoint = timedata[timestamp];
            
            console.log('右键点击K线:', timestamp, dataPoint);
            
            // 临时创建弹窗来获取格式化的内容
            showDetailModal(timestamp, dataPoint);
            
            // 等待弹窗内容生成后获取文本
            setTimeout(() => {
                const detailModal = document.getElementById('detailModal');
                if (detailModal) {
                    // 获取弹窗的纯文本内容
                    const modalText = detailModal.innerText || detailModal.textContent;
                    textToCopy = modalText;
                    
                    // 隐藏弹窗
                    detailModal.style.display = 'none';
                    
                    // 执行复制
                    copyToClipboard(textToCopy, false);
                }
            }, 10);
            
            return; // 提前返回，避免执行下面的默认复制
        }
    }
    
    // 默认复制 showDetailModal
    copyToClipboard(textToCopy, true);
});
```

## 🎯 优势对比

### ❌ 容器事件监听器方式
```javascript
chartContainer.addEventListener('contextmenu', function(e) {
    // 问题：
    // 1. 事件可能被ECharts拦截
    // 2. 需要手动计算坐标转换
    // 3. 无法准确识别点击的数据点
    // 4. 兼容性问题
});
```

### ✅ ECharts原生API方式
```javascript
chart.on('contextmenu', function(params) {
    // 优势：
    // 1. 直接获得数据点信息
    // 2. 无需坐标转换
    // 3. 事件处理更可靠
    // 4. 与ECharts完全兼容
});
```

## 📊 事件触发场景

### 1. 右键点击K线
- **触发**: `chart.on('contextmenu')`
- **params.dataIndex**: 有效的数据索引
- **行为**: 复制该K线的详细弹窗内容

### 2. 右键点击空白区域
- **触发**: `chart.on('contextmenu')`
- **params.dataIndex**: undefined 或 -1
- **行为**: 复制默认的 "showDetailModal" 文字

### 3. 右键点击其他图表元素
- **触发**: `chart.on('contextmenu')`
- **params.componentType**: 'xAxis', 'yAxis', 'legend' 等
- **行为**: 根据组件类型决定复制内容

## 🔍 调试信息

### 控制台输出
```javascript
console.log('图表右键事件触发:', params);
console.log('右键点击K线:', timestamp, dataPoint);
```

### 事件验证
1. 打开浏览器开发者工具 (F12)
2. 在K线图上右键点击
3. 查看控制台输出的事件信息
4. 验证 `params.dataIndex` 是否正确

## 🚀 测试步骤

1. **启动应用**: `python app.py`
2. **打开页面**: `http://localhost:5000`
3. **等待图表加载完成**
4. **右键点击K线**: 应该复制详细信息
5. **右键点击空白**: 应该复制 "showDetailModal"
6. **查看控制台**: 验证事件是否正确触发

## 📋 预期结果

### 成功指标
- ✅ 控制台显示 "图表右键事件触发" 消息
- ✅ 右键点击K线时显示 "已复制K线详细信息到剪贴板"
- ✅ 右键点击空白时显示 "已复制 'showDetailModal' 到剪贴板"
- ✅ 粘贴内容与弹窗内容一致

### 故障排除
- 如果事件不触发：检查ECharts版本兼容性
- 如果数据不正确：验证 `timestamps` 和 `timedata` 数组
- 如果复制失败：检查浏览器剪贴板权限

## 🎉 总结

使用ECharts原生API的`chart.on('contextmenu')`方式比容器事件监听器更可靠：
- **直接访问数据点信息**
- **无需复杂的坐标转换**
- **与ECharts完全兼容**
- **事件处理更稳定**

这种方式确保右键功能在所有支持ECharts的环境中都能正常工作！
