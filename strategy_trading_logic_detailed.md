# 策略分析交易详细逻辑说明

## 🎯 **总体流程概述**

策略分析器的交易逻辑分为以下几个主要阶段：
1. **前置检查** - 基础条件验证
2. **仓位判断** - 根据当前仓位状态选择检查路径
3. **开仓条件** - 做多/做空条件检查
4. **平仓条件** - 平多/平空条件检查
5. **交易执行** - 实际交易操作和记录

## 📋 **详细节点说明**

### **1. 前置检查阶段**

#### **1.1 同一分钟内交易检查**
```python
# 检查逻辑
if self.is_same_minute(row['timestamp'], params.get('last_entry_time'), params.get('last_close_time')):
    return {'action': None, 'reason': '同一分钟内跳过交易'}
```
- **目的**: 防止同一分钟内重复交易
- **判断**: 比较当前时间与上次开仓/平仓时间
- **结果**: 如果在同一分钟内，跳过交易

#### **1.2 连续交易锁定期检查**
```python
# 检查逻辑
if 'lock_time' in params and time.time() < params['lock_time']:
    return {'action': None, 'reason': '连续交易区间暂停'}
```
- **目的**: 防止过于频繁的交易
- **判断**: 检查当前时间是否在锁定期内
- **结果**: 如果在锁定期，跳过交易

#### **1.3 休息时间检查**
```python
# 检查逻辑
time_diff = (current_time - last_close_time).total_seconds() / 60
if time_diff < rest_minutes:
    return {'action': None, 'reason': f'休息时间未到，还需等待{rest_minutes - time_diff:.4f}分钟'}
```
- **目的**: 确保交易间隔符合策略要求
- **判断**: 计算距离上次平仓的时间间隔
- **结果**: 如果休息时间未到，跳过交易

### **2. 仓位状态判断**

#### **2.1 仓位状态分类**
```python
if current_position == 0:    # 无仓位 - 检查开仓条件
elif current_position > 0:   # 多仓 - 检查平多条件  
elif current_position < 0:   # 空仓 - 检查平空条件
```

### **3. 开仓条件检查**

#### **3.1 历史数据充足性检查**
```python
total_needed = lookback_minutes_buy + lookback_minutes_sell
if len(params['minute_candles']) < total_needed:
    return {'action': None, 'reason': '历史数据不足'}
```
- **目的**: 确保有足够的历史数据进行趋势判断
- **判断**: 检查K线数据数量是否满足回看要求
- **结果**: 数据不足时跳过交易

#### **3.2 K线类型判断**
- **完整K线**: 需要检查K线内的上涨和下跌两个阶段
- **实时K线**: 根据当前价格实时判断

#### **3.3 做多条件检查流程**

##### **第一层: 连续上涨次数检查**
```python
consecutive_up_count = 0
for candle in candles[-lookback_minutes_buy:]:
    if candle['is_up']:  # 收盘价 > 开盘价
        consecutive_up_count += 1
    else:
        break

if consecutive_up_count < lookback_minutes_buy:
    return None  # 连续上涨次数不足
```

##### **第二层: 前期下跌期检查**
```python
# 检查上涨期之前是否有足够的下跌期
down_period = candles[-(lookback_minutes_buy + lookback_minutes_sell):-lookback_minutes_buy]
consecutive_down_count = 0
for candle in reversed(down_period):
    if not candle['is_up']:  # 收盘价 <= 开盘价
        consecutive_down_count += 1
    else:
        break

if consecutive_down_count < lookback_minutes_sell:
    return None  # 前期下跌次数不足
```

##### **第三层: 触发价格计算**
```python
# 找到下跌期的最低K线
last_down_candle = min(down_period, key=lambda x: x['low'])

# 计算触发价格
if buy_rate == 1.0:
    trigger_price = last_down_candle['high']  # 突破最低K线的最高价
else:
    price_range = last_down_candle['high'] - last_down_candle['low']
    trigger_price = last_down_candle['low'] + (price_range * buy_rate)
```

##### **第四层: 价格触发检查**
```python
if current_high >= trigger_price:
    # 检查价格保护
    if trigger_price < current_open or trigger_price > current_high:
        # 触发价格超出K线范围，使用开盘价保护
        actual_trigger_price = current_open
        reason = "价格保护触发"
    else:
        actual_trigger_price = trigger_price
        reason = "正常触发"
    
    return {'action': '开多', 'trigger_price': actual_trigger_price}
```

#### **3.4 做空条件检查流程**

##### **逻辑与做多相反**
1. **连续下跌次数** ≥ lookback_minutes_buy
2. **前期上涨次数** ≥ lookback_minutes_sell  
3. **找到上涨期最高K线**
4. **计算触发价格**: 最高价 - 价格范围 × buy_rate
5. **价格触发检查**: 当前价格 ≤ 触发价格

### **4. 平仓条件检查**

#### **4.1 平多条件检查**

##### **强制平仓检查**
```python
entry_time_diff = (current_time - last_entry_time).total_seconds() / 60
min_trigger_price = last_position_price * (1 - min_trigger_rate)

if entry_time_diff > min_trigger_rest and current_close <= min_trigger_price:
    return {'action': '平多', 'reason': '持仓时间触发'}
```

##### **趋势平仓检查**
```python
# 检查连续下跌次数
consecutive_down_count = 0
for candle in reversed(candles[-lookback_minutes_sell:]):
    if not candle['is_up']:
        consecutive_down_count += 1
    else:
        break

if consecutive_down_count >= lookback_minutes_sell:
    # 找到最近的上涨K线
    last_up_candle = None
    for candle in reversed(candles):
        if candle['is_up']:
            last_up_candle = candle
            break
    
    # 计算触发价格
    if sell_rate == 1.0:
        trigger_price = last_up_candle['low']
    else:
        price_range = last_up_candle['high'] - last_up_candle['low']
        trigger_price = last_up_candle['high'] - (price_range * sell_rate)
    
    if current_low <= trigger_price:
        return {'action': '平多', 'trigger_price': trigger_price}
```

#### **4.2 平空条件检查**
- **逻辑与平多相反**
- **强制平仓**: 持仓时间 > min_trigger_rest 且价格 ≥ min_trigger_price
- **趋势平仓**: 连续上涨 ≥ lookback_minutes_sell 且价格突破触发点

### **5. 价格保护机制**

#### **5.1 保护触发条件**
```python
# 检查触发价格是否在当前K线范围内
if trigger_price < current_open or trigger_price > current_high:  # 做多
if trigger_price > current_open or trigger_price < current_low:   # 做空
```

#### **5.2 保护措施**
- **使用开盘价**: 当触发价格超出K线范围时
- **记录保护标识**: 在交易原因中标注"(保护)"
- **确保可执行性**: 避免不可能的触发价格

### **6. 交易执行阶段**

#### **6.1 交易参数计算**
```python
# 计算交易量
if action in ['开多', '开空']:
    amount = available_capital / entry_price
elif action in ['平多', '平空']:
    amount = current_position

# 计算手续费
fee = amount * entry_price * fee_rate
```

#### **6.2 状态更新**
1. **更新仓位**: current_position
2. **更新资金**: available_capital
3. **更新价格**: last_position_price
4. **更新时间**: last_entry_time / last_close_time
5. **记录交易**: 保存到数据库

## 🎯 **关键参数说明**

### **时间参数**
- `lookback_minutes_buy`: 开仓时连续趋势要求 (默认2分钟)
- `lookback_minutes_sell`: 平仓时连续趋势要求 (默认2分钟)  
- `rest_minutes`: 交易间隔休息时间 (默认0分钟)
- `min_trigger_rest`: 强制平仓最小持仓时间 (默认0分钟)

### **价格参数**
- `buy_rate`: 开仓触发价格倍率 (默认0.02 = 2%)
- `sell_rate`: 平仓触发价格倍率 (默认0.02 = 2%)
- `min_trigger_rate`: 强制平仓价格倍率 (默认0.002 = 0.2%)

### **状态参数**
- `current_position`: 当前仓位 (>0多仓, <0空仓, =0无仓)
- `available_capital`: 可用资金
- `last_position_price`: 上次开仓价格
- `is_complete`: K线类型 (1=完整, 0=实时)

## 🔄 **循环逻辑**

每个K线数据都会经过完整的判断流程：
1. **数据预处理** → 2. **前置检查** → 3. **仓位判断** → 4. **条件检查** → 5. **交易执行** → 6. **状态更新**

这个流程确保了策略的完整性和一致性，每个决策点都有明确的判断逻辑和处理方式。
