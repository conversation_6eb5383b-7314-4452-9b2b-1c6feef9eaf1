#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试新的交易条件判断逻辑
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def debug_new_logic():
    """调试新的交易条件判断逻辑"""
    
    print("=== 调试新的交易条件判断逻辑 ===")
    print()
    
    # 创建策略分析器实例
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 获取一些真实数据进行分析
    try:
        with analyzer.get_db_connection() as conn:
            cursor = conn.cursor()
            # 先查看表结构
            cursor.execute("DESCRIBE crypto_prices")
            columns = cursor.fetchall()
            print("数据库表结构:")
            for col in columns:
                print(f"  {col}")
            print()

            cursor.execute("""
                SELECT timestamp, open_price, close_price, high_price, low_price
                FROM crypto_prices
                WHERE currency = 'DOGE'
                AND timestamp >= '2025-06-02 00:00:00'
                AND timestamp <= '2025-06-02 01:00:00'
                ORDER BY timestamp
                LIMIT 20
            """)
            results = cursor.fetchall()
            
            if not results:
                print("没有找到数据")
                return
                
            print(f"找到 {len(results)} 条数据")
            print()
            
            # 模拟分钟K线数据处理
            minute_candles = []
            
            for i, row in enumerate(results):
                print(f"=== 第 {i+1} 条数据 ===")
                print(f"时间: {row['timestamp']}")
                print(f"开盘: {row['open_price']}, 收盘: {row['close_price']}, 最高: {row['high_price']}, 最低: {row['low_price']}")

                # 计算涨跌区间
                open_price = float(row['open_price'])
                close_price = float(row['close_price'])
                high_price = float(row['high_price'])
                low_price = float(row['low_price'])
                
                up_range = high_price - open_price      # 上涨区间
                down_range = open_price - low_price     # 下跌区间
                is_up_final = close_price > open_price  # 最终涨跌
                
                print(f"上涨区间: {up_range:.6f} (开盘到最高)")
                print(f"下跌区间: {down_range:.6f} (开盘到最低)")
                print(f"最终涨跌: {'涨' if is_up_final else '跌'}")
                print(f"主要趋势: {'上涨' if up_range > down_range else '下跌'}")
                
                # 添加到历史数据
                candle_data = {
                    'timestamp': row['timestamp'],
                    'open': open_price,
                    'close': close_price,
                    'high': high_price,
                    'low': low_price,
                    'is_up': is_up_final,
                    'up_range': up_range,
                    'down_range': down_range
                }
                minute_candles.append(candle_data)
                
                # 如果有足够的历史数据，进行连续涨跌分析
                if len(minute_candles) >= 5:
                    print(f"\n--- 连续涨跌分析 (lookback=5) ---")
                    recent_5 = minute_candles[-5:]
                    
                    # 检查连续下跌（为做多准备）
                    all_down = all(candle['down_range'] > candle['up_range'] for candle in recent_5)
                    # 检查连续上涨（为做空准备）
                    all_up = all(candle['up_range'] > candle['down_range'] for candle in recent_5)
                    
                    print(f"最近5分钟趋势:")
                    for j, candle in enumerate(recent_5):
                        trend = "上涨" if candle['up_range'] > candle['down_range'] else "下跌"
                        print(f"  {j+1}. {candle['timestamp']} - {trend} (上涨区间:{candle['up_range']:.6f}, 下跌区间:{candle['down_range']:.6f})")
                    
                    print(f"连续下跌: {all_down}")
                    print(f"连续上涨: {all_up}")
                    
                    if all_down:
                        print("✅ 满足做多准备条件（连续5分钟下跌趋势）")
                        # 找到最低点
                        lowest_candle = min(recent_5, key=lambda x: x['low'])
                        print(f"最低点: {lowest_candle['timestamp']}, 最低价: {lowest_candle['low']}, 最高价: {lowest_candle['high']}")
                        
                        # 计算触发价格 (buy_rate=1.0)
                        trigger_price = lowest_candle['high']
                        print(f"做多触发价格: {trigger_price}")
                        
                        # 检查当前是否可以触发
                        current_up_range = up_range
                        current_down_range = down_range
                        current_high = high_price
                        
                        print(f"当前上涨趋势: {current_up_range > current_down_range}")
                        print(f"当前最高价: {current_high}, 触发价格: {trigger_price}")
                        print(f"价格达到: {current_high >= trigger_price}")
                        
                        if current_up_range > current_down_range and current_high >= trigger_price:
                            print("🚀 可以触发做多！")
                        else:
                            print("❌ 不能触发做多")
                    
                    elif all_up:
                        print("✅ 满足做空准备条件（连续5分钟上涨趋势）")
                        # 找到最高点
                        highest_candle = max(recent_5, key=lambda x: x['high'])
                        print(f"最高点: {highest_candle['timestamp']}, 最高价: {highest_candle['high']}, 最低价: {highest_candle['low']}")
                        
                        # 计算触发价格 (buy_rate=1.0)
                        trigger_price = highest_candle['low']
                        print(f"做空触发价格: {trigger_price}")
                        
                        # 检查当前是否可以触发
                        current_up_range = up_range
                        current_down_range = down_range
                        current_low = low_price
                        
                        print(f"当前下跌趋势: {current_down_range > current_up_range}")
                        print(f"当前最低价: {current_low}, 触发价格: {trigger_price}")
                        print(f"价格达到: {current_low <= trigger_price}")
                        
                        if current_down_range > current_up_range and current_low <= trigger_price:
                            print("🚀 可以触发做空！")
                        else:
                            print("❌ 不能触发做空")
                    else:
                        print("❌ 不满足连续涨跌条件")
                
                print()
                
    except Exception as e:
        print(f"调试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_new_logic()
