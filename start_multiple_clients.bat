@echo off
echo 启动多个客户端处理模式...
set PYTHONUNBUFFERED=1

echo 启动客户端 a1...
start cmd /k "title 客户端 a1 && python strategy_analyzer.py --client-id a1 --sleep-time 5"

echo 启动客户端 a2...
start cmd /k "title 客户端 a2 && python strategy_analyzer.py --client-id a2 --sleep-time 5"

echo 启动客户端 a3...
start cmd /k "title 客户端 a3 && python strategy_analyzer.py --client-id a3 --sleep-time 5"

echo 所有客户端已启动！
echo 您可以在各自的命令窗口中查看它们的运行状态。
echo 要停止客户端，请在相应的命令窗口中按Ctrl+C。
pause
