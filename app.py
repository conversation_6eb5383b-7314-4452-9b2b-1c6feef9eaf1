from flask import Flask, render_template, jsonify, request, Response
import pymysql
from datetime import datetime, timedelta
import logging
import decimal
from config import *
from dateutil.parser import parse
# from functools import lru_cache  # 已移除以减少CPU占用
import pandas as pd
import time
import threading
import os
import traceback
from okx_api_handler import okx_api

# 数据库表名配置
DB_TABLES = {
    'PRICES': 'crypto_prices',
    'STRATEGY_RESULTS': 'strategy_results',
    'STRATEGY_TRADES': 'strategy_trades',
    'POSITION_HISTORY': 'position_history'
}

app = Flask(__name__)
app.config['DEBUG'] = False  # 禁用调试模式以减少CPU占用
app.config['TEMPLATES_AUTO_RELOAD'] = False  # 禁用模板自动重载
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0  # 禁用静态文件缓存

logging.basicConfig(level=logging.WARNING)  # 降低日志级别以减少CPU占用

# 监控的文件和它们的最后修改时间
monitored_files = {}

def get_monitored_files():
    """获取需要监控的文件列表"""
    base_dir = os.path.dirname(os.path.abspath(__file__))
    files = [
        os.path.join(base_dir, 'templates', 'index.html'),
        os.path.join(base_dir, 'static') if os.path.exists(os.path.join(base_dir, 'static')) else None
    ]
    return [f for f in files if f and os.path.exists(f)]

def get_file_mtime(file_path):
    """获取文件或目录的最新修改时间"""
    if os.path.isfile(file_path):
        return os.path.getmtime(file_path)
    elif os.path.isdir(file_path):
        latest = os.path.getmtime(file_path)
        for root, dirs, files in os.walk(file_path):
            for f in files:
                mtime = os.path.getmtime(os.path.join(root, f))
                if mtime > latest:
                    latest = mtime
        return latest
    return 0

def check_for_changes():
    """检查文件是否有更改 - 已禁用以减少CPU占用"""
    logging.info("文件监控功能已禁用")
    return

# 存储活跃的SSE客户端
active_clients = set()

@app.route('/changes')
def get_changes():
    """文件更改监听已禁用"""
    return Response('data: disabled\n\n', mimetype='text/event-stream')

# 文件监控已完全禁用以减少CPU占用
logging.info("文件监控已完全禁用以减少CPU占用")

# 支持的时间粒度
INTERVALS = {
    '1m': 1,
    '5m': 5,
    '15m': 15,
    '1h': 60,
    '4h': 240,
    '1d': 1440
}

def get_db_connection():
    """获取数据库连接"""
    try:
        return pymysql.connect(
            host=MYSQL_HOST,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=MYSQL_DATABASE,
            port=MYSQL_PORT,
            charset='utf8mb4',
            connect_timeout=10,  # 10秒连接超时
            read_timeout=30,     # 30秒读取超时
            write_timeout=30,    # 30秒写入超时
            autocommit=True,     # 自动提交
            cursorclass=pymysql.cursors.DictCursor  # 使用字典游标
        )
    except Exception as e:
        logging.error(f"数据库连接失败: {str(e)}")
        raise

@app.route('/')
def index():
    return render_template('index.html')

def get_suitable_interval(days):
    """
    根据时间跨度自动选择合适的时间粒度
    """
    if days <= 7:
        return '1m'
    elif days <= 15:
        return '5m'
    elif days <= 30:
        return '15m'
    elif days <= 60:
        return '1h'
    elif days <= 90:
        return '4h'
    else:
        return '1d'

def get_kline_data_from_db(start_time_str, end_time_str, interval_minutes, currency='DOGE'):
    conn = None
    cursor = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        sql = f"""
        WITH base_intervals AS (
            SELECT
                FROM_UNIXTIME(FLOOR(UNIX_TIMESTAMP(timestamp) / (%s * 60)) * (%s * 60)) as interval_start,
                MIN(timestamp) as first_time,
                MAX(timestamp) as last_time,
                MIN(low_price) as low_price,
                MAX(high_price) as high_price,
                SUM(volume) as volume
            FROM {DB_TABLES['PRICES']}
            WHERE currency = %s
            AND timestamp BETWEEN %s AND %s
            GROUP BY interval_start
        )
        SELECT
            DATE_FORMAT(bi.interval_start, '%%Y-%%m-%%d %%H:%%i:%%s') as interval_timestamp,
            COALESCE(
                (SELECT cp1.open_price
                FROM {DB_TABLES['PRICES']} cp1
                WHERE cp1.timestamp = bi.first_time AND cp1.currency = %s),
                (SELECT cp2.close_price
                FROM {DB_TABLES['PRICES']} cp2
                WHERE cp2.timestamp < bi.interval_start AND cp2.currency = %s
                ORDER BY cp2.timestamp DESC LIMIT 1)
            ) as open_price,
            COALESCE(
                (SELECT cp3.close_price
                FROM {DB_TABLES['PRICES']} cp3
                WHERE cp3.timestamp = bi.last_time AND cp3.currency = %s),
                (SELECT cp4.close_price
                FROM {DB_TABLES['PRICES']} cp4
                WHERE cp4.timestamp < bi.interval_start AND cp4.currency = %s
                ORDER BY cp4.timestamp DESC LIMIT 1)
            ) as close_price,
            bi.high_price,
            bi.low_price,
            bi.volume,
            %s as currency
        FROM base_intervals bi
        ORDER BY bi.interval_start ASC;
        """

        # 记录查询参数（仅在调试时）
        # logging.info(f"Executing query with params: interval_minutes={interval_minutes}, start_time={start_time_str}, end_time={end_time_str}, currency={currency}")

        cursor.execute(sql, (
            interval_minutes, interval_minutes,           # 时间间隔计算
            currency,                                    # WHERE currency = %s
            start_time_str, end_time_str,               # 时间范围
            currency,                                    # 第一个 COALESCE 中的 currency
            currency,                                    # 第二个 COALESCE 中的 currency
            currency,                                    # 第三个 COALESCE 中的 currency
            currency,                                    # 第四个 COALESCE 中的 currency
            currency                                     # SELECT %s as currency
        ))

        result = cursor.fetchall()
        if not result:
            logging.warning(f"No data found for currency {currency} in the given time range: {start_time_str} to {end_time_str}")
            return []

        # 验证数据格式
        for row in result:
            if not isinstance(row, dict):
                logging.error(f"Invalid row format: {row}")
                continue
            if not all(k in row for k in ['interval_timestamp', 'open_price', 'close_price', 'high_price', 'low_price', 'volume']):
                logging.error(f"Missing required fields in row: {row}")
                continue

        return result

    except pymysql.Error as e:
        logging.error(f"Database error: {e}")
        raise
    except Exception as e:
        logging.error(f"Unexpected error in get_kline_data_from_db: {e}")
        raise
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/api/kline')
def get_kline_data():
    cursor = None
    conn = None
    try:
        # 获取并验证参数
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        interval = request.args.get('interval', '1m')  # 允许为None
        strategy_id = request.args.get('strategy_id')
        currency = request.args.get('currency', 'DOGE')  # 添加币种参数，默认为DOGE

        # 如果提供了策略ID，使用策略的时间范围和币种
        if strategy_id:
            try:
                conn = get_db_connection()
                cursor = conn.cursor(pymysql.cursors.DictCursor)
                cursor.execute(f"""
                    SELECT start_time, end_time, currency, initial_capital FROM {DB_TABLES['STRATEGY_RESULTS']} WHERE id = %s
                """, (strategy_id,))
                strategy_info = cursor.fetchone()
                if strategy_info:
                    logging.info(f"Strategy info from database - start: {strategy_info['start_time']}, end: {strategy_info['end_time']}, currency: {strategy_info['currency']}")
                    if not start_time:
                        start_time = strategy_info['start_time'].strftime('%Y-%m-%d %H:%M:%S')
                    if not end_time:
                        end_time = strategy_info['end_time'].strftime('%Y-%m-%d %H:%M:%S')
                    currency = strategy_info['currency']  # 使用策略的币种
                else:
                    logging.error(f"No strategy found with ID: {strategy_id}")
                    return jsonify({
                        'code': 1,
                        'msg': f'未找到ID为 {strategy_id} 的策略'
                    })
            except Exception as e:
                logging.error(f"Error fetching strategy info: {e}")
                return jsonify({
                    'code': 1,
                    'msg': f'获取策略信息时出错: {str(e)}'
                })
            finally:
                if cursor:
                    cursor.close()
                if conn:
                    conn.close()

        elif not start_time or not end_time:
            # 如果没有提供时间范围，使用最近24小时
            end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            start_time = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')

        try:
            # 验证时间范围
            start_dt = parse(start_time)
            end_dt = parse(end_time)
        except Exception as e:
            logging.error(f"Error parsing dates: {e}")
            return jsonify({
                'code': 1,
                'msg': f'日期格式错误: {str(e)}'
            })

        days_diff = (end_dt - start_dt).days + (end_dt - start_dt).seconds / 86400

        # 如果没有指定interval或时间范围超过限制，自动选择合适的时间粒度
        if interval not in INTERVALS:
            interval = get_suitable_interval(days_diff)

        # 获取K线数据
        interval_minutes = INTERVALS[interval]
        try:
            data = get_kline_data_from_db(
                start_dt.strftime('%Y-%m-%d %H:%M:%S'),
                end_dt.strftime('%Y-%m-%d %H:%M:%S'),
                interval_minutes,
                currency  # 传入币种参数
            )
        except Exception as e:
            logging.error(f"Error fetching kline data: {e}")
            return jsonify({
                'code': 1,
                'msg': f'获取K线数据时出错: {str(e)}'
            })

        if not data:
            return jsonify({
                'code': 1,
                'msg': f'没有找到{currency}在指定时间范围内的数据'
            })

        # 获取交易数据（如果提供了策略ID）
        trades = []
        if strategy_id:
            try:
                conn = get_db_connection()
                cursor = conn.cursor(pymysql.cursors.DictCursor)
                cursor.execute(f"""
                    SELECT
                        st.id,
                        DATE_FORMAT(st.timestamp, '%%Y-%%m-%%d %%H:%%i:%%s') as timestamp,
                        st.action,
                        st.price,
                        st.amount,
                        st.fee,
                        st.profit_amount,
                        st.profit_percentage,
                        st.trigger_reason,
                        st.market_price,
                        st.account_value,
                        st.rest_minutes,
                        st.entry_price,
                        st.position_size,
                        st.position_type,
                        st.last_low,
                        st.last_high,
                        st.min_trigger_price,
                        st.lookback_minutes_buy,
                        st.lookback_minutes_sell
                    FROM {DB_TABLES['STRATEGY_TRADES']} st
                    WHERE st.strategy_id = %s
                    AND st.timestamp BETWEEN %s AND %s
                    ORDER BY st.timestamp
                """, (strategy_id, start_time, end_time))
                trades = cursor.fetchall() or []
                logging.info(f"Fetched {len(trades)} trades")

            except Exception as e:
                logging.error(f"Error fetching trades: {e}")
                return jsonify({
                    'code': 1,
                    'msg': f'获取交易数据时出错: {str(e)}'
                })
            finally:
                if cursor:
                    cursor.close()
                if conn:
                    conn.close()

        # 重构数据结构，以时间戳为索引创建数据点
        timestamps = []
        timedata = {}

        # 计算整体最高最低价
        all_highs = []
        all_lows = []

        for row in data:
            if not isinstance(row, dict) or row.get('interval_timestamp') is None:
                continue

            timestamp = row['interval_timestamp']

            try:
                # 确保所有价格数据都有默认值
                open_price = float(row.get('open_price', 0) or 0)
                close_price = float(row.get('close_price', 0) or 0)
                low_price = float(row.get('low_price', 0) or 0)
                high_price = float(row.get('high_price', 0) or 0)
                volume = float(row.get('volume', 0) or 0)

                if high_price > 0:
                    all_highs.append(high_price)
                if low_price > 0:
                    all_lows.append(low_price)

                # 计算涨跌幅，添加安全检查
                price_change = ((close_price - open_price) / open_price * 100) if open_price != 0 else 0
                # 计算震幅，添加安全检查
                price_range = ((high_price - low_price) / open_price * 100) if open_price != 0 else 0

                # 添加时间戳到有序列表
                timestamps.append(timestamp)

                # 为每个时间戳创建数据点
                timedata[timestamp] = {
                    'kline': [open_price, close_price, low_price, high_price],
                    'volume': volume,
                    'price_change': round(price_change, 2),
                    'price_range': round(price_range, 2),
                    'trade': None,  # 为交易数据预留位置
                    'account_value': None  # 为账户价值预留位置
                }
            except (ValueError, TypeError) as e:
                logging.error(f"Error processing row data for timestamp {timestamp}: {e}")
                continue

        # 计算全局最高最低价
        global_high = max(all_highs) if all_highs else 0
        global_low = min(all_lows) if all_lows else 0

        # 计算账户价值
        initial_value = 1000  # 默认初始值
        if strategy_id and strategy_info and 'initial_capital' in strategy_info:
            initial_value = float(strategy_info['initial_capital'] or 1000)

        # 初始状态
        last_long_trade = None
        last_short_trade = None
        last_account_value = initial_value

        # 将交易数据按时间戳整合到timedata中
        for trade in trades:
            trade_timestamp = trade.get('timestamp')
            # 确保交易数据是正确的字典格式
            if isinstance(trade, dict) and trade_timestamp:
                # 处理交易时间戳，忽略秒数部分
                # 时间格式应该是: YYYY-MM-DD HH:MM:SS，我们去除秒数
                if len(trade_timestamp) >= 19:  # 确保时间戳格式完整
                    # 将秒数部分替换为:00，忽略秒数
                    trade_timestamp_no_seconds = trade_timestamp[:17] + '00'
                else:
                    # 如果格式不完整，保持原样
                    trade_timestamp_no_seconds = trade_timestamp

                # 直接集成到timedata中
                trade_data = {
                    'id': trade.get('id'),
                    'timestamp': trade_timestamp,  # 保持原始时间戳
                    'timestamp_no_seconds': trade_timestamp_no_seconds,  # 添加不带秒数的时间戳
                    'action': trade.get('action'),
                    'price': float(trade.get('price', 0) or 0),
                    'amount': float(trade.get('amount', 0) or 0),
                    'fee': float(trade.get('fee', 0) or 0),
                    'profit_amount': float(trade.get('profit_amount', 0) or 0),
                    'profit_percentage': float(trade.get('profit_percentage', 0) or 0),
                    'trigger_reason': trade.get('trigger_reason'),
                    'market_price': float(trade.get('market_price', 0) or 0),
                    'account_value': float(trade.get('account_value', 0) or 0),
                    'rest_minutes': int(trade.get('rest_minutes', 0) or 0),
                    'entry_price': float(trade.get('entry_price', 0) or 0),
                    'position_size': float(trade.get('position_size', 0) or 0),
                    'position_type': trade.get('position_type'),
                    'last_low': float(trade.get('last_low', 0) or 0),
                    'last_high': float(trade.get('last_high', 0) or 0),
                    'min_trigger_price': float(trade.get('min_trigger_price', 0) or 0)
                }

                # 找到对应的时间数据点（忽略秒数）
                if trade_timestamp_no_seconds in timedata:
                    timedata[trade_timestamp_no_seconds]['trade'] = trade_data

        # 排序timedata的键，按时间顺序处理
        timestamps = sorted(timedata.keys())

        # 按时间顺序处理计算账户价值
        for timestamp in timestamps:
            point = timedata[timestamp]
            current_price = point['kline'][1]  # 收盘价

            # 更新交易状态
            trade = point['trade']
            if trade:
                # 使用交易中记录的账户价值
                if trade.get('account_value'):
                    last_account_value = trade['account_value']

                # 更新持仓状态
                if trade['position_type'] == 'long':
                    last_long_trade = trade
                    last_short_trade = None  # 清除可能的做空仓位
                elif trade['position_type'] == 'close_long':
                    last_long_trade = None
                elif trade['position_type'] == 'short':
                    last_short_trade = trade
                    last_long_trade = None  # 清除可能的做多仓位
                elif trade['position_type'] == 'close_short':
                    last_short_trade = None

            # 根据持仓状态计算当前账户价值
            current_value = last_account_value

            # 如果持有多头仓位，计算价值变化
            if last_long_trade and last_long_trade.get('entry_price') and last_long_trade.get('position_size'):
                entry_price = float(last_long_trade['entry_price'])
                position_size = float(last_long_trade['position_size'])
                if entry_price > 0 and position_size > 0:
                    current_value = last_account_value + (current_price - entry_price) * position_size

            # 如果持有空头仓位，计算价值变化
            if last_short_trade and last_short_trade.get('entry_price') and last_short_trade.get('position_size'):
                entry_price = float(last_short_trade['entry_price'])
                position_size = float(last_short_trade['position_size'])
                if entry_price > 0 and position_size > 0:
                    current_value = last_account_value + (entry_price - current_price) * position_size

            # 保存账户价值
            point['account_value'] = current_value

        # 检查是否有数据
        if not timestamps:
            return jsonify({
                'code': 1,
                'msg': '没有找到符合条件的数据'
            })

        # 构建优化后的响应
        response_data = {
            'code': 0,
            'data': {
                'timedata': timedata,
                'global_high': global_high,
                'global_low': global_low,
                'initial_capital': initial_value
            },
            'metadata': {
                'interval': interval,
                'start_time': start_dt.strftime('%Y-%m-%d %H:%M:%S'),
                'end_time': end_dt.strftime('%Y-%m-%d %H:%M:%S'),
                'total_points': len(timedata),
                'days': days_diff
            }
        }

        return jsonify(response_data)

    except Exception as e:
        logging.error(f"Error in get_kline_data: {str(e)}", exc_info=True)
        return jsonify({
            'code': 1,
            'msg': str(e)
        })

@app.route('/get_strategies')
def get_strategy_params():
    conn = None
    try:
        conn = get_db_connection()
        with conn.cursor(pymysql.cursors.DictCursor) as cursor:
            cursor.execute(f"""
                SELECT
                    sr.id as strategy_id,
                    sr.start_time,
                    sr.end_time,
                    sr.total_trades,
                    ROUND(sr.successful_trades * 100.0 / sr.total_trades, 2) as win_rate,
                    ROUND((sr.final_capital - sr.initial_capital) * 100.0 / sr.initial_capital, 2) as profit_percentage,
                    ROUND(sr.buy_rate * 100, 2) as buy_rate,
                    ROUND(sr.sell_rate * 100, 2) as sell_rate,
                    sr.lookback_minutes_buy,
                    sr.lookback_minutes_sell,
                    sr.rest_minutes
                FROM {DB_TABLES['STRATEGY_RESULTS']} sr
                WHERE sr.total_trades > 0
                ORDER BY id DESC
                LIMIT 1000
            """)
            strategies = cursor.fetchall()
            return jsonify(strategies)
    except Exception as e:
        logging.error(f"Error in get_strategies: {str(e)}", exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

@app.route('/api/strategy_params', methods=['GET'])
def get_api_strategies():
    """获取所有可用的策略参数组合"""
    try:
        strategies = []
        # 策略参数范围：减少组合数量以降低CPU占用
        for buy_rate in range(1, 6):  # 0.01-0.05，减少到5个值
            for sell_rate in range(1, 6):  # 0.01-0.05，减少到5个值
                for lookback_minutes_buy in range(2, 5):  # 2-4，减少到3个值
                    for lookback_minutes_sell in range(2, 5):  # 2-4，减少到3个值
                        strategies.append({
                            'buy_rate': buy_rate / 100,
                            'sell_rate': sell_rate / 100,
                            'lookback_minutes_buy': lookback_minutes_buy,
                            'lookback_minutes_sell': lookback_minutes_sell
                        })
        return jsonify(strategies)
    except Exception as e:
        return jsonify({
            'success': False,
            'msg': str(e)
        })

@app.route('/analyze_strategy', methods=['POST'])
def analyze_strategy():
    """分析特定策略的表现"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # 获取策略参数
        buy_rate = data.get('buy_rate')
        sell_rate = data.get('sell_rate')
        lookback_minutes_buy = data.get('lookback_minutes_buy')
        lookback_minutes_sell = data.get('lookback_minutes_sell')

        # 验证所有必需的参数是否存在
        required_params = {
            'buy_rate': buy_rate,
            'sell_rate': sell_rate,
            'lookback_minutes_buy': lookback_minutes_buy,
            'lookback_minutes_sell': lookback_minutes_sell
        }

        missing_params = [k for k, v in required_params.items() if v is None]
        if missing_params:
            return jsonify({'error': f'Missing parameters: {", ".join(missing_params)}'}), 400

        # 验证参数范围
        if not (0.01 <= buy_rate <= 0.10):
            return jsonify({'error': f'buy_rate out of valid range (0.01-0.10)'}), 400
        if not (0.01 <= sell_rate <= 0.10):
            return jsonify({'error': f'sell_rate out of valid range (0.01-0.10)'}), 400
        if not (1 <= lookback_minutes_buy <= 10):
            return jsonify({'error': f'lookback_minutes_buy out of valid range (1-10)'}), 400
        if not (1 <= lookback_minutes_sell <= 10):
            return jsonify({'error': f'lookback_minutes_sell out of valid range (1-10)'}), 400

        # 从数据库获取价格数据
        with get_db_connection() as conn:
            with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT timestamp, open_price, high_price, low_price, close_price, volume
                    FROM crypto_prices
                    WHERE currency = 'DOGE'
                    ORDER BY timestamp
                """)
                rows = cursor.fetchall()

        if not rows:
            return jsonify({'error': 'No price data available'}), 404

        # 转换为DataFrame
        df = pd.DataFrame(rows, columns=['timestamp', 'open_price', 'high_price', 'low_price', 'close_price', 'volume'])

        # 创建策略分析器并执行分析
        analyzer = StrategyAnalyzer()
        params = {
            'buy_rate': buy_rate,
            'sell_rate': sell_rate,
            'lookback_minutes_buy': lookback_minutes_buy,
            'lookback_minutes_sell': lookback_minutes_sell
        }
        result = analyzer.analyze_strategy_with_params(df, params)

        if result is None:
            return jsonify({'error': 'Strategy analysis failed'}), 500

        return jsonify(result)

    except Exception as e:
        app.logger.error(f'Error analyzing strategy: {str(e)}')
        return jsonify({'error': str(e)}), 500

@app.route('/api/strategies')
def get_strategies():
    try:
        conn = get_db_connection()
        with conn.cursor(pymysql.cursors.DictCursor) as cursor:
            cursor.execute(f"""
                SELECT
                    sr.id as strategy_id,
                    sr.start_time,
                    sr.end_time,
                    sr.total_trades,
                    ROUND(sr.successful_trades * 100.0 / sr.total_trades, 2) as win_rate,
                    ROUND((sr.final_capital - sr.initial_capital) * 100.0 / sr.initial_capital, 2) as profit_percentage,
                    ROUND(sr.buy_rate * 100, 2) as buy_rate,
                    ROUND(sr.sell_rate * 100, 2) as sell_rate,
                    sr.rest_minutes,
                    sr.lookback_minutes_buy,
                    sr.lookback_minutes_sell
                FROM {DB_TABLES['STRATEGY_RESULTS']} sr
                WHERE sr.total_trades > 0
                ORDER BY sr.id DESC
                LIMIT 100
            """)

            strategies = cursor.fetchall()

            # Convert decimal values to float for JSON serialization
            for strategy in strategies:
                for key, value in strategy.items():
                    if isinstance(value, decimal.Decimal):
                        strategy[key] = float(value)
                    elif value is None:
                        strategy[key] = 0

            return jsonify(strategies)
    except decimal.InvalidOperation as e:
        logging.error(f"Decimal conversion error in get_strategies: {str(e)}")
        return jsonify({"error": "数据转换错误"}), 500
    except Exception as e:
        logging.error(f"Error fetching strategies: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()

@app.route('/api/strategy_chart/<int:strategy_id>', methods=['GET'])
def get_strategy_chart(strategy_id):
    try:
        conn = get_db_connection()
        with conn.cursor(pymysql.cursors.DictCursor) as cursor:
            # 首先获取策略的基本信息
            cursor.execute(f"""
                SELECT
                    id,
                    buy_rate,
                    sell_rate,
                    initial_capital,
                    final_capital,
                    total_trades,
                    successful_trades,
                    total_profit,
                    total_fees,
                    start_time,
                    end_time,
                    rest_minutes,
                    min_trigger_rest,
                    lookback_minutes_buy,
                    lookback_minutes_sell,
                    currency
                FROM {DB_TABLES['STRATEGY_RESULTS']}
                WHERE id = %s
            """, (strategy_id,))

            strategy_info = cursor.fetchone()
            if not strategy_info:
                return jsonify({"error": "策略不存在"}), 404

            # 获取策略的币种
            currency = strategy_info['currency'] or 'DOGE'

            # 获取K线数据
            cursor.execute("""
                SELECT
                    DATE_FORMAT(timestamp, '%%Y-%%m-%%d %%H:%%i:%%s') as timestamp,
                    ROUND(COALESCE(open_price, 0), 6) as open_price,
                    ROUND(COALESCE(high_price, 0), 6) as high_price,
                    ROUND(COALESCE(low_price, 0), 6) as low_price,
                    ROUND(COALESCE(close_price, 0), 6) as close_price,
                    ROUND(COALESCE(volume, 0), 2) as volume,
                    %s as currency
                FROM crypto_prices
                WHERE currency = %s
                AND timestamp BETWEEN %s AND %s
                ORDER BY timestamp
            """, (currency, currency, strategy_info['start_time'], strategy_info['end_time']))

            klines = cursor.fetchall() or []

            # 获取交易记录
            cursor.execute(f"""
                SELECT
                    DATE_FORMAT(st.timestamp, '%%Y-%%m-%%d %%H:%%i:%%s') as timestamp,
                    st.action,
                    st.price,
                    st.amount,
                    st.fee,
                    st.profit_amount,
                    st.profit_percentage,
                    st.trigger_reason,
                    st.market_price,
                    st.account_value,
                    st.rest_minutes,
                    st.last_high,
                    st.last_low,
                    st.position_type,
                    st.entry_price,
                    st.position_size
                FROM {DB_TABLES['STRATEGY_TRADES']} st
                WHERE st.strategy_id = %s
                ORDER BY st.timestamp
            """, (strategy_id,))

            trades = cursor.fetchall() or []

            # 获取账户价值历史记录
            cursor.execute(f"""
                SELECT account_value_history, highest_account_value, lowest_account_value
                FROM {DB_TABLES['STRATEGY_RESULTS']}
                WHERE id = %s
            """, (strategy_id,))

            account_history = cursor.fetchone()

            # Convert decimal values to float for JSON serialization
            def safe_float_convert(value):
                if isinstance(value, decimal.Decimal):
                    return float(value)
                elif value is None:
                    return 0.0
                return value

            strategy_info = {k: safe_float_convert(v) for k, v in strategy_info.items()}

            # 修正策略参数的显示
            strategy_info['buy_rate'] = float(strategy_info.get('buy_rate', 0) or 0)
            strategy_info['sell_rate'] = float(strategy_info.get('sell_rate', 0) or 0)
            strategy_info['lookback_minutes_buy'] = int(strategy_info.get('lookback_minutes_buy', 0) or 0)
            strategy_info['lookback_minutes_sell'] = int(strategy_info.get('lookback_minutes_sell', 0) or 0)

            # 为了向后兼容，添加旧参数
            strategy_info['long_rebound_rate'] = strategy_info['buy_rate']
            strategy_info['long_pullback_rate'] = strategy_info['sell_rate']
            strategy_info['short_rebound_rate'] = strategy_info['buy_rate']
            strategy_info['short_pullback_rate'] = strategy_info['sell_rate']

            # 计算ROI
            if strategy_info['initial_capital'] > 0:
                strategy_info['roi'] = round((strategy_info['final_capital'] - strategy_info['initial_capital']) / strategy_info['initial_capital'] * 100, 2)
            else:
                strategy_info['roi'] = 0.0

            klines = [{k: safe_float_convert(v) for k, v in kline.items()} for kline in klines]
            trades = [{k: safe_float_convert(v) for k, v in trade.items()} for trade in trades]

            # 从结果表中获取策略的开始和结束时间
            cursor.execute(f"""
                SELECT start_time, end_time
                FROM {DB_TABLES['STRATEGY_RESULTS']}
                WHERE id = %s
            """, (strategy_id,))
            time_range = cursor.fetchone()

            if not time_range['start_time'] or not time_range['end_time']:
                return jsonify({'success': False, 'msg': 'No valid time range found in results'}), 404

            start_time = time_range['start_time'].strftime('%Y-%m-%d %H:%M:%S')
            end_time = time_range['end_time'].strftime('%Y-%m-%d %H:%M:%S')

            return jsonify({
                "success": True,
                "strategy_info": strategy_info,
                "klines": klines,
                "trades": trades,
                "account_history": account_history,
                "time_range": {
                    "start_time": start_time,
                    "end_time": end_time
                }
            })

    except Exception as e:
        logging.error(f"Error getting strategy chart data: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "获取策略图表数据失败",
            "details": str(e)
        }), 500
    finally:
        if conn:
            conn.close()

@app.route('/calendar')
def calendar():
    return render_template('calendar.html')

@app.route('/api/strategy_log')
def get_strategy_log():
    """获取策略日志文件内容"""
    try:
        strategy_id = request.args.get('strategy_id')
        is_live = request.args.get('is_live', '0')

        if not strategy_id:
            return jsonify({'error': '策略ID不能为空'}), 400

        # 构建日志文件路径
        log_path = f"log/strategy/strategy_line_{strategy_id}_live_{is_live}.log"

        # 检查文件是否存在
        if not os.path.exists(log_path):
            return jsonify({'error': f'找不到策略日志文件: {log_path}'}), 404

        # 读取文件内容
        with open(log_path, 'r', encoding='utf-8') as file:
            content = file.read()

        return Response(content, mimetype='text/plain')
    except Exception as e:
        logging.error(f"获取策略日志文件时出错: {str(e)}", exc_info=True)
        return jsonify({'error': str(e)}), 500

@app.route('/api/calendar_data')
def get_calendar_data():
    try:
        # 获取查询参数
        strategy_id_param = request.args.get('strategy_id')
        month = request.args.get('month')

        if not month:
            return jsonify({'error': '月份参数不能为空'})

        # 确保策略ID是整数
        strategy_id = None
        if strategy_id_param:
            try:
                strategy_id = int(strategy_id_param)
            except (ValueError, TypeError):
                return jsonify({'error': f'无效的策略ID: {strategy_id_param}'})

        conn = get_db_connection()
        try:
            with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                if strategy_id:
                    # 修改这里的查询，使用正确的日期格式化语法
                    query = f"""
                        SELECT
                            DATE(timestamp) as trade_date,
                            COUNT(*) as trades_count,
                            SUM(profit_amount+fee) as total_profit,
                            strategy_id
                        FROM {DB_TABLES['STRATEGY_TRADES']}
                        WHERE strategy_id = %s
                        AND DATE_FORMAT(timestamp, '%%Y-%%m') = %s
                        GROUP BY DATE(timestamp), strategy_id
                        ORDER BY trade_date
                    """
                    cursor.execute(query, (strategy_id, month))
                else:
                    # 先找到最优的策略ID
                    buy_rate = request.args.get('buy_rate', type=float)
                    sell_rate = request.args.get('sell_rate', type=float)
                    rest_minutes = request.args.get('rest_minutes', type=int)
                    lookback_minutes_buy = request.args.get('lookback_minutes_buy', type=int)
                    lookback_minutes_sell = request.args.get('lookback_minutes_sell', type=int)
                    currency = request.args.get('currency')

                    conditions = []
                    params = []

                    if buy_rate is not None:
                        conditions.append("sr.buy_rate = %s")
                        params.append(buy_rate)
                    if sell_rate is not None:
                        conditions.append("sr.sell_rate = %s")
                        params.append(sell_rate)
                    if rest_minutes is not None:
                        conditions.append("sr.rest_minutes = %s")
                        params.append(rest_minutes)
                    if currency:
                        conditions.append("sr.currency = %s")
                        params.append(currency)
                    if lookback_minutes_buy is not None:
                        conditions.append("sr.lookback_minutes_buy = %s")
                        params.append(lookback_minutes_buy)
                    if lookback_minutes_sell is not None:
                        conditions.append("sr.lookback_minutes_sell = %s")
                        params.append(lookback_minutes_sell)

                    # 查询最优策略ID
                    optimal_strategy_query = f"""
                        SELECT sr.id
                        FROM {DB_TABLES['STRATEGY_RESULTS']} sr
                        WHERE """ + " AND ".join(conditions) + """
                        ORDER BY sr.final_capital DESC
                        LIMIT 1
                    """
                    cursor.execute(optimal_strategy_query, params)
                    optimal_strategy = cursor.fetchone()

                    if optimal_strategy:
                        strategy_id = optimal_strategy['id']  # 使用字典方式访问

                        # 创建新的条件列表，专门用于交易查询
                        trade_conditions = ["st.strategy_id = %s"]
                        trade_params = [strategy_id]

                        # 添加日期条件
                        trade_conditions.append("DATE_FORMAT(st.timestamp, '%%Y-%%m') = %s")
                        trade_params.append(month)

                        query = f"""
                            SELECT
                                DATE(st.timestamp) as trade_date,
                                COUNT(*) as trades_count,
                                SUM(st.profit_amount-st.fee) as total_profit,
                                st.strategy_id
                            FROM {DB_TABLES['STRATEGY_TRADES']} st
                            WHERE """ + " AND ".join(trade_conditions) + """
                            GROUP BY DATE(st.timestamp), st.strategy_id
                            ORDER BY trade_date
                        """
                        cursor.execute(query, trade_params)
                    else:
                        # 处理没有找到策略ID的情况
                        return jsonify({'error': '未找到符合条件的策略'})

                results = cursor.fetchall()

                # 格式化结果
                calendar_data = [
                    {
                        'date': result['trade_date'].strftime('%Y-%m-%d'),
                        'trades_count': result['trades_count'],
                        'profit_amount': float(result['total_profit']) if result['total_profit'] is not None else 0,
                        'strategy_id': result['strategy_id']
                    }
                    for result in results
                ]

                return jsonify(calendar_data)

        except Exception as e:
            error_msg = f"数据库查询错误: {str(e)} \n错误详情:\n{traceback.format_exc()}"
            logging.error(error_msg)
            return jsonify({'error': error_msg}), 500
        finally:
            conn.close()

    except Exception as e:
        error_msg = f"处理请求时发生错误: {str(e)}\n{traceback.format_exc()}"
        logging.error(error_msg)
        return jsonify({'error': error_msg}), 500

def save_strategy_result(conn, result, strategy_params):
    """保存策略分析结果到数据库"""
    try:
        with conn.cursor(pymysql.cursors.DictCursor) as cursor:
            cursor.execute(f"""
                INSERT INTO {DB_TABLES['STRATEGY_RESULTS']} (
                    strategy_name, start_time, end_time,
                    long_rebound_rate, long_pullback_rate,
                    short_rebound_rate, short_pullback_rate,
                    buy_rate, sell_rate,
                    initial_capital, final_capital,
                    total_trades, successful_trades, total_profit, total_fees, roi, rest_minutes, lookback_minutes,
                    lookback_minutes_buy, lookback_minutes_sell,
                    currency
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            """, (
                'Rebound Strategy',
                result['analysis_start_time'],
                result['analysis_end_time'],
                strategy_params.get('long_rebound_rate', 0),
                strategy_params.get('long_pullback_rate', 0),
                strategy_params.get('short_rebound_rate', 0),
                strategy_params.get('short_pullback_rate', 0),
                strategy_params.get('buy_rate', strategy_params.get('short_rebound_rate', 0)) or 0,
                strategy_params.get('sell_rate', strategy_params.get('short_pullback_rate', 0)) or 0,
                result['initial_capital'] or 0,
                result['final_capital'] or 0,
                len(result['trades']),
                sum(1 for t in result['trades'] if t['action'] == 'sell' and t.get('profit_amount', 0) > 0),
                result['profit_loss'] or 0,
                result['total_fee'] or 0,
                result['profit_percentage'] or 0,
                result['rest_minutes'] or 0,
                result['lookback_minutes'] or 0,
                strategy_params.get('lookback_minutes_buy', result.get('lookback_minutes', 4)) or 0,
                strategy_params.get('lookback_minutes_sell', result.get('lookback_minutes', 4)) or 0,
                strategy_params.get('currency', 'DOGE')  # 添加币种参数，默认为DOGE
            ))

            # 获取插入的策略ID
            strategy_id = cursor.lastrowid

            # 插入交易记录
            for trade in result['trades']:
                cursor.execute(f"""
                    INSERT INTO {DB_TABLES['STRATEGY_TRADES']} (
                        strategy_id, timestamp, action, price, amount,
                        fee, profit_amount, profit_percentage, trigger_reason,
                        market_price, account_value, rest_minutes, lookback_minutes,
                        currency
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                """, (
                    strategy_id,
                    trade['timestamp'],
                    trade['action'],
                    trade['price'] if trade.get('price') is not None else 0,
                    trade['amount'] if trade.get('amount') is not None else 0,
                    trade['fee'] if trade.get('fee') is not None else 0,
                    trade.get('profit_amount', 0),
                    trade.get('profit_percentage', 0),
                    trade.get('trigger_reason', ''),
                    trade['market_price'] if trade.get('market_price') is not None else 0,
                    trade['account_value'] if trade.get('account_value') is not None else 0,
                    trade['rest_minutes'] if trade.get('rest_minutes') is not None else 0,
                    trade['lookback_minutes'] if trade.get('lookback_minutes') is not None else 0,
                    strategy_params.get('currency', 'DOGE')  # 添加币种参数，默认为DOGE
                ))

            conn.commit()
            return strategy_id

    except Exception as e:
        conn.rollback()
        app.logger.error(f'Error saving strategy result: {str(e)}')
        raise

@app.route('/api/strategy_results', methods=['GET'])
def get_strategy_results():
    """获取策略分析结果"""
    try:
        # 检查是否有ID参数
        strategy_id = request.args.get('id')

        with get_db_connection() as conn:
            with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                # 如果提供了ID，只查询该ID的策略
                if strategy_id:
                    cursor.execute(f"""
                        SELECT
                            id, strategy_name,
                            DATE_FORMAT(start_time, '%Y-%m-%d %H:%i:%s') as start_time,
                            DATE_FORMAT(end_time, '%Y-%m-%d %H:%i:%s') as end_time,
                            buy_rate, sell_rate,
                            lookback_minutes_buy, lookback_minutes_sell,
                            initial_capital, final_capital,
                            total_trades, successful_trades,
                            total_profit, total_fees, roi, rest_minutes,
                            currency
                        FROM {DB_TABLES['STRATEGY_RESULTS']}
                        WHERE id = %s
                    """, (strategy_id,))
                    results = cursor.fetchall()

                    if not results:
                        return jsonify([])  # 返回空数组而不是错误，便于前端处理
                else:
                    # 如果没有提供ID，返回所有策略（保持原有行为）
                    cursor.execute(f"""
                        SELECT
                            id, strategy_name,
                            DATE_FORMAT(start_time, '%Y-%m-%d %H:%i:%s') as start_time,
                            DATE_FORMAT(end_time, '%Y-%m-%d %H:%i:%s') as end_time,
                            buy_rate, sell_rate,
                            lookback_minutes_buy, lookback_minutes_sell,
                            initial_capital, final_capital,
                            total_trades, successful_trades,
                            total_profit, total_fees, roi, rest_minutes,
                            currency
                        FROM {DB_TABLES['STRATEGY_RESULTS']}
                        ORDER BY id DESC
                    """)
                    results = cursor.fetchall()

                # 转换Decimal为float以便JSON序列化，并处理None值
                for result in results:
                    for key, value in result.items():
                        if isinstance(value, decimal.Decimal):
                            result[key] = float(value)
                        elif value is None and key in ['buy_rate', 'sell_rate', 'lookback_minutes_buy', 'lookback_minutes_sell',
                                                      'initial_capital', 'final_capital', 'total_trades', 'successful_trades',
                                                      'total_profit', 'total_fees', 'roi', 'rest_minutes']:
                            # 为数值字段设置默认值
                            result[key] = 0

                return jsonify(results)

    except Exception as e:
        app.logger.error(f'Error getting strategy results: {str(e)}')
        return jsonify({
            'success': False,
            'msg': str(e)
        })

@app.route('/api/strategy_trades/<int:strategy_id>', methods=['GET'])
def get_strategy_trades(strategy_id):
    try:
        with get_db_connection() as conn:
            with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                # 从结果表中获取策略的开始和结束时间
                cursor.execute(f"""
                    SELECT start_time, end_time
                    FROM {DB_TABLES['STRATEGY_RESULTS']}
                    WHERE id = %s
                """, (strategy_id,))
                time_range = cursor.fetchone()

                if not time_range:
                    return jsonify({'success': False, 'msg': 'Strategy not found'}), 404

                start_time = time_range['start_time'].strftime('%Y-%m-%d %H:%M:%S')
                end_time = time_range['end_time'].strftime('%Y-%m-%d %H:%M:%S')

                # 获取策略交易记录
                cursor.execute(f"""
                    SELECT
                        id, strategy_id,
                        DATE_FORMAT(timestamp, '%Y-%m-%d %H:%i:%s') as timestamp,
                        action, price, amount, fee,
                        profit_amount, profit_percentage,
                        trigger_reason, market_price, account_value, rest_minutes, lookback_minutes
                    FROM {DB_TABLES['STRATEGY_TRADES']}
                    WHERE strategy_id = %s
                    ORDER BY timestamp
                """, (strategy_id,))
                trades = cursor.fetchall()

                # 转换Decimal为float以便JSON序列化，并处理None值
                for trade in trades:
                    for key, value in trade.items():
                        if isinstance(value, decimal.Decimal):
                            trade[key] = float(value)
                        elif value is None and key in ['price', 'amount', 'fee', 'profit_amount', 'profit_percentage',
                                                     'market_price', 'account_value', 'rest_minutes', 'lookback_minutes']:
                            # 为数值字段设置默认值
                            trade[key] = 0

                return jsonify({
                    'success': True,
                    'trades': trades,
                    'time_range': {
                        'start_time': start_time,
                        'end_time': end_time
                    }
                })

    except Exception as e:
        app.logger.error(f'Error getting strategy trades: {str(e)}')
        return jsonify({
            'success': False,
            'msg': str(e)
        })

@app.route('/api/strategy_info', methods=['GET'])
def get_strategy_info():
    """获取单个策略的基本信息（轻量级接口，专为日历页面设计）"""
    try:
        # 获取策略ID参数
        strategy_id = request.args.get('id')
        if not strategy_id:
            return jsonify({
                'success': False,
                'message': '缺少策略ID参数'
            })

        conn = get_db_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # 只查询日历页面需要的基本信息
        query = f"""
            SELECT
                id,
                buy_rate,
                sell_rate,
                rest_minutes,
                min_trigger_rest,
                lookback_minutes_buy,
                lookback_minutes_sell,
                DATE_FORMAT(start_time, '%Y-%m-%d %H:%i:%s') as start_time,
                currency
            FROM {DB_TABLES['STRATEGY_RESULTS']}
            WHERE id = %s
        """
        cursor.execute(query, (strategy_id,))
        result = cursor.fetchone()

        if result:
            # 处理数据类型
            for key, value in result.items():
                if isinstance(value, decimal.Decimal):
                    result[key] = float(value)
                elif value is None and key in ['buy_rate', 'sell_rate', 'lookback_minutes_buy', 'lookback_minutes_sell',
                                             'rest_minutes', 'min_trigger_rest']:
                    # 为数值字段设置默认值
                    result[key] = 0

            return jsonify({
                'success': True,
                'strategy': result
            })
        else:
            return jsonify({
                'success': False,
                'message': f'未找到ID为{strategy_id}的策略'
            })

    except Exception as e:
        app.logger.error(f"Error in get_strategy_info: {str(e)}")
        return jsonify({
            'success': False,
            'message': str(e)
        })
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()

@app.route('/api/best_strategy')
def get_best_strategy():
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 修改查询,使用新的参数字段
        query = f"""
            SELECT id, buy_rate, sell_rate,
                   rest_minutes, min_trigger_rest,
                   lookback_minutes_buy, lookback_minutes_sell,
                   start_time, final_capital,
                   currency
            FROM {DB_TABLES['STRATEGY_RESULTS']}
            WHERE final_capital IS NOT NULL
            ORDER BY final_capital DESC
            LIMIT 1
        """
        cursor.execute(query)
        result = cursor.fetchone()

        if result:
            try:
                if isinstance(result, dict):
                    strategy = {
                        'id': int(result['id']),
                        'buy_rate': float(result['buy_rate'] if result['buy_rate'] is not None else 0),
                        'sell_rate': float(result['sell_rate'] if result['sell_rate'] is not None else 0),
                        'rest_minutes': int(result['rest_minutes'] if result['rest_minutes'] is not None else 0),
                        'min_trigger_rest': int(result['min_trigger_rest'] if result['min_trigger_rest'] is not None else 0),
                        'lookback_minutes_buy': int(result['lookback_minutes_buy'] if result['lookback_minutes_buy'] is not None else 0),
                        'lookback_minutes_sell': int(result['lookback_minutes_sell'] if result['lookback_minutes_sell'] is not None else 0),
                        'start_time': result['start_time'].strftime('%Y-%m') if result['start_time'] else None,
                        'currency': result['currency'] if result['currency'] else 'DOGE'
                    }
                else:
                    strategy = {
                        'id': int(result[0]),
                        'buy_rate': float(result[1] if result[1] is not None else 0),
                        'sell_rate': float(result[2] if result[2] is not None else 0),
                        'rest_minutes': int(result[3] if result[3] is not None else 0),
                        'min_trigger_rest': int(result[4] if result[4] is not None else 0),
                        'lookback_minutes_buy': int(result[5] if result[5] is not None else 0),
                        'lookback_minutes_sell': int(result[6] if result[6] is not None else 0),
                        'start_time': result[7].strftime('%Y-%m') if result[7] else None,
                        'currency': result[8] if result[8] else 'DOGE'
                    }
                return jsonify({'success': True, 'strategy': strategy})
            except Exception as e:
                app.logger.error(f"Error processing strategy data: {str(e)}, Result type: {type(result)} \n错误详情:\n {traceback.format_exc()}")
                return jsonify({'success': False, 'message': f'处理策略数据时出错: {str(e)}'})
        else:
            cursor.execute(f"SELECT COUNT(*) FROM {DB_TABLES['STRATEGY_RESULTS']}")
            count = cursor.fetchone()[0]
            return jsonify({
                'success': False,
                'message': f'未找到策略数据，表中共有{count}条记录'
            })

    except Exception as e:
        app.logger.error(f"Error in get_best_strategy: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()

@app.before_request
def log_request_info():
    # 获取客户端IP地址
    client_ip = request.remote_addr
    # 判断是否为IPv6地址
    is_ipv6 = ':' in client_ip
    logging.info(f"Access from {'IPv6' if is_ipv6 else 'IPv4'} address: {client_ip}")

@app.route('/api/order_history', methods=['GET'])
def get_order_history_api():
    """获取历史委托数据"""
    try:
        # 获取并验证参数
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        currency = request.args.get('currency', 'DOGE')  # 默认为DOGE
        inst_id = request.args.get('inst_id')

        # 确保 inst_id 有效
        if not inst_id or inst_id == '-USDT-SWAP':
            inst_id = f"{currency}-USDT-SWAP"
            logging.info(f"Using default inst_id: {inst_id} based on currency: {currency}")

        logging.info(f"Order history request - currency: {currency}, inst_id: {inst_id}, start: {start_time}, end: {end_time}")

        # 如果没有提供时间范围，使用最近24小时
        if not start_time or not end_time:
            end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            start_time = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
            logging.info(f"Using default time range: {start_time} to {end_time}")
        else:
            logging.info(f"Using provided time range: {start_time} to {end_time}")

        # 检查是否是未来日期
        try:
            now = datetime.now()
            start_dt = parse(start_time)
            end_dt = parse(end_time)

            if start_dt > now:
                logging.warning(f"Start time {start_time} is in the future, using yesterday instead")
                start_time = (now - timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
                start_dt = parse(start_time)

            if end_dt > now:
                logging.warning(f"End time {end_time} is in the future, using current time instead")
                end_time = now.strftime('%Y-%m-%d %H:%M:%S')
                end_dt = parse(end_time)
        except Exception as e:
            logging.error(f"Error checking date range: {e}")
            # 继续使用原始时间范围
            logging.info(f"Using default time range: {start_time} to {end_time}")

        try:
            # 验证时间范围
            start_dt = parse(start_time)
            end_dt = parse(end_time)
        except Exception as e:
            logging.error(f"Error parsing dates: {e}")
            return jsonify({
                'code': 1,
                'msg': f'日期格式错误: {str(e)}'
            })

        # 获取历史委托数据
        start_time_str = start_dt.strftime('%Y-%m-%d %H:%M:%S')
        end_time_str = end_dt.strftime('%Y-%m-%d %H:%M:%S')

        logging.info(f"Formatted time range: {start_time_str} to {end_time_str}")

        result = okx_api.get_order_history_by_time(
            start_time=start_time_str,
            end_time=end_time_str,
            instId=inst_id
        )

        if result.get('code') != '0':
            return jsonify({
                'code': 1,
                'msg': result.get('msg', '获取历史委托数据失败')
            })

        return jsonify({
            'code': 0,
            'data': result.get('data', {}),
            'metadata': result.get('metadata', {
                'start_time': start_dt.strftime('%Y-%m-%d %H:%M:%S'),
                'end_time': end_dt.strftime('%Y-%m-%d %H:%M:%S'),
                'instrument_id': inst_id,
                'total_minutes': 0,
                'total_orders': 0
            })
        })

    except Exception as e:
        logging.error(f"Error in get_order_history_api: {str(e)}", exc_info=True)
        return jsonify({
            'code': 1,
            'msg': str(e)
        })

@app.route('/api/currencies', methods=['GET'])
def get_currencies():
    """获取所有可用的币种"""
    conn = None
    cursor = None
    try:
        # 记录开始获取币种列表
        logging.info("开始获取币种列表")

        # 获取数据库连接
        conn = get_db_connection()
        if not conn:
            logging.error("无法建立数据库连接")
            return jsonify({
                'code': 1,
                'message': '数据库连接失败'
            })

        # 创建游标
        cursor = conn.cursor(pymysql.cursors.DictCursor)  # 使用字典游标

        # 先检查表是否存在
        cursor.execute("SHOW TABLES LIKE 'crypto_prices'")
        if not cursor.fetchone():
            logging.error("crypto_prices表不存在")
            return jsonify({
                'code': 1,
                'message': 'crypto_prices表不存在'
            })

        # 执行查询
        sql = "SELECT DISTINCT currency FROM crypto_prices ORDER BY currency"
        logging.info(f"执行SQL查询: {sql}")
        cursor.execute(sql)

        # 获取结果
        results = cursor.fetchall()
        if not results:
            logging.warning("未找到任何币种数据")
            return jsonify({
                'code': 0,
                'data': [],
                'message': '未找到任何币种数据'
            })

        # 从字典结果中提取币种
        currencies = [row['currency'] for row in results]
        logging.info(f"查询到 {len(currencies)} 个币种: {currencies}")

        return jsonify({
            'code': 0,
            'data': currencies,
            'message': 'success'
        })
    except Exception as e:
        error_msg = f"获取币种列表时出错: {str(e)}\n{traceback.format_exc()}"
        logging.error(error_msg)
        return jsonify({
            'code': 1,
            'message': error_msg
        })
    finally:
        try:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
            logging.info("数据库连接已关闭")
        except Exception as e:
            logging.error(f"关闭数据库连接时出错: {str(e)}")

@app.route('/api/instruments', methods=['GET'])
def get_instruments():
    """获取产品列表信息"""
    try:
        # 获取请求参数
        inst_type = request.args.get('inst_type')  # SPOT/SWAP/FUTURES/OPTION

        # 调用OKX API获取产品列表
        result = okx_api.get_instruments(instType=inst_type)

        return jsonify(result)

    except Exception as e:
        error_msg = f"获取产品列表失败: {str(e)}"
        logging.error(error_msg)
        return jsonify({
            'code': 1,
            'message': error_msg,
            'data': []
        })

@app.route('/realtime')
def realtime():
    return render_template('realtime.html')

@app.route('/debug/trade_price/<timestamp>')
def debug_trade_price(timestamp):
    """调试特定时间点的交易价格问题"""
    try:
        # 解析时间戳 (格式: 2025-03-01_02:19:00)
        target_time = datetime.strptime(timestamp, '%Y-%m-%d_%H:%M:%S')
        target_time_str = target_time.strftime('%Y-%m-%d %H:%M:%S')

        # 查询周围时间的K线数据
        start_time = target_time - timedelta(minutes=10)
        end_time = target_time + timedelta(minutes=10)

        conn = get_db_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # 查询K线数据
        kline_query = """
        SELECT timestamp, open_price, close_price, high_price, low_price, volume
        FROM crypto_prices
        WHERE timestamp BETWEEN %s AND %s
        ORDER BY timestamp
        """
        cursor.execute(kline_query, (start_time, end_time))
        kline_data = cursor.fetchall()

        # 查询交易数据
        trade_start = target_time - timedelta(minutes=5)
        trade_end = target_time + timedelta(minutes=5)

        trade_query = f"""
        SELECT timestamp, position_type, price, amount, fee, trigger_reason
        FROM {DB_TABLES['STRATEGY_TRADES']}
        WHERE timestamp BETWEEN %s AND %s
        ORDER BY timestamp
        """
        cursor.execute(trade_query, (trade_start, trade_end))
        trade_data = cursor.fetchall()

        cursor.close()
        conn.close()

        # 找到目标时间点的具体数据
        target_kline = None
        target_trade = None

        for kline in kline_data:
            if kline['timestamp'].strftime('%Y-%m-%d %H:%M:%S') == target_time_str:
                target_kline = kline
                break

        for trade in trade_data:
            if trade['timestamp'].strftime('%Y-%m-%d %H:%M:%S') == target_time_str:
                target_trade = trade
                break

        # 分析价格范围问题
        analysis = {
            'target_time': target_time_str,
            'target_kline': target_kline,
            'target_trade': target_trade,
            'surrounding_klines': kline_data,
            'surrounding_trades': trade_data,
            'price_analysis': None
        }

        # 如果有交易和K线数据，进行价格分析
        if target_kline and target_trade:
            trade_price = float(target_trade['price'])
            kline_high = float(target_kline['high_price'])
            kline_low = float(target_kline['low_price'])

            price_analysis = {
                'trade_price': trade_price,
                'kline_high': kline_high,
                'kline_low': kline_low,
                'price_in_range': kline_low <= trade_price <= kline_high,
                'exceed_high': max(0, trade_price - kline_high),
                'exceed_low': max(0, kline_low - trade_price),
                'exceed_percentage': 0
            }

            if trade_price > kline_high:
                price_analysis['exceed_percentage'] = ((trade_price - kline_high) / kline_high) * 100
                price_analysis['exceed_direction'] = 'high'
            elif trade_price < kline_low:
                price_analysis['exceed_percentage'] = ((kline_low - trade_price) / kline_low) * 100
                price_analysis['exceed_direction'] = 'low'
            else:
                price_analysis['exceed_direction'] = 'none'

            analysis['price_analysis'] = price_analysis

        return jsonify({
            'success': True,
            'data': analysis
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    # 生产模式启动，禁用调试和自动重载以减少CPU占用
    app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False, threaded=True)
