#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试新策略方法
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def main():
    print("=== 简单测试新策略 ===")
    
    try:
        # 创建策略分析器
        analyzer = StrategyAnalyzer(DB_CONFIG)
        print("策略分析器创建成功")
        
        # 测试参数
        start_time = datetime(2025, 6, 6, 0, 0, 0)
        end_time = datetime(2025, 6, 6, 23, 59, 59)
        
        print(f"测试时间: {start_time} 到 {end_time}")
        print("开始分析...")
        
        # 运行新策略分析
        result = analyzer.analyze_new_strategy(
            start_time=start_time,
            end_time=end_time,
            currency='DOGE',
            buy_rate=1.0,
            sell_rate=0.02,
            rest_minutes=0,
            lookback_minutes_buy=5,
            lookback_minutes_sell=2
        )
        
        print("=== 分析结果 ===")
        print(f"初始资金: ${result['initial_capital']:.4f}")
        print(f"最终资金: ${result['final_capital']:.4f}")
        print(f"总收益: ${result['total_profit']:.4f}")
        print(f"收益率: {result['return_rate']:.2f}%")
        print(f"交易次数: {result['total_trades']}")
        print(f"总手续费: ${result['total_fees']:.4f}")
        
        if result['trades']:
            print(f"\n=== 前5条交易记录 ===")
            for i, trade in enumerate(result['trades'][:5], 1):
                action = "BUY " if trade['action'] == '开多' else "SELL" if trade['action'] == '平多' else trade['action']
                print(f"{i}. {trade['timestamp']} | {action:4s} | ${trade['price']:8.4f} | 收益: ${trade['profit']:8.4f}")
        
        print("\n测试完成！")
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
