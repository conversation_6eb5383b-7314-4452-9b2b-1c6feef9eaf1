#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
对比开盘价保护调整和直接开盘价的收益差异
"""

import subprocess
import sys
import time

def run_strategy_test(price_adjust_rate, description):
    """运行策略测试并返回结果"""
    print(f"\n{'='*60}")
    print(f"测试: {description}")
    print(f"price-adjust-rate: {price_adjust_rate}")
    print(f"{'='*60}")
    
    cmd = [
        "python", "strategy_analyzer.py",
        "--start", "2025-04-10 02:18:00",
        "--end", "2025-04-10 02:32:00", 
        "--sell-rate", "1",
        "--buy-rate", "1",
        "--rest", "0",
        "--test-new-strategy",
        "--min-trigger-rest", "0",
        "--lookback-minutes-buy", "2",
        "--lookback-minutes-sell", "5",
        "--debug",
        "--price-adjust-rate", str(price_adjust_rate)
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
            
        return result.stdout, result.stderr
        
    except subprocess.TimeoutExpired:
        print("测试超时")
        return None, "超时"
    except Exception as e:
        print(f"测试失败: {e}")
        return None, str(e)

def extract_key_info(output):
    """从输出中提取关键信息"""
    if not output:
        return {}
    
    info = {
        'trades': [],
        'total_profit': None,
        'price_adjustments': []
    }
    
    lines = output.split('\n')
    for line in lines:
        # 提取交易记录
        if '|' in line and ('BUY' in line or 'SELL' in line):
            info['trades'].append(line.strip())
        
        # 提取价格调整信息
        if '调整成交价为开盘价' in line:
            info['price_adjustments'].append(line.strip())
        
        # 提取总收益
        if '总收益' in line or '最终收益' in line:
            info['total_profit'] = line.strip()
    
    return info

def main():
    print("开始对比测试：开盘价保护调整 vs 直接开盘价")
    
    # 测试1: 直接开盘价 (price_adjust_rate = 0)
    stdout1, stderr1 = run_strategy_test(0.0, "直接开盘价买入卖出")
    info1 = extract_key_info(stdout1)
    
    time.sleep(2)
    
    # 测试2: 开盘价+0.1% (price_adjust_rate = 0.001)  
    stdout2, stderr2 = run_strategy_test(0.001, "开盘价保护+0.1%")
    info2 = extract_key_info(stdout2)
    
    time.sleep(2)
    
    # 测试3: 开盘价+1% (price_adjust_rate = 0.01)
    stdout3, stderr3 = run_strategy_test(0.01, "开盘价保护+1%")
    info3 = extract_key_info(stdout3)
    
    # 对比分析
    print(f"\n{'='*80}")
    print("对比分析结果")
    print(f"{'='*80}")
    
    tests = [
        ("直接开盘价", info1),
        ("开盘价+0.1%", info2), 
        ("开盘价+1%", info3)
    ]
    
    for name, info in tests:
        print(f"\n{name}:")
        print(f"  交易次数: {len(info['trades'])}")
        if info['trades']:
            print("  交易记录:")
            for trade in info['trades']:
                print(f"    {trade}")
        
        if info['price_adjustments']:
            print("  价格调整:")
            for adj in info['price_adjustments']:
                print(f"    {adj}")
        
        if info['total_profit']:
            print(f"  总收益: {info['total_profit']}")
    
    # 详细分析差异原因
    print(f"\n{'='*80}")
    print("差异原因分析")
    print(f"{'='*80}")
    
    if len(info1['trades']) != len(info2['trades']):
        print(f"⚠️ 交易次数不同: 直接开盘价({len(info1['trades'])}) vs 开盘价+0.1%({len(info2['trades'])})")
    
    if len(info1['trades']) > 0 and len(info2['trades']) > 0:
        print("\n交易价格对比:")
        for i, (trade1, trade2) in enumerate(zip(info1['trades'], info2['trades'])):
            print(f"  交易{i+1}:")
            print(f"    直接开盘价: {trade1}")
            print(f"    开盘价+0.1%: {trade2}")

if __name__ == "__main__":
    main()
