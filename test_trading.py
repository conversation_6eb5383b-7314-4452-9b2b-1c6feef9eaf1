import logging
import ssl
import httpx
import certifi

# 禁用SSL验证
ssl._create_default_https_context = ssl._create_unverified_context

# 创建一个全局的 httpx 客户端实例
client = httpx.Client(verify=True)

# 设置根日志级别为INFO
logging.basicConfig(level=logging.INFO)

# 设置特定模块的日志级别
logging.getLogger('hpack.hpack').setLevel(logging.WARNING)
logging.getLogger('httpx').setLevel(logging.WARNING)
logging.getLogger('httpcore').setLevel(logging.WARNING)

from trading_executor import trading_executor
import time
from okx_api_handler import okx_api

def test_account_balance():
    """测试获取账户余额"""
    print("\n=== 测试获取账户余额 ===")
    
    # 测试USDT余额
    print("\n>> 测试USDT余额:")
    print("正在获取USDT余额...")
    usdt_balance = trading_executor.get_account_balance('USDT')
    print("USDT账户余额信息:")
    for key, value in usdt_balance.items():
        print(f"  {key}: {value}")
    
    time.sleep(1)  # 等待1秒避免请求过快
    
    # 测试BTC余额
    print("\n>> 测试BTC余额:")
    print("正在获取BTC余额...")
    btc_balance = trading_executor.get_account_balance('DOGE')
    print("DOGE账户余额信息:")
    for key, value in btc_balance.items():
        print(f"  {key}: {value}")

def calculate_contract_size(usdt_amount: float):
    """计算合约张数"""
    print("\n=== USDT金额换算合约张数 ===")
    print(f"计划投入USDT金额: {usdt_amount}")
    
    # 获取DOGE当前价格和合约信息
    result = okx_api.get_tickers(instType="SWAP")
    
    if result['code'] == 0:
        doge_ticker = None
        for ticker in result['data']:
            if ticker['instrument_id'] == 'DOGE-USDT-SWAP':
                doge_ticker = ticker
                break
        
        if doge_ticker:
            current_price = float(doge_ticker['last_price'])
            print(f"\nDOGE当前价格: {current_price} USDT")
            
            # 获取合约具体信息
            contract_info = okx_api.get_instruments(instType="SWAP")
            contract_detail = None
            
            if contract_info['code'] == 0:
                for instrument in contract_info['data']:
                    if instrument['instrument_id'] == 'DOGE-USDT-SWAP':
                        contract_detail = instrument
                        break
                
                if contract_detail:
                    # 每张合约价值1000 DOGE
                    contract_value = 1000  # 每张合约代表的DOGE数量
                    min_size = float(contract_detail['min_size'])  # 最小下单数量
                    
                    # 计算每张合约的USDT价值
                    contract_usdt_value = contract_value * current_price
                    print(f"每张合约价值: {contract_usdt_value:.4f} USDT")
                    
                    # 计算能买多少张合约
                    contract_num = usdt_amount / contract_usdt_value
                    # 向下取整到最小下单单位
                    contract_num = (int(contract_num / min_size) * min_size)
                    
                    # 计算实际能买到的DOGE数量
                    actual_doge = contract_num * contract_value
                    actual_usdt = contract_num * contract_usdt_value
                    
                    print(f"\n换算结果:")
                    print(f"当前DOGE价格: {current_price} USDT")
                    print(f"每张合约面值: {contract_value} DOGE")
                    print(f"每张合约价值: {contract_usdt_value:.4f} USDT")
                    print(f"最小下单单位: {min_size} 张")
                    print(f"可以购买的合约张数: {contract_num} 张")
                    print(f"对应DOGE数量: {actual_doge:.4f} DOGE")
                    print(f"实际投入USDT: {actual_usdt:.4f} USDT")
                    
                    return {
                        'contract_num': contract_num,
                        'doge_amount': actual_doge,
                        'current_price': current_price,
                        'actual_usdt': actual_usdt
                    }
    
    print("获取合约信息失败")
    return None

def test_spot_trade():
    """测试永续合约交易"""
    print("\n=== 测试永续合约交易 ===")
    
    # 计算要购买的合约张数（假设要投入100 USDT）
    calc_result = calculate_contract_size(5)
    if not calc_result:
        print("计算合约张数失败，终止交易")
        return

    # 设置持仓方式为单向持仓
    print("\n>> 设置持仓方式:")
    print("正在设置持仓方式...")
    mode_result = okx_api.set_position_mode(posMode='long_short_mode')
    print(f"设置持仓方式结果: {mode_result}")
    
    time.sleep(1)  # 等待1秒
    
    # 设置杠杆倍数
    print("\n>> 设置杠杆倍数:")
    print("正在设置杠杆倍数...")
    leverage_result = okx_api.set_leverage(
        instId='DOGE-USDT-SWAP',
        lever='1',  # 1倍杠杆
        mgnMode='cross'  # 全仓模式
    )
    print(f"设置杠杆倍数结果: {leverage_result}")
    
    time.sleep(1)  # 等待1秒
    
    # 测试开多（买入）
    # print("\n>> 测试开多:")
    # print("正在执行开多操作...")
    # buy_result = trading_executor.execute_perpetual_trade(
    #     instId="DOGE-USDT-SWAP",  # 永续合约
    #     tdMode="cross",           # 全仓模式
    #     side="buy",               # 买入开多
    #     ordType="market",         # 市价单
    #     sz=str(calc_result['contract_num']),  # 计算得到的合约张数
    #     posSide="long"           # 持仓方向为多
    # )
    # print(f"开多订单结果: {buy_result}")
    
    # time.sleep(10)  # 等待10秒
    
    # 测试平多（卖出）
    print("\n>> 测试平多:")
    
    print("正在执行平多操作...")
    sell_result = trading_executor.execute_perpetual_trade(
       instId="DOGE-USDT-SWAP",  # 永续合约
       tdMode="cross",           # 全仓模式
       side="sell",              # 卖出平多
       ordType="market",         # 市价单
       sz="0.02",               # 合约张数
       posSide="long"           # 持仓方向为多
    )
    print(f"平多订单结果: {sell_result}")

def test_position_info():
    """测试获取持仓信息"""
    print("\n=== 测试获取持仓信息 ===")
    print("正在获取持仓信息...")
    position = trading_executor.get_position_info("DOGE-USDT-SWAP")
    print(f"DOGE-USDT-SWAP持仓信息: {position}")

def test_instrument_info():
    """测试获取产品信息"""
    print("\n=== 测试获取产品信息 ===")
    print("正在获取产品信息...")
    result = okx_api.get_instruments(instType="SWAP")
    
    if result['code'] == 0:
        for instrument in result['data']:
            if instrument['instrument_id'] == 'DOGE-USDT-SWAP':
                print("\nDOGE-USDT-SWAP产品信息:")
                for key, value in instrument.items():
                    print(f"  {key}: {value}")
                break
    else:
        print(f"获取产品信息失败: {result['message']}")

def test_history_orders():
    """测试获取历史订单记录"""
    print("\n=== 测试获取历史订单记录 ===")
    print("正在获取最近7天的订单记录...")
    
    # 获取所有类型的订单
    result = trading_executor.get_history_orders(
        instId="DOGE-USDT-SWAP"  # 指定产品
    )
    
    if result.get('code') == '0':
        orders = result.get('data', [])
        print(f"\n找到 {len(orders)} 条订单记录:")
        
        for order in orders:
            print("\n订单详情:")
            print(f"  订单ID: {order['order_id']}")
            print(f"  产品: {order['instrument_id']}")
            print(f"  方向: {order['side']}")
            print(f"  类型: {order['order_type']}")
            print(f"  状态: {order['state']}")
            print(f"  价格: {order['price']}")
            print(f"  数量: {order['size']}")
            print(f"  成交均价: {order['filled_price']}")
            print(f"  成交数量: {order['filled_size']}")
            print(f"  手续费: {order['fee']} {order['fee_currency']}")
            print(f"  创建时间: {order['create_time']}")
            print(f"  更新时间: {order['update_time']}")
    else:
        print(f"\n获取订单记录失败: {result.get('msg', '未知错误')}")

def run_all_tests():
    """运行所有测试"""
    print("开始执行交易测试...\n")
    
    try:
        # 测试获取产品信息
        #test_instrument_info()
        
        #time.sleep(1)  # 等待1秒
        
        # 测试账户余额
        #test_account_balance()
        
        #time.sleep(1)  # 等待1秒
        
        # 测试永续合约交易
        test_spot_trade()
        
        time.sleep(1)  # 等待1秒
        
        # 测试持仓信息
        test_position_info()
        
        time.sleep(1)  # 等待1秒
        
        # 测试历史订单记录
        test_history_orders()
        
        print("\n所有测试完成!")
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    run_all_tests()