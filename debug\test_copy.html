<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>右键复制功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-area {
            width: 400px;
            height: 300px;
            background-color: #fff;
            border: 2px solid #ddd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        
        .test-area:hover {
            border-color: #007bff;
        }
        
        .test-area.active {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        
        .message {
            padding: 10px 20px;
            border-radius: 4px;
            margin: 10px 0;
            display: none;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .paste-area {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            resize: vertical;
        }
        
        .instructions {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
            margin-bottom: 20px;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>🖱️ 右键复制功能测试</h1>
    
    <div class="instructions">
        <h3>测试说明</h3>
        <div class="step">
            <strong>步骤 1:</strong> 在下方的测试区域内右键点击
        </div>
        <div class="step">
            <strong>步骤 2:</strong> 观察是否出现成功提示消息
        </div>
        <div class="step">
            <strong>步骤 3:</strong> 在下方的文本框中粘贴 (Ctrl+V)
        </div>
        <div class="step">
            <strong>步骤 4:</strong> 验证是否粘贴出 "showDetailModal"
        </div>
    </div>
    
    <div class="test-area" id="testArea">
        <div style="text-align: center;">
            <h3>🎯 测试区域</h3>
            <p>在此区域右键点击</p>
            <p style="color: #666; font-size: 14px;">应该复制 "showDetailModal" 到剪贴板</p>
        </div>
    </div>
    
    <div id="message" class="message"></div>
    
    <h3>📋 粘贴测试区域</h3>
    <textarea class="paste-area" placeholder="在这里粘贴 (Ctrl+V) 来验证复制是否成功..."></textarea>
    
    <div style="margin-top: 20px;">
        <h3>🔧 功能状态</h3>
        <p id="clipboardSupport"></p>
        <p id="secureContext"></p>
    </div>

    <script>
        // 检查浏览器支持情况
        document.getElementById('clipboardSupport').textContent = 
            `Clipboard API 支持: ${navigator.clipboard ? '✅ 是' : '❌ 否'}`;
        document.getElementById('secureContext').textContent = 
            `安全上下文: ${window.isSecureContext ? '✅ 是' : '❌ 否'}`;

        const testArea = document.getElementById('testArea');
        const messageDiv = document.getElementById('message');

        // 显示消息的函数
        function showMessage(text, type = 'info', duration = 3000) {
            messageDiv.textContent = text;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';
            
            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, duration);
        }

        // 降级复制方案
        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            textArea.style.opacity = "0";
            
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    console.log('已复制到剪贴板 (降级方案):', text);
                    showMessage('✅ 已复制 "showDetailModal" 到剪贴板 (降级方案)', 'success');
                } else {
                    console.error('复制失败 (降级方案)');
                    showMessage('❌ 复制失败，请手动复制', 'error');
                }
            } catch (err) {
                console.error('复制失败 (降级方案):', err);
                showMessage('❌ 复制失败，请手动复制', 'error');
            }
            
            document.body.removeChild(textArea);
        }

        // 添加右键菜单功能
        testArea.addEventListener('contextmenu', function(e) {
            e.preventDefault(); // 阻止默认右键菜单
            
            // 视觉反馈
            testArea.classList.add('active');
            setTimeout(() => {
                testArea.classList.remove('active');
            }, 200);
            
            // 复制 showDetailModal 文字到剪贴板
            const textToCopy = 'showDetailModal';
            
            // 使用现代的 Clipboard API
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(textToCopy).then(function() {
                    console.log('已复制到剪贴板:', textToCopy);
                    showMessage('✅ 已复制 "showDetailModal" 到剪贴板', 'success');
                }).catch(function(err) {
                    console.error('复制失败:', err);
                    fallbackCopyTextToClipboard(textToCopy);
                });
            } else {
                // 降级方案
                fallbackCopyTextToClipboard(textToCopy);
            }
        });

        // 添加左键点击提示
        testArea.addEventListener('click', function(e) {
            showMessage('ℹ️ 请使用右键点击来测试复制功能', 'info');
        });

        console.log('右键复制功能测试页面已加载');
    </script>
</body>
</html>
