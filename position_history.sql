-- ----------------------------
-- Table structure for position_history
-- ----------------------------
DROP TABLE IF EXISTS `position_history`;
CREATE TABLE `position_history` (
  `id` int NOT NULL AUTO_INCREMENT,
  `timestamp` datetime NOT NULL COMMENT '时间戳',
  `currency` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '币种',
  `position_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '持仓类型(long/short/none)',
  `position_size` decimal(20, 8) NOT NULL DEFAULT 0 COMMENT '持仓大小',
  `entry_price` decimal(20, 8) NOT NULL DEFAULT 0 COMMENT '入场价格',
  `market_price` decimal(20, 8) NOT NULL DEFAULT 0 COMMENT '当前市场价格',
  `unrealized_pnl` decimal(20, 8) NOT NULL DEFAULT 0 COMMENT '未实现盈亏',
  `leverage` decimal(10, 2) NOT NULL DEFAULT 1 COMMENT '杠杆倍数',
  `margin_mode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'cross' COMMENT '保证金模式',
  `liquidation_price` decimal(20, 8) NOT NULL DEFAULT 0 COMMENT '强平价格',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'active' COMMENT '状态(active/closed)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_position_history_timestamp`(`timestamp` ASC) USING BTREE COMMENT '时间戳索引',
  INDEX `idx_position_history_currency`(`currency` ASC) USING BTREE COMMENT '币种索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '持仓历史记录表' ROW_FORMAT = DYNAMIC;
