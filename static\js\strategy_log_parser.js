/**
 * 策略日志解析器
 * 用于解析和处理策略日志文件
 */
class StrategyLogParser {
    constructor() {
        this.data = null;
        this.headers = {
            zh: [], // 中文列名
            en: []  // 英文列名
        };
        this.rows = [];
        this.timeIndex = -1;
    }

    /**
     * 加载并解析日志文件
     * @param {string} strategyId - 策略ID
     * @param {boolean} isLiveTrading - 是否实盘交易
     * @returns {Promise<Object>} - 解析后的数据
     */
    async loadLogFile(strategyId, isLiveTrading = false) {
        try {
            const liveValue = isLiveTrading ? 1 : 0;
            const url = `/api/strategy_log?strategy_id=${strategyId}&is_live=${liveValue}`;
            const response = await fetch(url);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const text = await response.text();
            this.parseLogContent(text);
            return this.data;
        } catch (error) {
            console.error('Error loading strategy log file:', error);
            throw error;
        }
    }

    /**
     * 解析日志内容
     * @param {string} content - 日志文件内容
     */
    parseLogContent(content) {
        // 分割行
        const lines = content.trim().split('\n');

        if (lines.length < 3) {
            throw new Error('日志文件格式不正确，至少需要3行（中文标题、英文标题和数据）');
        }

        // 解析标题行
        this.headers.zh = lines[0].split('\t');
        this.headers.en = lines[1].split('\t');

        // 找到时间列的索引
        this.timeIndex = this.headers.en.findIndex(header => header === 'timestamp');

        if (this.timeIndex === -1) {
            throw new Error('找不到时间列');
        }

        // 解析数据行
        this.rows = [];
        // 创建以时间为键的对象
        this.rowsByTime = {};

        for (let i = 2; i < lines.length; i++) {
            const values = lines[i].split('\t');
            if (values.length === this.headers.en.length) {
                const row = {};
                let timestamp = '';

                for (let j = 0; j < this.headers.en.length; j++) {
                    const key = this.headers.en[j];
                    let value = values[j];

                    // 尝试将数值转换为数字
                    if (!isNaN(value) && value !== '') {
                        value = parseFloat(value);
                    }

                    row[key] = value;

                    // 记录时间戳
                    if (j === this.timeIndex) {
                        timestamp = value;
                    }
                }

                // 添加到数组和以时间为键的对象中
                this.rows.push(row);

                // 确保时间戳存在且有效
                if (timestamp) {
                    this.rowsByTime[timestamp] = row;
                }
            }
        }

        // 构建数据对象
        this.data = {
            headers: this.headers,
            rows: this.rows,
            rowsByTime: this.rowsByTime,
            timeIndex: this.timeIndex
        };
        
    }

    /**
     * 获取指定列的所有值
     * @param {string} columnName - 列名
     * @returns {Array} - 列值数组
     */
    getColumnData(columnName) {
        if (!this.data || !this.rows) {
            throw new Error('数据未加载');
        }

        return this.rows.map(row => row[columnName]);
    }

    /**
     * 获取指定时间的行数据
     * @param {string} timestamp - 时间戳
     * @returns {Object|null} - 行数据或null
     */
    getRowByTime(timestamp) {
        if (!this.data || !this.rowsByTime) {
            throw new Error('数据未加载');
        }

        // 直接从以时间为键的对象中获取数据，O(1)复杂度
        return this.rowsByTime[timestamp] || null;
    }

    /**
     * 获取所有时间戳
     * @returns {Array<string>} - 时间戳数组
     */
    getTimestamps() {
        return this.getColumnData('timestamp');
    }

    /**
     * 按单位分组列
     * @returns {Object} - 分组后的列
     */
    groupColumnsByUnit() {
        if (!this.data || !this.headers) {
            throw new Error('数据未加载');
        }

        // 定义不同单位的列组
        const groups = {
            price: ['open', 'close', 'high', 'low', 'last_low', 'last_high', 'min_trigger_price', 'trigger_price', 'actual_trigger_price', 'protection_price', 'last_position_price'],
            percentage: ['current_percentage'],
            count: ['continuous_up_count', 'continuous_down_count', 'entry_time_diff'],
            minutes: ['rest_minutes'],
            account: ['account_value'],
            position: ['current_position'],
            profit: ['total_profit'],
            fees:[ 'total_fees'],
            other: [ 'total_trades'] // 其他未分类的列
        };

        // 分类每个列
        const result = {};
        for (const group in groups) {
            result[group] = [];
        }

        this.headers.en.forEach(header => {
            if (header === 'timestamp') return; // 跳过时间列

            let assigned = false;
            for (const group in groups) {
                if (groups[group].includes(header)) {
                    result[group].push(header);
                    assigned = true;
                    break;
                }
            }

            if (!assigned) {
                result.other.push(header);
            }
        });
        // debugger
        return result;
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StrategyLogParser;
}
