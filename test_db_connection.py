#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试数据库连接
"""

import pymysql
from config import *
import time

def test_db_connection():
    """测试数据库连接"""
    print("=== 数据库连接测试 ===")
    print(f"主机: {MYSQL_HOST}")
    print(f"端口: {MYSQL_PORT}")
    print(f"用户: {MYSQL_USER}")
    print(f"数据库: {MYSQL_DATABASE}")
    
    try:
        print("正在尝试连接数据库...")
        start_time = time.time()
        
        conn = pymysql.connect(
            host=MYSQL_HOST,
            port=MYSQL_PORT,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=MYSQL_DATABASE,
            charset='utf8mb4',
            connect_timeout=10,  # 10秒连接超时
            read_timeout=30,     # 30秒读取超时
            write_timeout=30     # 30秒写入超时
        )
        
        end_time = time.time()
        print(f"✅ 数据库连接成功！耗时: {end_time - start_time:.2f}秒")
        
        # 测试查询
        with conn.cursor() as cursor:
            cursor.execute("SELECT 1 as test")
            result = cursor.fetchone()
            print(f"✅ 测试查询成功: {result}")
            
            # 检查表是否存在
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"✅ 数据库中的表: {[table[0] for table in tables]}")
        
        conn.close()
        print("✅ 数据库连接已关闭")
        
    except pymysql.err.OperationalError as e:
        print(f"❌ 数据库连接失败 (OperationalError): {e}")
        if "timed out" in str(e):
            print("   可能的原因:")
            print("   1. 网络连接问题")
            print("   2. 数据库服务器未启动")
            print("   3. 防火墙阻止连接")
            print("   4. 数据库配置错误")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        import traceback
        traceback.print_exc()

def test_network_connectivity():
    """测试网络连通性"""
    print("\n=== 网络连通性测试 ===")
    import socket
    
    try:
        print(f"正在测试到 {MYSQL_HOST}:{MYSQL_PORT} 的网络连接...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((MYSQL_HOST, MYSQL_PORT))
        sock.close()
        
        if result == 0:
            print("✅ 网络连接正常")
        else:
            print(f"❌ 网络连接失败，错误代码: {result}")
    except Exception as e:
        print(f"❌ 网络测试失败: {e}")

if __name__ == "__main__":
    test_network_connectivity()
    test_db_connection()
