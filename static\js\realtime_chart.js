// WebSocket连接管理
let ws = null;
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;
let lastKlineData = null;

// 全局变量
let klineDataArray = [];
let volumes = [];
let timestamps = [];
let chart = null;
let currentDataZoom = {start: 0, end: 100}; // 保存当前缩放状态
let lastTooltipParams = null; // 保存最后的提示框参数

// 添加全局变量存储交易记录
let tradeMarkers = [];

// 添加全局变量存储账户价值数据
let accountValues = [];

// 添加全局变量记录统计信息
let dashboardData = {
    initialValue: 0,
    totalProfit: 0,
    totalFee: 0,
    tradeCount: 0,
    lastReason: '--',
    triggerPrice: 0,
    positionStatus: '无仓位',
    accountValue: 0
};

// 初始化图表
function initChart(containerId) {
    chart = echarts.init(document.getElementById(containerId));
    updateChartOption();
}

// 连接WebSocket
function connectWebSocket() {
    if (ws && ws.readyState === WebSocket.OPEN) {
        return;
    }
    
    // 首先加载历史数据
    loadInitialData();
    
    ws = new WebSocket('ws://localhost:28765');
    
    ws.onopen = function() {
        console.log('WebSocket连接已建立');
        showToast('实时数据连接已建立', 'success');
        reconnectAttempts = 0;
    };
    
    ws.onmessage = function(event) {
        try {
            const data = JSON.parse(event.data);
            if (data.type === 'kline') {
                handleKlineData(data.data);
            } else if (data.type === 'trade') {
                handleTradeData(data.data);
            } else if (data.type === 'account_value') {
                handleAccountValue(data.data);
            } else if (data.type === 'dashboard') {
                handleDashboardData(data.data);
            } else if (data.type === 'trade_condition') {
                handleTradeCondition(data.data);
            }
        } catch (error) {
            console.error('处理WebSocket消息时出错:', error);
        }
    };
    
    ws.onclose = function() {
        console.log('WebSocket连接已关闭');
        if (reconnectAttempts < maxReconnectAttempts) {
            reconnectAttempts++;
            const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
            console.log(`${delay/1000}秒后尝试重新连接... (第${reconnectAttempts}次)`);
            showToast(`实时数据连接已断开，${delay/1000}秒后重试`, 'warning');
            setTimeout(connectWebSocket, delay);
        } else {
            showToast('实时数据连接已断开，请刷新页面重试', 'error');
        }
    };
    
    ws.onerror = function(error) {
        console.error('WebSocket错误:', error);
        showToast('实时数据连接出错', 'error');
    };
}

// 添加初始数据加载函数
async function loadInitialData() {
    try {
        // 使用当前北京时间作为结束时间
        const endTime = new Date();
        // 计算2小时前的北京时间
        const startTime = new Date(endTime.getTime() - (2 * 60 * 60 * 1000));
        
        // 将北京时间转换为 UTC 时间字符串（考虑时区偏移）
        const formattedStartTime = new Date(startTime.getTime() - (8 * 60 * 60 * 1000)).toISOString().split('.')[0] + 'Z';
        const formattedEndTime = new Date(endTime.getTime() - (8 * 60 * 60 * 1000)).toISOString().split('.')[0] + 'Z';
        
        console.log('请求历史数据:', {
            beijingStartTime: startTime.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
            beijingEndTime: endTime.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
            utcStartTime: formattedStartTime,
            utcEndTime: formattedEndTime,
            timeDiff: (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60) + '小时'
        });
        
        const response = await fetch(`/api/kline?start_time=${formattedStartTime}&end_time=${formattedEndTime}`);
        const result = await response.json();
        
        if (result.code === 0 && result.data) {
            const { timestamps: newTimestamps, kline: newKline, volumes: newVolumes, trades, price_changes, price_ranges, global_high, global_low } = result.data;
            
            // 确保数据按时间排序
            const sortedData = newTimestamps.map((timestamp, index) => ({
                timestamp,
                kline: newKline[index],
                volume: newVolumes[index]
            })).sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
            
            // 更新全局变量
            timestamps = sortedData.map(item => item.timestamp);
            klineDataArray = sortedData.map(item => item.kline);
            volumes = sortedData.map(item => item.volume);
            
            // 处理交易数据
            if (trades) {
                trades.forEach(trade => {
                    handleTradeData(trade);
                });
            }
            
            // 更新图表
            updateChartOption();
            
            // 设置默认缩放范围到最新数据
            currentDataZoom.start = 80;  // 显示最后20%的数据
            currentDataZoom.end = 100;
            
            console.log('历史数据加载完成:', {
                dataPoints: timestamps.length,
                timeRange: {
                    start: new Date(timestamps[0]).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
                    end: new Date(timestamps[timestamps.length - 1]).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
                    duration: (new Date(timestamps[timestamps.length - 1]) - new Date(timestamps[0])) / (1000 * 60 * 60) + '小时'
                }
            });
        }
    } catch (error) {
        console.error('加载初始数据失败:', error);
        showToast('加载历史数据失败', 'error');
    }
}

// 处理K线数据
function handleKlineData(klineData) {
    if (!chart || !klineData) {
        return;
    }

    // 保存当前提示框状态
    let currentTooltip = null;
    if (chart) {
        try {
            currentTooltip = {
                ...lastTooltipParams,
                position: chart.getOption().tooltip[0].position
            };
        } catch (e) {
            console.log('获取提示框状态失败', e);
        }
    }

    console.log('收到K线数据:', klineData);

    const timestamp = klineData.timestamp;
    const newData = [
        parseFloat(klineData.open),
        parseFloat(klineData.close),
        parseFloat(klineData.low),
        parseFloat(klineData.high)
    ];
    const volume = parseFloat(klineData.volume || 0);
    const volumeCurrency = parseFloat(klineData.volume_currency || 0);
    const isComplete = klineData.is_complete;

    // 保存当前缩放状态
    if (chart) {
        const option = chart.getOption();
        if (option.dataZoom && option.dataZoom.length > 0) {
            currentDataZoom.start = option.dataZoom[0].start;
            currentDataZoom.end = option.dataZoom[0].end;
        }
    }

    // 检查是否是新的时间戳
    const index = timestamps.indexOf(timestamp);
    
    if (index === -1) {
        // 如果是新的时间戳，添加到数组末尾
        timestamps.push(timestamp);
        klineDataArray.push(newData);
        volumes.push(volume);
        
        // 更新最后一个K线数据
        lastKlineData = {
            timestamp: timestamp,
            data: newData,
            volume: volume,
            isComplete: isComplete
        };
    } else {
        // 更新现有K线
        klineDataArray[index] = newData;
        volumes[index] = volume;
        
        // 如果是最后一个K线，更新状态
        if (index === timestamps.length - 1) {
            lastKlineData = {
                timestamp: timestamp,
                data: newData,
                volume: volume,
                isComplete: isComplete
            };
        }
    }

    // 更新账户价值数据
    if (klineData.account_value) {
        const accountValuePoint = {
            timestamp: klineData.timestamp,
            value: parseFloat(klineData.account_value)
        };
        accountValues.push(accountValuePoint);
    }

    // 使用静默更新模式，避免提示框闪烁
    updateChartOption(true);
    
    // 恢复提示框
    if (currentTooltip || lastTooltipParams) {
        const tooltipParams = currentTooltip || lastTooltipParams;
        if (tooltipParams) {
            requestAnimationFrame(() => {
                try {
                    const action = {
                        type: 'showTip',
                        seriesIndex: tooltipParams.seriesIndex,
                        dataIndex: tooltipParams.dataIndex
                    };
                    
                    if (tooltipParams.position) {
                        action.position = tooltipParams.position;
                    }
                    
                    chart.dispatchAction(action);
                } catch (e) {
                    console.error('恢复提示框失败', e);
                }
            });
        }
    }
}

// 修改处理交易数据的函数
function handleTradeData(trade) {
    if (!chart || !trade) return;
    
    // 确保必要字段存在
    if (!trade.timestamp || !trade.price || !trade.amount) {
        console.error('无效的交易数据:', trade);
        return;
    }

    // 精确查找时间索引
    const tradeTime = new Date(trade.timestamp);
    const timeIndex = timestamps.findIndex(ts => {
        const tsTime = new Date(ts);
        return tsTime.getFullYear() === tradeTime.getFullYear() &&
               tsTime.getMonth() === tradeTime.getMonth() &&
               tsTime.getDate() === tradeTime.getDate() &&
               tsTime.getHours() === tradeTime.getHours() &&
               tsTime.getMinutes() === tradeTime.getMinutes();
    });

    if (timeIndex === -1) {
        console.warn('未找到对应时间戳:', trade.timestamp);
        return;
    }

    // 将交易信息添加到标记点数组
    tradeMarkers.push({
        timestamp: trade.timestamp,
        timeIndex: timeIndex,
        action: trade.action,
        position_type: trade.position_type,
        price: parseFloat(trade.price),
        amount: parseFloat(trade.amount),
        reason: trade.reason,
        order_no: trade.live_order_no,
        profit: trade.profit || 0,
        profit_percentage: trade.profit_percentage || '0%',
        fee: trade.fee || 0,
        market_price: trade.market_price || trade.price,
        account_value: trade.account_value || 0,
        last_high: trade.last_high || 0,
        last_low: trade.last_low || 0,
        min_trigger_price: trade.min_trigger_price || 0,
        id: trade.id || '',
        trigger_reason: trade.reason || ''
    });
    
    // 更新账户价值数据
    if (trade.account_value) {
        accountValues.push({
            timestamp: trade.timestamp,
            value: parseFloat(trade.account_value)
        });
    }
    
    // 初始化账户价值
    if(dashboardData.initialValue === 0) {
        dashboardData.initialValue = trade.account_value;
    }
    
    // 更新仪表盘数据
    dashboardData = {
        ...dashboardData,
        triggerPrice: trade.min_trigger_price || 0,
        positionStatus: trade.position_type || '无仓位',
        lastReason: trade.reason || '--',
        accountValue: trade.account_value || 0,
        totalProfit: (dashboardData.totalProfit || 0) + (trade.profit || 0),
        totalFee: (dashboardData.totalFee || 0) + (trade.fee || 0),
        tradeCount: (dashboardData.tradeCount || 0) + 1
    };

    // 广播完整仪表盘数据
    window.dispatchEvent(new CustomEvent('dashboardUpdate', {
        detail: dashboardData
    }));
    
    updateDashboard();
    updateChartOption();
}

// 添加全局事件监听
window.addEventListener('dashboardUpdate', function(e) {
    const data = e.detail;
    updateDashboard(data);
});

// 修改处理账户价值的函数
function handleAccountValue(accountData) {
    if (!chart || !accountData) return;
    
    // 将账户价值数据存入全局变量，确保按时间排序
    const newValue = {
        timestamp: accountData.timestamp,
        value: parseFloat(accountData.account_value)
    };
    
    // 检查是否已存在相同时间戳的数据
    const existingIndex = accountValues.findIndex(item => item.timestamp === accountData.timestamp);
    if (existingIndex !== -1) {
        // 更新现有数据
        accountValues[existingIndex] = newValue;
    } else {
        // 添加新数据
        accountValues.push(newValue);
    }
    
    // 按时间戳排序
    accountValues.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
    
    console.log('收到账户价值数据:', accountData.timestamp, accountData.account_value);
    
    // 限制数组大小，避免内存泄漏
    if (accountValues.length > 1000) {
        accountValues = accountValues.slice(-1000);
    }
    
    // 更新图表
    updateChartOption();
}

// 更新图表配置
function updateChartOption(silent = false) {
    if (!chart) return;

    const upColor = '#26A69A';    // 涨-绿色
    const downColor = '#EF5350';  // 跌-红色
    
    // 获取最新价格和时间戳
    let currentPrice = 0;
    let currentTimestamp = '';
    if (klineDataArray.length > 0) {
        currentPrice = klineDataArray[klineDataArray.length - 1][1]; // 最新收盘价
        currentTimestamp = timestamps[timestamps.length - 1]; // 最新时间戳
    }
    
    // 创建当前价格线的数据
    const priceLineData = timestamps.map((timestamp, index) => {
        if (timestamp === currentTimestamp) {
            return currentPrice;
        }
        return null;
    });
    
    const option = {
        animation: false,
        legend: {
            top: 10,
            left: 'center',
            data: ['K线', '成交量', '交易', '账户价值'],
            textStyle: {
                color: '#666'  // 与index.html的文本颜色一致
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            },
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            borderColor: '#ddd',
            borderWidth: 1,
            padding: 15,
            textStyle: {
                color: '#333',
                fontSize: 12
            },
            formatter: function(params) {
                const klineParam = params.find(p => p.seriesType === 'candlestick');
                
                if (!klineParam) return '';

                const index = klineParam.dataIndex;
                const [open, close, low, high] = klineParam.data;
                const timestamp = timestamps[index];
                const volume = volumes[index] || 0;
                
                // 查找与当前K线时间戳最接近的账户价值
                let accountValue = '--';
                if (accountValues.length > 0) {
                    // 找到时间戳匹配的账户价值
                    const matchingValue = accountValues.find(av => {
                        return av.timestamp === timestamp;
                    });
                    
                    if (matchingValue) {
                        accountValue = matchingValue.value.toFixed(2);
                    } else {
                        // 如果没有精确匹配，使用最新的账户价值
                        accountValue = dashboardData.accountValue.toFixed(2);
                    }
                }
                
                // 计算涨跌幅和震幅
                const priceChange = ((close - open) / open * 100).toFixed(2);
                const priceRange = ((high - low) / low * 100).toFixed(2);
                
                // 获取全局极值
                const globalHigh = Math.max(...klineDataArray.map(d => d[3]));
                const globalLow = Math.min(...klineDataArray.map(d => d[2]));
                const fromHigh = ((globalHigh - close) / globalHigh * 100).toFixed(2);
                const fromLow = ((close - globalLow) / globalLow * 100).toFixed(2);

                // 颜色定义
                const colorStyle = (value, compareValue) => 
                    `style="color: ${value >= compareValue ? '#26A69A' : '#EF5350'}"`;
                
                return `
                    <div style="min-width: 280px;">
                        <div style="font-weight: 500; margin-bottom: 8px; border-bottom: 1px solid #eee; padding-bottom: 5px;">
                            ${timestamp}
                        </div>
                        
                        <div style="margin-bottom: 6px;">
                            <span style="display: inline-block; width: 60px;">开盘</span>
                            <span ${colorStyle(close, open)}>${open.toFixed(5)}</span>
                        </div>
                        
                        <div style="margin-bottom: 6px;">
                            <span style="display: inline-block; width: 60px;">收盘</span>
                            <span ${colorStyle(close, open)}>${close.toFixed(5)}</span>
                            <span style="margin-left: 15px; ${colorStyle(close, open)}">
                                (${priceChange}%)
                            </span>
                        </div>
                        
                        <div style="margin-bottom: 6px;">
                            <span style="display: inline-block; width: 60px;">最高</span>
                            ${high.toFixed(5)}
                        </div>
                        
                        <div style="margin-bottom: 6px;">
                            <span style="display: inline-block; width: 60px;">最低</span>
                            ${low.toFixed(5)}
                            <span style="margin-left: 15px;">(${priceRange}%)</span>
                        </div>
                        
                        <div style="margin-bottom: 6px;">
                            <span style="display: inline-block; width: 60px;">成交量</span>
                            ${volume.toFixed(2)}
                        </div>
                        
                        <div style="margin-bottom: 6px; border-top: 1px solid #eee; padding-top: 8px;">
                            <span style="display: inline-block; width: 80px;">距历史最高</span>
                            <span style="color: #EF5350">-${fromHigh}%</span>
                        </div>
                        
                        <div style="margin-bottom: 6px;">
                            <span style="display: inline-block; width: 80px;">距历史最低</span>
                            <span style="color: #26A69A">+${fromLow}%</span>
                        </div>
                        
                        <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid #eee;">
                            <span style="display: inline-block; width: 60px;">账户价值</span>
                            <span style="color: #FFA500; font-weight: 500;">${accountValue} USDT</span>
                        </div>
                    </div>
                `;
            }
        },
        grid: [{
            left: '10%',
            right: '5%',
            top: '10%',
            height: '60%'
        }, {
            left: '10%',
            right: '5%',
            top: '75%',
            height: '15%'
        }],
        xAxis: [{
            type: 'category',
            data: timestamps,
            boundaryGap: false,
            axisLine: { onZero: false },
            splitLine: { show: false },
            min: 'dataMin',
            max: 'dataMax',
            axisLabel: {
                formatter: function(value) {
                    return value.substring(11, 16);  // 只显示时:分
                }
            }
        }, {
            type: 'category',
            gridIndex: 1,
            data: timestamps,
            boundaryGap: false,
            axisLine: { onZero: false },
            splitLine: { show: false },
            min: 'dataMin',
            max: 'dataMax'
        }],
        yAxis: [{
            scale: true,
            position: 'left',
            gridIndex: 0,
            axisLine: {
                lineStyle: { color: '#666' }
            },
            axisLabel: {
                formatter: (value) => value.toFixed(5) // K线价格保留5位小数
            }
        }, {
            scale: true,
            gridIndex: 1,
            show: false
        }, {
            scale: true,
            position: 'right',
            gridIndex: 0,
            offset: 50,  // 右侧偏移量
            axisLine: {
                lineStyle: { color: '#FFA500' }
            },
            axisLabel: {
                formatter: (value) => value.toFixed(2) // 账户价值保留2位小数
            }
        }],
        dataZoom: [{
            type: 'inside',
            xAxisIndex: [0, 1],  // 同时控制两个x轴
            start: currentDataZoom.start,
            end: currentDataZoom.end
        }, {
            show: true,
            xAxisIndex: [0, 1],
            type: 'slider',
            bottom: '5%',
            start: currentDataZoom.start,
            end: currentDataZoom.end,
            backgroundColor: '#f5f5f5',  // 与index.html的背景色一致
            fillerColor: 'rgba(255,165,0,0.2)'
        }],
        series: [
            {
                name: 'K线',
                type: 'candlestick',
                data: klineDataArray,
                yAxisIndex: 0,
                itemStyle: {
                    color: upColor,
                    color0: downColor,
                    borderColor: upColor,
                    borderColor0: downColor
                },
                markPoint: {
                    data: tradeMarkers.map(trade => {
                        const markers = [];
                        
                        // 添加交易点标记
                        markers.push({
                            name: trade.position_type,
                            coord: [trade.timeIndex, trade.price],
                            value: trade.position_type,
                            itemStyle: {
                                color: {
                                    'long': '#f5222d',
                                    'close_long': '#fa8c16',
                                    'short': '#52c41a',
                                    'close_short': '#1890ff'
                                }[trade.position_type] || '#722ed1'
                            },
                            symbol: 'arrow',
                            symbolRotate: trade.action === 'BUY' ? 90 : -90,
                            symbolSize: [20, 28]
                        });
                        
                        // 在开多和平空时显示最低点
                        if (trade.position_type === 'long' || trade.position_type === 'close_short') {
                            markers.push({
                                name: 'Low',
                                coord: [trade.timeIndex, trade.last_low],
                                value: trade.last_low,
                                symbol: 'circle',
                                symbolSize: 6,
                                itemStyle: {
                                    color: '#FF5252',
                                    opacity: 0.7
                                },
                                label: {
                                    show: true,
                                    position: 'left',
                                    formatter: `L: ${trade.last_low}`,
                                    fontSize: 10,
                                    color: '#FF5252'
                                }
                            });
                        }
                        
                        // 在开空和平多时显示最高点
                        if (trade.position_type === 'short' || trade.position_type === 'close_long') {
                            markers.push({
                                name: 'High',
                                coord: [trade.timeIndex, trade.last_high],
                                value: trade.last_high,
                                symbol: 'circle',
                                symbolSize: 6,
                                itemStyle: {
                                    color: '#2196F3',
                                    opacity: 0.7
                                },
                                label: {
                                    show: true,
                                    position: 'right',
                                    formatter: `H: ${trade.last_high}`,
                                    fontSize: 10,
                                    color: '#2196F3'
                                }
                            });
                        }
                        
                        // 添加触发价格标记
                        if (trade.min_trigger_price) {
                            markers.push({
                                name: 'Trigger',
                                coord: [trade.timeIndex, trade.min_trigger_price],
                                value: trade.min_trigger_price,
                                symbol: 'circle',
                                symbolSize: 6,
                                itemStyle: {
                                    color: '#006400',
                                    opacity: 0.7
                                },
                                label: {
                                    show: true,
                                    position: 'left',
                                    formatter: `T: ${trade.min_trigger_price}`,
                                    fontSize: 10,
                                    color: '#006400'
                                }
                            });
                        }
                        
                        return markers;
                    }).flat()
                }
            },
            {
                name: '成交量',
                type: 'bar',
                xAxisIndex: 1,
                yAxisIndex: 1,
                data: volumes,
                itemStyle: {
                    color: function(params) {
                        const index = params.dataIndex;
                        if (index >= 0 && index < klineDataArray.length) {
                            const [open, close] = klineDataArray[index];
                            return close >= open ? upColor : downColor;
                        }
                        return upColor;
                    }
                }
            },
            {
                name: '账户价值',
                type: 'line',
                yAxisIndex: 2,
                data: accountValues.map(item => [item.timestamp, item.value]),
                lineStyle: {
                    color: '#FFA500',
                    width: 2
                },
                itemStyle: {
                    color: '#FFA500'
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(255,165,0,0.3)' },
                        { offset: 1, color: 'rgba(255,165,0,0.1)' }
                    ])
                }
            }
        ]
    };

    // 使用静默更新模式，避免提示框闪烁
    if (silent) {   
        chart.setOption(option, {
            silent: true,
            replaceMerge: ['series']
        });
    } else {
        chart.setOption(option, true);
    }
}

// 显示提示消息
function showToast(message, type = 'info', duration = 3000) {
    const container = document.getElementById('toastContainer');
    if (!container) return;
    
    const toast = document.createElement('div');
    toast.className = `toast-message ${type}`;
    toast.textContent = message;
    
    container.appendChild(toast);
    setTimeout(() => toast.classList.add('show'), 10);
    
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => container.removeChild(toast), 300);
    }, duration);
}

// 初始化
function init(containerId) {
    initChart(containerId);
    connectWebSocket();
    
    // 处理窗口大小变化
    window.addEventListener('resize', function() {
        if (chart) {
            chart.resize();
        }
    });
    
    // 处理页面关闭
    window.addEventListener('beforeunload', function() {
        if (ws) {
            ws.close();
        }
    });
    
    // 监听缩放事件
    if (chart) {
        chart.on('datazoom', function(params) {
            currentDataZoom.start = params.start;
            currentDataZoom.end = params.end;
        });
        
        // 监听提示框显示事件，使用更可靠的方法
        chart.on('showTip', function(params) {
            lastTooltipParams = {
                seriesIndex: params.seriesIndex,
                dataIndex: params.dataIndex
            };
        });
        
        // 添加点击事件，点击后锁定/解锁提示框
        let tooltipLocked = false;
        chart.on('click', function(params) {
            if (params.componentType === 'series') {
                tooltipLocked = !tooltipLocked;
                if (tooltipLocked) {
                    // 锁定提示框
                    chart.setOption({
                        tooltip: {
                            alwaysShowContent: true
                        }
                    });
                    showToast('提示框已锁定', 'info', 1000);
                } else {
                    // 解锁提示框
                    chart.setOption({
                        tooltip: {
                            alwaysShowContent: false
                        }
                    });
                    showToast('提示框已解锁', 'info', 1000);
                }
            }
        });
    }
}

// 修改样式
const style = document.createElement('style');
style.textContent += `
    .trade-tooltip {
        padding: 10px;
        background: rgba(0, 0, 0, 0.9);
        border-radius: 4px;
        color: white;
        min-width: 200px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
    .trade-header {
        font-weight: bold;
        margin-bottom: 8px;
        padding-bottom: 8px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        font-size: 14px;
    }
    .trade-content {
        font-size: 12px;
        line-height: 1.8;
    }
    .trade-content > div {
        margin: 4px 0;
    }
    
    /* 新增固定提示样式 */
    .echarts-graphic text {
        white-space: pre-wrap;
        line-height: 1.5;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
`;
document.head.appendChild(style);

style.textContent += `
    #tradeConditionContainer {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
    }
    
    .trade-condition-toast {
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 15px;
        border-radius: 6px;
        margin-bottom: 10px;
        min-width: 250px;
        transform: translateX(120%);
        transition: transform 0.3s ease-in-out;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .trade-condition-toast.show {
        transform: translateX(0);
    }
    
    .trade-condition-header {
        font-weight: bold;
        margin-bottom: 8px;
        padding-bottom: 8px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .trade-condition-content {
        font-size: 12px;
        line-height: 1.5;
    }
    
    .trade-long {
        border-left: 4px solid #26A69A;
    }
    
    .trade-short {
        border-left: 4px solid #EF5350;
    }
    
    .close-long {
        border-left: 4px solid #66BB6A;
    }
    
    .close-short {
        border-left: 4px solid #FF7043;
    }
`;
document.head.appendChild(style);

// 修改仪表盘更新函数
function updateDashboard() {
    // 账户价值
    const accountValueElem = document.getElementById('accountValue');
    if (accountValueElem && dashboardData.accountValue !== undefined) {
    accountValueElem.textContent = `$${dashboardData.accountValue.toFixed(2)}`;
    }

    // 仓位状态
    const positionElem = document.getElementById('positionStatus');
    if (positionElem && dashboardData.positionStatus) {
    positionElem.textContent = dashboardData.positionStatus;
    positionElem.style.color = {
        'long': '#f5222d',
        'short': '#52c41a',
        'close_long': '#fa8c16',
        'close_short': '#1890ff'
    }[dashboardData.positionStatus] || '#666';
    }

    // 收益百分比（使用初始化账户价值计算）
    const profitPercentageElem = document.getElementById('profitPercentage');
    if (profitPercentageElem && dashboardData.initialValue > 0 && dashboardData.accountValue !== undefined) {
        const percentage = ((dashboardData.accountValue - dashboardData.initialValue) / dashboardData.initialValue * 100).toFixed(3);
        profitPercentageElem.textContent = `${percentage}%`;
        profitPercentageElem.style.color = percentage >= 0 ? '#52c41a' : '#f5222d';
    }

    // 交易次数
    const tradeCountElem = document.getElementById('tradeCount');
    if (tradeCountElem && dashboardData.tradeCount !== undefined) {
        tradeCountElem.textContent = dashboardData.tradeCount;
    }

    // 总收入
    const totalProfitElem = document.getElementById('totalProfit');
    if (totalProfitElem && dashboardData.totalProfit !== undefined) {
        totalProfitElem.textContent = `$${dashboardData.totalProfit.toFixed(2)}`;
    }

    // 手续费
    const totalFeeElem = document.getElementById('totalFee');
    if (totalFeeElem && dashboardData.totalFee !== undefined) {
        totalFeeElem.textContent = `$${dashboardData.totalFee.toFixed(2)}`;
    }

    // 交易原因（确保更新逻辑）
    const reasonElem = document.getElementById('lastReason');
    if (reasonElem) {
    reasonElem.textContent = dashboardData.lastReason || '--'; // 添加默认值
    reasonElem.style.animation = 'highlight 1s';
}
}

// 修改仪表盘数据处理
function handleDashboardData(data) {
    if (!data) return;
    
    dashboardData = {
        ...dashboardData, // 保留现有数据
        initialValue: dashboardData.initialValue || data.account_value || 0,
        accountValue: data.account_value || 0,
        positionStatus: data.position_status || '无仓位',
        totalProfit: data.total_profit || 0,
        totalFee: data.total_fee || 0,
        tradeCount: data.trade_count || 0
    };

    window.dispatchEvent(new CustomEvent('dashboardUpdate', {
        detail: dashboardData
    }));
}

// 修改交易条件处理
function handleTradeCondition(condition) {
    if (!condition) return;
    
    dashboardData = {
        ...dashboardData, // 保留现有数据
        lastReason: condition.reason || '无原因'
    };

    window.dispatchEvent(new CustomEvent('dashboardUpdate', {
        detail: dashboardData
    }));
}

// 导出函数
window.RealtimeChart = {
    init: init,
    connectWebSocket: connectWebSocket
}; 
