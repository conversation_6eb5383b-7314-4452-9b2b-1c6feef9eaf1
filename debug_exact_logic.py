#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
精确模拟策略分析器的逻辑
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def debug_exact_logic():
    """精确模拟策略分析器的逻辑"""
    
    print("=== 精确模拟策略分析器的逻辑 ===")
    print()
    
    # 创建策略分析器实例
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 获取00:00:00到00:45:00的数据（模拟完整的历史数据）
    try:
        with analyzer.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT timestamp, open_price as open, close_price as close, 
                       high_price as high, low_price as low
                FROM crypto_prices 
                WHERE currency = 'DOGE' 
                AND timestamp >= '2025-06-06 00:00:00' 
                AND timestamp <= '2025-06-06 00:45:00'
                ORDER BY timestamp
            """)
            results = cursor.fetchall()
            
            if not results:
                print("没有找到数据")
                return
                
            print(f"找到 {len(results)} 条数据")
            print()
            
            # 模拟策略分析器的主循环
            minute_candles = []
            
            for i, row in enumerate(results):
                timestamp = row['timestamp']
                open_price = float(row['open'])
                close_price = float(row['close'])
                high_price = float(row['high'])
                low_price = float(row['low'])
                
                # 模拟策略分析器中的涨跌判断逻辑
                if len(minute_candles) > 0:
                    prev_candle = minute_candles[-1]
                    # 上涨：当前最高值和最低值都比上一分钟高
                    if high_price > prev_candle['high'] and low_price > prev_candle['low']:
                        is_up = True
                    # 下跌：当前最高值和最低值都比上一分钟低
                    elif high_price < prev_candle['high'] and low_price < prev_candle['low']:
                        is_up = False
                    else:
                        # 横盘或混合情况，暂时按收盘价判断
                        is_up = close_price > open_price
                else:
                    # 第一条数据，按收盘价判断
                    is_up = close_price > open_price
                
                # 如果是00:42:00，进行开仓分析
                if '00:42:00' in str(timestamp):
                    print(f"\n=== 分析00:42:00的开仓条件 ===")
                    print(f"当前时间: {timestamp}")
                    print(f"历史K线数量: {len(minute_candles)}")
                    print(f"当前K线: 开盘{open_price:.6f}, 收盘{close_price:.6f}, 最高{high_price:.6f}, 最低{low_price:.6f}")
                    print(f"当前涨跌判断: {'涨' if is_up else '跌'}")
                    
                    # 模拟analyze_correct_consecutive_moves函数
                    lookback_buy = 5
                    lookback_sell = 2
                    total_needed = lookback_buy + lookback_sell - 1  # 6分钟
                    
                    if len(minute_candles) >= total_needed:
                        # 创建当前K线数据
                        current_candle = {
                            'timestamp': timestamp,
                            'open': open_price,
                            'close': close_price,
                            'high': high_price,
                            'low': low_price,
                            'is_up': is_up
                        }
                        
                        # 获取历史数据 + 当前数据（模拟策略分析器的逻辑）
                        historical_candles = minute_candles[-total_needed:]
                        all_candles = historical_candles + [current_candle]
                        
                        print(f"\n历史数据（{len(historical_candles)}条）:")
                        for j, candle in enumerate(historical_candles):
                            trend = "涨" if candle['is_up'] else "跌"
                            print(f"  {j+1}. {candle['timestamp']} - {trend}")
                        
                        print(f"\n当前K线:")
                        trend = "涨" if current_candle['is_up'] else "跌"
                        print(f"  {current_candle['timestamp']} - {trend}")
                        
                        print(f"\n完整序列（{len(all_candles)}条）:")
                        for j, candle in enumerate(all_candles):
                            trend = "涨" if candle['is_up'] else "跌"
                            print(f"  {j+1}. {candle['timestamp']} - {trend}")
                        
                        # 检查做多条件：先跌5分钟，再涨2分钟
                        down_period = all_candles[:lookback_buy]  # 前5分钟
                        up_period = all_candles[lookback_buy:]    # 后2分钟（包含当前）
                        
                        print(f"\n前5分钟（下跌期）:")
                        for j, candle in enumerate(down_period):
                            trend = "涨" if candle['is_up'] else "跌"
                            print(f"  {j+1}. {candle['timestamp']} - {trend}")
                        
                        print(f"\n后2分钟（上涨期）:")
                        for j, candle in enumerate(up_period):
                            trend = "涨" if candle['is_up'] else "跌"
                            print(f"  {j+1}. {candle['timestamp']} - {trend}")
                        
                        # 检查前期是否连续下跌
                        all_down_in_down_period = all(not candle['is_up'] for candle in down_period)
                        # 检查后期是否连续上涨
                        all_up_in_up_period = all(candle['is_up'] for candle in up_period)
                        
                        print(f"\n前5分钟连续下跌: {all_down_in_down_period}")
                        print(f"后2分钟连续上涨: {all_up_in_up_period}")
                        print(f"满足做多条件: {all_down_in_down_period and all_up_in_up_period}")
                        
                        if all_down_in_down_period and all_up_in_up_period:
                            print(f"\n✅ 00:42:00 满足开多条件")
                        else:
                            print(f"\n❌ 00:42:00 不满足开多条件")
                    else:
                        print(f"历史数据不足，需要{total_needed}条，实际只有{len(minute_candles)}条")
                
                # 如果是00:43:00，进行平仓分析
                if '00:43:00' in str(timestamp):
                    print(f"\n=== 分析00:43:00的平仓条件 ===")
                    print(f"当前时间: {timestamp}")
                    print(f"历史K线数量: {len(minute_candles)}")
                    print(f"当前K线: 开盘{open_price:.6f}, 收盘{close_price:.6f}, 最高{high_price:.6f}, 最低{low_price:.6f}")
                    print(f"当前涨跌判断: {'涨' if is_up else '跌'}")
                    
                    # 模拟平仓逻辑：连续2分钟下跌（含当前）
                    lookback_sell = 2
                    
                    if len(minute_candles) >= lookback_sell - 1:
                        # 创建当前K线数据
                        current_candle = {
                            'timestamp': timestamp,
                            'open': open_price,
                            'close': close_price,
                            'high': high_price,
                            'low': low_price,
                            'is_up': is_up
                        }
                        
                        # 获取历史数据 + 当前数据（模拟策略分析器的逻辑）
                        historical_candles = minute_candles[-(lookback_sell-1):]
                        recent_moves = historical_candles + [current_candle]
                        
                        print(f"\n最近2分钟数据:")
                        for j, candle in enumerate(recent_moves):
                            trend = "涨" if candle['is_up'] else "跌"
                            print(f"  {j+1}. {candle['timestamp']} - {trend}")
                        
                        # 检查是否连续下跌
                        all_down = all(not candle['is_up'] for candle in recent_moves)
                        
                        print(f"\n连续2分钟下跌: {all_down}")
                        print(f"满足平多条件: {all_down}")
                        
                        if all_down:
                            print(f"\n✅ 00:43:00 满足平多条件")
                        else:
                            print(f"\n❌ 00:43:00 不满足平多条件")
                            
                            # 分析为什么不满足
                            print(f"详细分析:")
                            for j, candle in enumerate(recent_moves):
                                trend = "涨" if candle['is_up'] else "跌"
                                status = "✅" if not candle['is_up'] else "❌"
                                print(f"  {status} {candle['timestamp']} - {trend}")
                    else:
                        print(f"历史数据不足，需要{lookback_sell-1}条历史数据，实际只有{len(minute_candles)}条")
                
                # 添加到历史数据（模拟策略分析器的逻辑：在trade_condition_new之后添加）
                candle_data = {
                    'timestamp': timestamp,
                    'open': open_price,
                    'close': close_price,
                    'high': high_price,
                    'low': low_price,
                    'is_up': is_up
                }
                minute_candles.append(candle_data)
                    
    except Exception as e:
        print(f"调试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_exact_logic()
