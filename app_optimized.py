from flask import Flask, render_template, jsonify, request, Response
import pymysql
from datetime import datetime, timedelta
import logging
import decimal
from config import *
from dateutil.parser import parse
from functools import lru_cache
import pandas as pd
import time
import threading
import os
import traceback
from okx_api_handler import okx_api

# 数据库表名配置
DB_TABLES = {
    'PRICES': 'crypto_prices',
    'STRATEGY_RESULTS': 'strategy_results',
    'STRATEGY_TRADES': 'strategy_trades',
    'POSITION_HISTORY': 'position_history'
}

app = Flask(__name__)
app.config['DEBUG'] = False  # 禁用调试模式以减少CPU占用
app.config['TEMPLATES_AUTO_RELOAD'] = False  # 禁用模板自动重载
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0  # 禁用静态文件缓存

logging.basicConfig(level=logging.INFO)  # 降低日志级别

# 禁用文件监控以减少CPU占用
ENABLE_FILE_MONITORING = False

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/strategy_params', methods=['GET'])
@lru_cache(maxsize=1)
def get_api_strategies():
    """获取所有可用的策略参数组合（优化版本）"""
    try:
        strategies = []
        # 大幅减少策略参数组合数量以降低CPU占用
        # 从原来的 10×10×5×5=2500 个组合减少到 3×3×2×2=36 个组合
        for buy_rate in [1, 3, 5]:  # 0.01, 0.03, 0.05
            for sell_rate in [1, 3, 5]:  # 0.01, 0.03, 0.05
                for lookback_minutes_buy in [2, 3]:  # 2, 3
                    for lookback_minutes_sell in [3, 5]:  # 3, 5
                        strategies.append({
                            'buy_rate': buy_rate / 100,
                            'sell_rate': sell_rate / 100,
                            'lookback_minutes_buy': lookback_minutes_buy,
                            'lookback_minutes_sell': lookback_minutes_sell
                        })
        
        logging.info(f"生成了 {len(strategies)} 个策略组合")
        return jsonify(strategies)
    except Exception as e:
        logging.error(f"策略参数生成错误: {str(e)}")
        return jsonify({
            'success': False,
            'msg': str(e)
        })

@app.route('/health')
def health_check():
    """健康检查端点"""
    return jsonify({
        'status': 'ok',
        'timestamp': datetime.now().isoformat(),
        'file_monitoring': ENABLE_FILE_MONITORING
    })

if __name__ == '__main__':
    logging.info("启动优化版Flask应用")
    logging.info(f"文件监控: {'启用' if ENABLE_FILE_MONITORING else '禁用'}")
    
    # 使用生产级别的配置
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=False,  # 禁用调试模式
        threaded=True,  # 启用多线程
        use_reloader=False  # 禁用自动重载
    )
