# 放大缩小功能测试示例

## 🎯 **期望的行为**

### **放大 (Zoom In) 示例**
```
当前范围: 10% - 90% (80%范围)
中心点: (10 + 90) / 2 = 50%
新范围: 80% × 0.7 = 56%
新开始: 50 - 56/2 = 22%
新结束: 50 + 56/2 = 78%
结果: 22% - 78% ✅ (在当前范围内放大)
```

### **缩小 (Zoom Out) 示例**
```
当前范围: 22% - 78% (56%范围)
中心点: (22 + 78) / 2 = 50%
新范围: 56% × 1.5 = 84%
新开始: 50 - 84/2 = 8%
新结束: 50 + 84/2 = 92%
结果: 8% - 92% ✅ (在当前中心扩展)
```

## 🔍 **具体测试场景**

### **场景1: 从10%-15%范围开始**
```
初始: 10% - 15% (5%范围，中心12.5%)

点击放大:
- 新范围: 5% × 0.7 = 3.5%
- 新开始: 12.5 - 3.5/2 = 10.75%
- 新结束: 12.5 + 3.5/2 = 14.25%
- 结果: 10.75% - 14.25% ✅

点击缩小:
- 新范围: 5% × 1.5 = 7.5%
- 新开始: 12.5 - 7.5/2 = 8.75%
- 新结束: 12.5 + 7.5/2 = 16.25%
- 结果: 8.75% - 16.25% ✅
```

### **场景2: 从当前10%-90%范围开始**
```
初始: 10% - 90% (80%范围，中心50%)

点击放大:
- 新范围: 80% × 0.7 = 56%
- 新开始: 50 - 56/2 = 22%
- 新结束: 50 + 56/2 = 78%
- 结果: 22% - 78% ✅

再次放大:
- 当前: 22% - 78% (56%范围，中心50%)
- 新范围: 56% × 0.7 = 39.2%
- 新开始: 50 - 39.2/2 = 30.4%
- 新结束: 50 + 39.2/2 = 69.6%
- 结果: 30.4% - 69.6% ✅
```

## 📊 **控制台日志示例**

修复后应该看到这样的日志：
```
🔍 放大: 10.0%-90.0% (80.0%) → 22.0%-78.0% (56.0%)
🔍 放大: 22.0%-78.0% (56.0%) → 30.4%-69.6% (39.2%)
🔍 缩小: 30.4%-69.6% (39.2%) → 20.6%-79.4% (58.8%)
```

## 🎮 **测试步骤**

1. **刷新页面** 确保加载最新代码
2. **观察当前范围** 从控制台看到类似 `DataZoom事件: {start: 10, end: 90}`
3. **点击放大按钮** 🔍+
   - 应该看到: `🔍 放大: 10.0%-90.0% (80.0%) → 22.0%-78.0% (56.0%)`
   - 图表应该显示更少的数据，但保持在原来的中心区域
4. **再次点击放大**
   - 应该看到: `🔍 放大: 22.0%-78.0% (56.0%) → 30.4%-69.6% (39.2%)`
   - 继续在当前范围内放大
5. **点击缩小按钮** 🔍-
   - 应该看到: `🔍 缩小: 30.4%-69.6% (39.2%) → 20.6%-79.4% (58.8%)`
   - 图表应该显示更多数据，但仍然以当前中心为基准

## ✅ **预期效果**

### **放大 (🔍+)**
- ✅ 保持当前视图的中心点
- ✅ 减少显示的数据范围 (×0.7)
- ✅ 显示更详细的内容
- ✅ 不会跳到其他地方

### **缩小 (🔍-)**
- ✅ 保持当前视图的中心点
- ✅ 增加显示的数据范围 (×1.5)
- ✅ 显示更多的内容
- ✅ 不会跳到其他地方

## 🔧 **核心逻辑**

```javascript
// 获取当前状态
const currentStart = dataZoom.start;    // 例如: 10
const currentEnd = dataZoom.end;        // 例如: 90
const center = (currentStart + currentEnd) / 2;  // 中心: 50

// 放大: 减少范围，保持中心
const newRange = currentRange * 0.7;    // 80 × 0.7 = 56
const newStart = center - newRange / 2; // 50 - 28 = 22
const newEnd = center + newRange / 2;   // 50 + 28 = 78

// 结果: 22% - 78% (在原来10%-90%的中心区域)
```

这样就确保了放大缩小始终在当前范围内进行，不会跳到其他地方！🎯
