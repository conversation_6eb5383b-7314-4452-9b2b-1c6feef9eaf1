#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试当前K线参与连续涨跌判断的逻辑
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_analyzer import StrategyAnalyzer
from db_config import DB_CONFIG

def test_current_candle_logic():
    """测试当前K线参与连续涨跌判断的逻辑"""
    
    print("=== 测试当前K线参与连续涨跌判断的逻辑 ===")
    print()
    
    # 创建策略分析器实例
    analyzer = StrategyAnalyzer(DB_CONFIG)
    
    # 模拟历史K线数据
    minute_candles = [
        {'timestamp': '2025-06-06T10:00:00', 'is_up': False},  # 跌
        {'timestamp': '2025-06-06T10:01:00', 'is_up': False},  # 跌
        {'timestamp': '2025-06-06T10:02:00', 'is_up': False},  # 跌
        {'timestamp': '2025-06-06T10:03:00', 'is_up': False},  # 跌
        {'timestamp': '2025-06-06T10:04:00', 'is_up': False},  # 跌 (连续5次下跌)
        {'timestamp': '2025-06-06T10:05:00', 'is_up': True},   # 涨
    ]
    
    # 模拟当前K线数据（未完成）
    current_row = {
        'timestamp': '2025-06-06T10:06:00',
        'open': '0.1800',
        'close': '0.1810',  # 收盘价 > 开盘价，表示上涨
        'high': '0.1815',
        'low': '0.1795',
        'is_complete': 0  # 未完成的K线
    }
    
    print("历史K线数据（完整的分钟K线）：")
    for i, candle in enumerate(minute_candles):
        direction = "涨" if candle['is_up'] else "跌"
        print(f"  {i+1}. {candle['timestamp']} - {direction}")
    
    print(f"\n当前K线数据（未完成）：")
    print(f"  {current_row['timestamp']} - 涨（开盘: {current_row['open']}, 收盘: {current_row['close']}）")
    
    print(f"\n分析结果：")
    print(f"- 历史：连续5次下跌 + 1次上涨")
    print(f"- 当前：1次上涨（未完成）")
    print(f"- 总计：先跌5次，再涨2次（包含当前）")
    print(f"- 符合做多条件：lookback-minutes-buy=5, lookback-minutes-sell=2")
    
    # 测试连续涨跌分析函数
    def analyze_consecutive_moves(candles, current_row, lookback_sell, lookback_buy):
        """
        复制策略分析器中的逻辑进行测试
        """
        # 创建包含当前K线的完整数据
        current_candle = {
            'timestamp': current_row['timestamp'],
            'open': float(current_row['open']),
            'close': float(current_row['close']),
            'high': float(current_row['high']),
            'low': float(current_row['low']),
            'is_up': float(current_row['close']) > float(current_row['open'])
        }
        
        # 将当前K线添加到历史数据中进行分析
        all_candles = candles + [current_candle]
        
        if len(all_candles) < lookback_sell + lookback_buy:
            return False, False, -1, -1
        
        # 倒序分析：先看最近的lookback_sell次（包含当前K线），再看之前的lookback_buy次
        recent_moves = all_candles[-lookback_sell:]  # 最近的sell次数（包含当前）
        previous_moves = all_candles[-(lookback_sell + lookback_buy):-lookback_sell]  # 之前的buy次数
        
        print(f"\n详细分析过程：")
        print(f"- 总K线数: {len(all_candles)}")
        print(f"- 需要分析: 先{lookback_buy}次 + 后{lookback_sell}次")
        
        print(f"- 之前{lookback_buy}次K线:")
        for i, candle in enumerate(previous_moves):
            direction = "涨" if candle['is_up'] else "跌"
            print(f"    {i+1}. {candle['timestamp']} - {direction}")
        
        print(f"- 最近{lookback_sell}次K线（含当前）:")
        for i, candle in enumerate(recent_moves):
            direction = "涨" if candle['is_up'] else "跌"
            status = "（当前）" if candle['timestamp'] == current_row['timestamp'] else ""
            print(f"    {i+1}. {candle['timestamp']} - {direction} {status}")
        
        # 检查最近是否连续上涨
        recent_all_up = all(candle['is_up'] for candle in recent_moves)
        # 检查之前是否连续下跌
        previous_all_down = all(not candle['is_up'] for candle in previous_moves)
        
        print(f"\n判断结果：")
        print(f"- 之前{lookback_buy}次是否全部下跌: {previous_all_down}")
        print(f"- 最近{lookback_sell}次是否全部上涨: {recent_all_up}")
        print(f"- 满足做多条件: {recent_all_up and previous_all_down}")
        
        if recent_all_up and previous_all_down:
            return True, False, -1, -1
        
        return False, False, -1, -1
    
    # 测试不同的参数组合
    test_cases = [
        (2, 5, "先跌5次，再涨2次"),
        (1, 5, "先跌5次，再涨1次"),
        (3, 5, "先跌5次，再涨3次"),
        (2, 3, "先跌3次，再涨2次"),
    ]
    
    for lookback_sell, lookback_buy, description in test_cases:
        print(f"\n" + "="*60)
        print(f"测试案例: {description}")
        print(f"参数: lookback-minutes-buy={lookback_buy}, lookback-minutes-sell={lookback_sell}")
        
        can_buy_long, can_sell_short, _, _ = analyze_consecutive_moves(
            minute_candles, current_row, lookback_sell, lookback_buy
        )
        
        result = "✅ 满足做多条件" if can_buy_long else "❌ 不满足做多条件"
        print(f"最终结果: {result}")

if __name__ == "__main__":
    test_current_candle_logic()
