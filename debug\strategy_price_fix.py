#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略价格计算修复方案
解决交易价格超出K线范围的问题
"""

def fix_trigger_price_calculation():
    """
    修复触发价格计算逻辑的建议方案
    """
    
    print("🔧 策略价格计算修复方案")
    print("=" * 50)
    
    print("\n❌ 当前问题:")
    print("1. 触发价格基于历史K线计算 (如02:15:00的最高K线)")
    print("2. 交易执行时未验证价格是否在当前K线范围内")
    print("3. 导致交易价格0.19996超出当前K线范围[0.19760, 0.19846]")
    
    print("\n🎯 问题根源:")
    print("在 _check_close_long_position 方法中:")
    print("- 第1074行: trigger_price = highest_candle['high'] - (price_range * sell_rate)")
    print("- highest_candle 来自历史K线，不是当前K线")
    print("- 第1098行: 返回的 trigger_price 可能超出当前K线范围")
    
    print("\n✅ 修复方案:")
    print("1. 强制验证所有交易价格必须在当前K线范围内")
    print("2. 如果计算出的价格超出范围，使用当前K线的边界价格")
    print("3. 添加详细的调试日志记录价格调整过程")
    
    print("\n🔧 具体修复代码:")
    
    # 修复方案1: 强制价格范围验证
    fix_code_1 = '''
def ensure_price_in_range(price, current_low, current_high, price_type="触发价"):
    """确保价格在当前K线范围内"""
    if price < current_low:
        print_debug(f"  ⚠️ {price_type}{price:.5f} < 最低价{current_low:.5f}，调整为最低价")
        return current_low
    elif price > current_high:
        print_debug(f"  ⚠️ {price_type}{price:.5f} > 最高价{current_high:.5f}，调整为最高价")
        return current_high
    else:
        print_debug(f"  ✅ {price_type}{price:.5f}在K线范围内")
        return price
'''
    
    # 修复方案2: 修改返回逻辑
    fix_code_2 = '''
# 在 _check_close_long_position 方法的返回部分添加验证
current_low = candles[-1]['low']
current_high = candles[-1]['high']

# 确保 actual_trigger_price 在当前K线范围内
safe_trigger_price = ensure_price_in_range(
    actual_trigger_price, current_low, current_high, "实际触发价"
)

return {
    'action': '平多',
    'reason': f'连续{lookback_buy}分钟下跌，价格跌破触发点{safe_trigger_price:.4f}',
    'trigger_price': safe_trigger_price,  # 使用安全的价格
    'min_trigger_price': 0
}
'''
    
    print(fix_code_1)
    print(fix_code_2)
    
    print("\n📝 需要修改的文件位置:")
    print("文件: strategy_analyzer.py")
    print("方法: _check_close_long_position")
    print("行号: 1095-1100 (返回语句)")
    print("同样需要修改: _check_close_short_position, _check_open_position 等方法")
    
    print("\n🧪 测试验证:")
    print("1. 使用问题时间点 2025-03-01 02:19:00 进行测试")
    print("2. 验证所有交易价格都在K线范围内")
    print("3. 检查调试日志确认价格调整过程")
    
    return {
        'fix_function': fix_code_1,
        'fix_return': fix_code_2,
        'test_case': {
            'timestamp': '2025-03-01 02:19:00',
            'kline_range': [0.19760, 0.19846],
            'original_trigger': 0.19996,
            'expected_trigger': 0.19846  # 应该被调整为最高价
        }
    }

def create_price_validation_patch():
    """创建价格验证补丁代码"""
    
    patch_code = '''
# 在 strategy_analyzer.py 中添加价格验证函数

def ensure_price_in_range(self, price, current_low, current_high, price_type="价格"):
    """
    确保价格在当前K线范围内
    
    Args:
        price: 要验证的价格
        current_low: 当前K线最低价
        current_high: 当前K线最高价
        price_type: 价格类型描述（用于日志）
    
    Returns:
        调整后的安全价格
    """
    if price < current_low:
        print_debug(f"  ⚠️ {price_type}{price:.5f} < 最低价{current_low:.5f}，调整为最低价")
        return current_low
    elif price > current_high:
        print_debug(f"  ⚠️ {price_type}{price:.5f} > 最高价{current_high:.5f}，调整为最高价")
        return current_high
    else:
        print_debug(f"  ✅ {price_type}{price:.5f}在K线范围[{current_low:.5f}, {current_high:.5f}]内")
        return price

# 修改所有返回交易决策的地方，添加价格验证
# 例如在 _check_close_long_position 方法中:

# 原代码:
return {
    'action': '平多',
    'reason': f'连续{lookback_buy}分钟下跌，价格跌破触发点{actual_trigger_price:.4f}',
    'trigger_price': actual_trigger_price,
    'min_trigger_price': 0
}

# 修改为:
safe_trigger_price = self.ensure_price_in_range(
    actual_trigger_price, current_low, current_high, "实际触发价"
)

return {
    'action': '平多',
    'reason': f'连续{lookback_buy}分钟下跌，价格跌破触发点{safe_trigger_price:.4f}',
    'trigger_price': safe_trigger_price,
    'min_trigger_price': 0
}
'''
    
    return patch_code

if __name__ == "__main__":
    # 执行修复分析
    fix_info = fix_trigger_price_calculation()
    
    print("\n" + "="*50)
    print("📋 修复总结:")
    print("1. 添加 ensure_price_in_range 函数进行价格验证")
    print("2. 修改所有交易决策返回点，确保价格在K线范围内")
    print("3. 增强调试日志，记录价格调整过程")
    print("4. 测试验证修复效果")
    
    # 生成补丁代码
    patch = create_price_validation_patch()
    print(f"\n📄 补丁代码已生成，可以应用到 strategy_analyzer.py")
    
    print(f"\n🎯 预期结果:")
    print(f"- 原始触发价: {fix_info['test_case']['original_trigger']}")
    print(f"- K线范围: {fix_info['test_case']['kline_range']}")
    print(f"- 调整后价格: {fix_info['test_case']['expected_trigger']}")
    print(f"- 不再超出K线范围 ✅")
