# 智能交易数据加载功能

## 功能概述

实现了基于视图范围的智能交易数据加载机制，避免在大时间范围内加载庞大的交易数据导致页面卡顿。

## 核心特性

### 1. 智能范围检测
- **小范围加载**：当浏览范围 ≤ 24小时时，自动加载交易数据
- **大范围清除**：当浏览范围 > 24小时时，自动清除交易数据
- **实时监控**：监听图表的 `dataZoom` 事件，实时响应范围变化

### 2. 自动加载机制
- **移动自动加载**：移动图表范围时自动检查并加载对应的交易数据
- **防重复加载**：智能检测已加载的时间范围，避免重复请求
- **延时处理**：使用300ms延时避免频繁触发

### 3. 状态指示器
- **实时状态显示**：在控制面板显示当前交易数据加载状态
- **图标指示**：使用不同图标表示不同状态
- **颜色编码**：不同状态使用不同颜色

## 状态说明

| 状态 | 图标 | 颜色 | 说明 |
|------|------|------|------|
| 未加载 | 📊 | 灰色 | 初始状态或无数据 |
| 检查中 | 📊 | 灰色 | 正在检查视图范围 |
| 加载中 | ⏳ | 橙色 | 正在加载交易数据 |
| 已加载 | ✅ | 绿色 | 交易数据加载成功 |
| 无数据 | 📊 | 灰色 | 当前范围内无交易数据 |
| 已禁用 | 🚫 | 灰色 | 交易数据加载开关已关闭 |
| 已清除 | 📊 | 灰色 | 范围过大，已清除数据 |
| 加载失败 | ❌ | 红色 | 加载过程中出现错误 |

## 工作流程

### 1. 初始化
```javascript
// 图表创建后自动检查
setTimeout(() => {
    checkAndLoadTradeData();
}, 1000);
```

### 2. 范围变化监听
```javascript
// 监听dataZoom事件
chart.on('dataZoom', function(params) {
    handleDataZoomChange(params);
});
```

### 3. 智能判断逻辑
```javascript
function checkAndLoadTradeData() {
    const currentRange = getCurrentVisibleTimeRange();
    const { durationHours } = currentRange;
    
    if (durationHours <= 24) {
        loadTradeDataForRange(startTime, endTime);
    } else {
        clearTradeDataFromChart();
    }
}
```

## 性能优化

### 1. 防抖处理
- 使用300ms延时避免频繁触发
- 清除之前的延时任务

### 2. 重复检测
- 记录当前加载的时间范围
- 避免重复加载相同范围的数据

### 3. 条件加载
- 检查交易数据开关状态
- 检查策略ID是否存在

## 用户体验

### 1. 无感知切换
- 用户移动图表时自动处理
- 无需手动操作

### 2. 状态反馈
- 实时显示加载状态
- 提供操作反馈

### 3. 性能保护
- 大范围时自动清除数据
- 避免页面卡顿

## 技术实现

### 1. 核心函数

#### `handleDataZoomChange(params)`
- 处理dataZoom事件
- 实现防抖机制

#### `checkAndLoadTradeData()`
- 检查当前视图范围
- 决定加载或清除数据

#### `getCurrentVisibleTimeRange()`
- 计算当前可见的时间范围
- 返回开始时间、结束时间和持续时间

#### `loadTradeDataForRange(startTime, endTime)`
- 为指定时间范围加载交易数据
- 更新图表显示

#### `clearTradeDataFromChart()`
- 清除图表中的交易数据
- 释放内存

#### `updateTradeDataStatus(message, type)`
- 更新状态指示器
- 提供视觉反馈

### 2. 数据结构

```javascript
// 当前加载范围记录
currentTradeDataRange = {
    startTime: "2024-01-01 00:00:00",
    endTime: "2024-01-01 23:59:59"
};

// 延时任务ID
tradeDataLoadTimeout = null;
```

## 配置选项

### 1. 时间阈值
```javascript
// 24小时阈值，可根据需要调整
if (durationHours <= 24) {
    // 加载数据
}
```

### 2. 延时时间
```javascript
// 300ms防抖延时，可根据需要调整
tradeDataLoadTimeout = setTimeout(() => {
    checkAndLoadTradeData();
}, 300);
```

## 使用说明

### 1. 启用功能
- 确保"加载交易数据"开关已开启
- 选择有效的策略ID

### 2. 观察状态
- 查看控制面板中的状态指示器
- 注意状态变化和颜色

### 3. 操作建议
- 小范围浏览时可以看到详细的交易标记
- 大范围浏览时专注于K线走势
- 需要查看交易详情时缩小到24小时内

## 故障排除

### 1. 数据不加载
- 检查交易数据开关是否开启
- 确认策略ID是否有效
- 查看浏览器控制台错误信息

### 2. 状态显示异常
- 刷新页面重新初始化
- 检查网络连接

### 3. 性能问题
- 确保大范围时数据已清除
- 避免频繁快速移动图表

## 未来扩展

1. **可配置阈值**：允许用户自定义24小时阈值
2. **分级加载**：根据范围大小加载不同详细程度的数据
3. **缓存机制**：缓存已加载的数据段
4. **预加载**：预测用户可能查看的范围并预加载
