from importlib.resources import open_binary
import pymysql
from datetime import datetime, timedelta, time
import time as time_module  # 重命名为time_module以避免冲突
from config import *
from okx_api_handler import okx_api

def create_database_and_table():
    """创建数据库和表"""
    conn = pymysql.connect(
        host=MYSQL_HOST,
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        port=MYSQL_PORT
    )
    cursor = conn.cursor()

    try:
        # 创建数据库
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {MYSQL_DATABASE}")
        cursor.execute(f"USE {MYSQL_DATABASE}")

        # 创建表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS crypto_prices (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            currency VARCHAR(20) NOT NULL,
            timestamp DATETIME NOT NULL,
            open_price DECIMAL(20, 8) NOT NULL,
            high_price DECIMAL(20, 8) NOT NULL,
            low_price DECIMAL(20, 8) NOT NULL,
            close_price DECIMAL(20, 8) NOT NULL,
            volume DECIMAL(20, 8) NOT NULL,
            volume_currency DECIMAL(20, 8) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_price (currency, timestamp)
        )
        """)
        conn.commit()
        print("数据库和表创建完成")

    except Exception as e:
        print(f"创建数据库和表时发生错误: {str(e)}")
        conn.rollback()
    finally:
        conn.close()

def get_latest_record(cursor, currency):
    """获取最新的记录时间"""
    cursor.execute("""
        SELECT MAX(timestamp) as latest_time
        FROM crypto_prices
        WHERE currency = %s
    """, (currency,))
    result = cursor.fetchone()
    return result[0] if result and result[0] else None

def get_earliest_record(cursor, currency):
    """获取最早的记录时间"""
    cursor.execute("""
        SELECT MIN(timestamp) as earliest_time
        FROM crypto_prices
        WHERE currency = %s
    """, (currency,))
    result = cursor.fetchone()
    return result[0] if result and result[0] else None

def find_data_gaps(cursor, currency, interval_minutes=1):
    """查找数据中的空缺"""
    # 首先获取所有时间戳
    cursor.execute("""
        SELECT timestamp 
        FROM crypto_prices 
        WHERE currency = %s 
        ORDER BY timestamp
    """, (currency,))
    
    timestamps = [row[0] for row in cursor.fetchall()]
    gaps = []
    
    # 在Python中处理时间间隔检查
    for i in range(len(timestamps) - 1):
        current_time = timestamps[i]
        next_time = timestamps[i + 1]
        time_diff = (next_time - current_time).total_seconds() / 60
        
        if time_diff > interval_minutes:
            # 生成缺失的时间点
            while current_time < next_time:
                current_time = current_time + timedelta(minutes=interval_minutes)
                if current_time < next_time:
                    gaps.append(current_time)
                if len(gaps) >= 1000:
                    break
            if len(gaps) >= 1000:
                break
    
    return gaps[:1000]

def check_daily_data_completeness(cursor, currency):
    """检查每天数据的完整性"""
    cursor.execute("""
        SELECT 
            DATE(timestamp) as date,
            COUNT(*) as count,
            MIN(timestamp) as min_time,
            MAX(timestamp) as max_time,
            COUNT(DISTINCT HOUR(timestamp)) as hours_count,
            COUNT(DISTINCT MINUTE(timestamp)) as minutes_count
        FROM crypto_prices 
        WHERE currency = %s 
        GROUP BY DATE(timestamp)
        ORDER BY date
    """, (currency,))
    
    return cursor.fetchall()

def get_day_gaps(cursor, currency, date):
    """获取指定日期的数据缺口"""
    current_time = datetime.now()
    
    # 如果是未来日期，直接返回空列表
    if isinstance(date, datetime):
        date_to_check = date.date()
    else:
        date_to_check = date
        
    if date_to_check > current_time.date():
        print(f"跳过未来日期 {date}")
        return []
        
    # 首先获取当天所有时间点
    cursor.execute("""
        SELECT 
            timestamp,
            LEAD(timestamp) OVER (ORDER BY timestamp) as next_timestamp
        FROM crypto_prices 
        WHERE currency = %s 
        AND DATE(timestamp) = %s
        ORDER BY timestamp
    """, (currency, date_to_check))
    
    records = cursor.fetchall()
    gaps = []
    
    if not records:
        # 如果当天没有数据，返回整天作为缺口（但不超过当前时间）
        if isinstance(date, datetime):
            day_start = date
        else:
            day_start = datetime.combine(date, time.min)
            
        day_end = datetime.combine(date_to_check, time.max)
        if date_to_check == current_time.date():
            day_end = current_time
        gaps.append((day_start, min(day_end, current_time)))
        return gaps
    
    # 检查一天开始的缺口
    if isinstance(date, datetime):
        day_start = date
    else:
        day_start = datetime.combine(date, time.min)
        
    if records[0][0] - day_start > timedelta(minutes=1):
        gaps.append((day_start, records[0][0]))
    
    # 检查中间的缺口
    for i in range(len(records)-1):
        current, next_time = records[i][0], records[i][1]
        if next_time and (next_time - current) > timedelta(minutes=1):
            gaps.append((current, next_time))
    
    # 检查一天结束的缺口
    day_end = datetime.combine(date_to_check, time.max)
    if date_to_check == current_time.date():
        day_end = current_time
    
    if records[-1][0] + timedelta(minutes=1) < day_end:
        gaps.append((records[-1][0], day_end))
    
    return gaps

def get_crypto_prices(currency_pair='DOGE-USDT', start_time=None, end_time=None, bar='1m'):
    """获取加密货币价格数据"""
    currency = currency_pair.split('-')[0]
    print(f"开始获取 {currency} 价格数据...")

    # 设置默认时间范围
    current_time = datetime.now()
    current_timestamp = int(current_time.timestamp())
    
    if end_time is None:
        end_time = current_timestamp
    else:
        if isinstance(end_time, datetime):
            end_time = int(end_time.timestamp())
        end_time = min(end_time, current_timestamp)
        
    if start_time is None:
        start_time = end_time - (24 * 60 * 60)
    else:
        if isinstance(start_time, datetime):
            start_time = int(start_time.timestamp())
    
    start_datetime = datetime.fromtimestamp(start_time)
    end_datetime = datetime.fromtimestamp(end_time)
        
    print(f"计划获取时间范围: {start_datetime} 至 {end_datetime}")

    conn = None
    cursor = None
    
    try:
        conn = pymysql.connect(
            host=MYSQL_HOST,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=MYSQL_DATABASE,
            port=MYSQL_PORT
        )
        cursor = conn.cursor()

        # 检查整个时间段的数据
        cursor.execute("""
            SELECT timestamp 
            FROM crypto_prices 
            WHERE currency = %s 
            AND timestamp BETWEEN %s AND %s
            ORDER BY timestamp
        """, (currency, start_datetime, end_datetime))
        
        records = cursor.fetchall()
        gaps = []
        
        if not records:
            # 如果没有数据，整个时间段都是缺口
            gaps.append((start_datetime, end_datetime))
        else:
            # 检查开始时间的缺口
            if start_datetime + timedelta(minutes=1) < records[0][0]:
                gaps.append((start_datetime, records[0][0]))
            
            # 检查中间的缺口
            for i in range(len(records)-1):
                current = records[i][0]
                next_time = records[i+1][0]
                if (next_time - current).total_seconds() > 60:  # 如果间隔超过1分钟
                    gaps.append((current, next_time))
            
            # 检查结束时间的缺口
            if records[-1][0] + timedelta(minutes=1) < end_datetime:
                gaps.append((records[-1][0], end_datetime))
        
        if gaps:
            print(f"\n发现 {len(gaps)} 个时间缺口:")
            for gap_start, gap_end in gaps:
                gap_duration = gap_end - gap_start
                hours = gap_duration.total_seconds() / 3600
                print(f"缺口: {gap_start} 至 {gap_end} (约 {hours:.1f} 小时)")
                
                # 将大的时间缺口分成小块处理
                current_start = gap_start
                while current_start < gap_end:
                    current_end = min(current_start + timedelta(hours=4), gap_end)
                    print(f"\n填补缺口: {current_start.strftime('%Y-%m-%d %H:%M')} 至 "
                          f"{current_end.strftime('%Y-%m-%d %H:%M')}")
                    try:
                        save_kline_data(cursor, conn, currency_pair, currency,
                                      int(current_start.timestamp()), 
                                      int(current_end.timestamp()), bar)
                        time_module.sleep(1)  # 添加短暂延迟，避免请求过于频繁
                    except Exception as e:
                        print(f"填补缺口时出错: {str(e)}")
                    current_start = current_end

        # 显示数据完整性报告
        print("\n数据完整性报告:")
        cursor.execute("""
            SELECT 
                DATE(timestamp) as date,
                COUNT(*) as count,
                MIN(timestamp) as min_time,
                MAX(timestamp) as max_time
            FROM crypto_prices 
            WHERE currency = %s 
            AND timestamp BETWEEN %s AND %s
            GROUP BY DATE(timestamp)
            ORDER BY date
        """, (currency, start_datetime, end_datetime))
        
        daily_stats = cursor.fetchall()
        total_minutes = 0
        missing_minutes = 0
        
        for date, count, min_time, max_time in daily_stats:
            expected_count = 24 * 60  # 一天应该有1440条分钟数据
            if date == start_datetime.date():
                # 对于开始日期，只计算从开始时间到天结束
                minutes_in_day = (datetime.combine(date, time.max) - 
                                start_datetime).total_seconds() / 60
                expected_count = int(minutes_in_day) + 1
            elif date == end_datetime.date():
                # 对于结束日期，只计算从天开始到结束时间
                minutes_in_day = (end_datetime - 
                                datetime.combine(date, time.min)).total_seconds() / 60
                expected_count = int(minutes_in_day) + 1
            
            total_minutes += expected_count
            current_missing = expected_count - count
            missing_minutes += current_missing
            
            completion_rate = (count / expected_count) * 100
            print(f"日期 {date}: {count}/{expected_count} 条数据 "
                  f"({completion_rate:.2f}% 完整) "
                  f"[时间范围: {min_time.strftime('%H:%M')} - {max_time.strftime('%H:%M')}]")
        
        if total_minutes > 0:
            overall_completion = ((total_minutes - missing_minutes) / total_minutes) * 100
            print(f"\n总体数据完整性: {overall_completion:.2f}%")
        
        print("\n数据获取完成")

    except Exception as e:
        print(f"获取数据时发生错误: {str(e)} {traceback.format_exc()}")
        if conn:
            conn.rollback()
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def save_kline_data(cursor, conn, currency_pair, currency, start_time, end_time, bar):
    """保存K线数据到数据库"""
    # 确保不获取未来数据
    current_time = datetime.now()
    end_time = min(end_time, int(current_time.timestamp()))
    
    if start_time >= end_time:
        print("开始时间大于或等于结束时间，跳过数据获取")
        return
        
    if datetime.fromtimestamp(start_time) > current_time:
        print("开始时间在未来，跳过数据获取")
        return
        
    batch_values = []
    batch_size = 1000  # 数据库批量插入大小
    total_records = 0
    max_retries = 3
    retry_delay = 5  # 秒
    
    # 计算时间范围
    time_range = end_time - start_time
    # 由于API每次最多返回100条数据，对于1分钟K线，每次最多获取100分钟的数据
    block_size = 100 * 60  # 100分钟
    
    current_start = start_time
    while current_start < end_time:
        current_end = min(current_start + block_size, end_time)
        
        try:
            retry_count = 0
            while retry_count < max_retries:
                try:
                    # 使用新的API处理器获取数据
                    result = okx_api.get_kline_data(
                        currency_pair=currency_pair,
                        start_time=datetime.fromtimestamp(current_start),
                        end_time=datetime.fromtimestamp(current_end),
                        bar=bar
                    )

                    if result['code'] != 0:
                        print(f"获取数据失败: {result['message']}")
                        retry_count += 1
                        if retry_count < max_retries:
                            print(f"等待 {retry_delay} 秒后重试...")
                            time_module.sleep(retry_delay)
                            continue
                        break

                    if not result.get('data'):
                        print(f"时间段 {datetime.fromtimestamp(current_start)} 至 {datetime.fromtimestamp(current_end)} 未获取到数据")
                        break
                    currency=currency.split('-')[0]
                    for candle in result['data']:
                        timestamp = datetime.strptime(candle['timestamp'], '%Y-%m-%d %H:%M:%S')
                        
                        # 只处理指定时间范围内的数据
                        if timestamp.timestamp() < current_start or timestamp.timestamp() > current_end:
                            continue
                            
                        batch_values.append((
                            currency,
                            timestamp,
                            candle['open'],
                            candle['high'],
                            candle['low'],
                            candle['close'],
                            candle['volume'],
                            candle['volume_currency']
                        ))

                        if len(batch_values) >= batch_size:
                            try:
                                sql = """
                                INSERT IGNORE INTO crypto_prices 
                                (currency, timestamp, open_price, high_price, low_price, close_price, volume, volume_currency)
                                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                                """
                                cursor.executemany(sql, batch_values)
                                conn.commit()
                                total_records += len(batch_values)
                                print(f"已保存 {len(batch_values)} 条记录，总计: {total_records} 条")
                                batch_values = []
                            except Exception as e:
                                print(f"保存批量数据时出错: {str(e)}")
                                conn.rollback()
                                raise

                    break  # 成功获取数据，跳出重试循环

                except Exception as e:
                    print(f"第 {retry_count + 1} 次尝试失败: {str(e)}")
                    retry_count += 1
                    if retry_count < max_retries:
                        print(f"等待 {retry_delay} 秒后重试...")
                        time_module.sleep(retry_delay)
                    else:
                        print("达到最大重试次数，放弃获取数据")
                        raise

        except Exception as e:
            print(f"处理时间段 {datetime.fromtimestamp(current_start)} 至 {datetime.fromtimestamp(current_end)} 时出错: {str(e)}")
        
        current_start = current_end
        time_module.sleep(0.5)  # 在获取下一个时间段之前稍作延迟

    # 处理剩余的数据
    if batch_values:
        try:
            sql = """
            INSERT IGNORE INTO crypto_prices 
            (currency, timestamp, open_price, high_price, low_price, close_price, volume, volume_currency)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            cursor.executemany(sql, batch_values)
            conn.commit()
            total_records += len(batch_values)
            print(f"最后保存 {len(batch_values)} 条记录，总计: {total_records} 条")
        except Exception as e:
            print(f"保存剩余数据时出错: {str(e)}")
            conn.rollback()
            raise
if __name__ == "__main__":
    create_database_and_table()
    
    # 获取从2023年8月1日到现在的数据
    start_date = datetime(2025, 3, 1)  # 修改为正确的日期
    
    # 定义要获取的币种列表
    currency_pairs = [
        'DOGE-USDT-SWAP',
        'SHIB-USDT-SWAP',
        'BTC-USDT-SWAP',
        # 'ETH-USDT',
    ]
    
    # 遍历获取每个币种的数据
    for pair in currency_pairs:
        print(f"\n开始获取 {pair} 的数据...")
        get_crypto_prices(pair, start_time=start_date)
        print(f"{pair} 数据获取完成\n")
        time_module.sleep(1)  # 在获取不同币种之间添加延迟
